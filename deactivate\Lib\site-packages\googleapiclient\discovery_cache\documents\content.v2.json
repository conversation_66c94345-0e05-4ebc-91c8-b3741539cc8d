{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/content": {"description": "Manage your product listings and accounts for Google Shopping"}}}}, "basePath": "/content/v2/", "baseUrl": "https://shoppingcontent.googleapis.com/content/v2/", "batchPath": "batch", "canonicalName": "Shopping Content", "description": "Manage your product listings and accounts for Google Shopping", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/shopping-content/v2/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "content:v2", "kind": "discovery#restDescription", "mtlsRootUrl": "https://shoppingcontent.mtls.googleapis.com/", "name": "content", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"accounts": {"methods": {"authinfo": {"description": "Returns information about the authenticated user.", "flatPath": "accounts/authinfo", "httpMethod": "GET", "id": "content.accounts.authinfo", "parameterOrder": [], "parameters": {}, "path": "accounts/authinfo", "response": {"$ref": "AccountsAuthInfoResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "claimwebsite": {"description": "Claims the website of a Merchant Center sub-account.", "flatPath": "{merchantId}/accounts/{accountId}/claimwebsite", "httpMethod": "POST", "id": "content.accounts.claimwebsite", "parameterOrder": ["merchantId", "accountId"], "parameters": {"accountId": {"description": "The ID of the account whose website is claimed.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "merchantId": {"description": "The ID of the managing account. If this parameter is not the same as accountId, then this account must be a multi-client account and `accountId` must be the ID of a sub-account of this account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "overwrite": {"description": "Only available to selected merchants. When set to `True`, this flag removes any existing claim on the requested website by another account and replaces it with a claim from this account.", "location": "query", "type": "boolean"}}, "path": "{merchantId}/accounts/{accountId}/claimwebsite", "response": {"$ref": "AccountsClaimWebsiteResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "custombatch": {"description": "Retrieves, inserts, updates, and deletes multiple Merchant Center (sub-)accounts in a single request.", "flatPath": "accounts/batch", "httpMethod": "POST", "id": "content.accounts.custombatch", "parameterOrder": [], "parameters": {"dryRun": {"description": "Flag to simulate a request like in a live environment. If set to true, dry-run mode checks the validity of the request and returns errors (if any).", "location": "query", "type": "boolean"}}, "path": "accounts/batch", "request": {"$ref": "AccountsCustomBatchRequest"}, "response": {"$ref": "AccountsCustomBatchResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "delete": {"description": "Deletes a Merchant Center sub-account.", "flatPath": "{merchantId}/accounts/{accountId}", "httpMethod": "DELETE", "id": "content.accounts.delete", "parameterOrder": ["merchantId", "accountId"], "parameters": {"accountId": {"description": "The ID of the account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "dryRun": {"description": "Flag to simulate a request like in a live environment. If set to true, dry-run mode checks the validity of the request and returns errors (if any).", "location": "query", "type": "boolean"}, "force": {"default": "false", "description": "Flag to delete sub-accounts with products. The default value is false.", "location": "query", "type": "boolean"}, "merchantId": {"description": "The ID of the managing account. This must be a multi-client account, and accountId must be the ID of a sub-account of this account.", "format": "uint64", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/accounts/{accountId}", "scopes": ["https://www.googleapis.com/auth/content"]}, "get": {"description": "Retrieves a Merchant Center account.", "flatPath": "{merchantId}/accounts/{accountId}", "httpMethod": "GET", "id": "content.accounts.get", "parameterOrder": ["merchantId", "accountId"], "parameters": {"accountId": {"description": "The ID of the account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "merchantId": {"description": "The ID of the managing account. If this parameter is not the same as accountId, then this account must be a multi-client account and `accountId` must be the ID of a sub-account of this account.", "format": "uint64", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/accounts/{accountId}", "response": {"$ref": "Account"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "insert": {"description": "Creates a Merchant Center sub-account.", "flatPath": "{merchantId}/accounts", "httpMethod": "POST", "id": "content.accounts.insert", "parameterOrder": ["merchantId"], "parameters": {"dryRun": {"description": "Flag to simulate a request like in a live environment. If set to true, dry-run mode checks the validity of the request and returns errors (if any).", "location": "query", "type": "boolean"}, "merchantId": {"description": "The ID of the managing account. This must be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/accounts", "request": {"$ref": "Account"}, "response": {"$ref": "Account"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "link": {"description": "Performs an action on a link between two Merchant Center accounts, namely accountId and linkedAccountId.", "flatPath": "{merchantId}/accounts/{accountId}/link", "httpMethod": "POST", "id": "content.accounts.link", "parameterOrder": ["merchantId", "accountId"], "parameters": {"accountId": {"description": "The ID of the account that should be linked.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "merchantId": {"description": "The ID of the managing account. If this parameter is not the same as accountId, then this account must be a multi-client account and `accountId` must be the ID of a sub-account of this account.", "format": "uint64", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/accounts/{accountId}/link", "request": {"$ref": "AccountsLinkRequest"}, "response": {"$ref": "AccountsLinkResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "list": {"description": "Lists the sub-accounts in your Merchant Center account.", "flatPath": "{merchantId}/accounts", "httpMethod": "GET", "id": "content.accounts.list", "parameterOrder": ["merchantId"], "parameters": {"maxResults": {"description": "The maximum number of accounts to return in the response, used for paging.", "format": "uint32", "location": "query", "type": "integer"}, "merchantId": {"description": "The ID of the managing account. This must be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "pageToken": {"description": "The token returned by the previous request.", "location": "query", "type": "string"}}, "path": "{merchantId}/accounts", "response": {"$ref": "AccountsListResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "update": {"description": "Updates a Merchant Center account. Any fields that are not provided are deleted from the resource.", "flatPath": "{merchantId}/accounts/{accountId}", "httpMethod": "PUT", "id": "content.accounts.update", "parameterOrder": ["merchantId", "accountId"], "parameters": {"accountId": {"description": "The ID of the account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "dryRun": {"description": "Flag to simulate a request like in a live environment. If set to true, dry-run mode checks the validity of the request and returns errors (if any).", "location": "query", "type": "boolean"}, "merchantId": {"description": "The ID of the managing account. If this parameter is not the same as accountId, then this account must be a multi-client account and `accountId` must be the ID of a sub-account of this account.", "format": "uint64", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/accounts/{accountId}", "request": {"$ref": "Account"}, "response": {"$ref": "Account"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}, "accountstatuses": {"methods": {"custombatch": {"description": "Retrieves multiple Merchant Center account statuses in a single request.", "flatPath": "accountstatuses/batch", "httpMethod": "POST", "id": "content.accountstatuses.custombatch", "parameterOrder": [], "parameters": {}, "path": "accountstatuses/batch", "request": {"$ref": "AccountstatusesCustomBatchRequest"}, "response": {"$ref": "AccountstatusesCustomBatchResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "get": {"description": "Retrieves the status of a Merchant Center account. No itemLevelIssues are returned for multi-client accounts.", "flatPath": "{merchantId}/accountstatuses/{accountId}", "httpMethod": "GET", "id": "content.accountstatuses.get", "parameterOrder": ["merchantId", "accountId"], "parameters": {"accountId": {"description": "The ID of the account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "destinations": {"description": "If set, only issues for the specified destinations are returned, otherwise only issues for the Shopping destination.", "location": "query", "repeated": true, "type": "string"}, "merchantId": {"description": "The ID of the managing account. If this parameter is not the same as accountId, then this account must be a multi-client account and `accountId` must be the ID of a sub-account of this account.", "format": "uint64", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/accountstatuses/{accountId}", "response": {"$ref": "Account<PERSON><PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "list": {"description": "Lists the statuses of the sub-accounts in your Merchant Center account.", "flatPath": "{merchantId}/accountstatuses", "httpMethod": "GET", "id": "content.accountstatuses.list", "parameterOrder": ["merchantId"], "parameters": {"destinations": {"description": "If set, only issues for the specified destinations are returned, otherwise only issues for the Shopping destination.", "location": "query", "repeated": true, "type": "string"}, "maxResults": {"description": "The maximum number of account statuses to return in the response, used for paging.", "format": "uint32", "location": "query", "type": "integer"}, "merchantId": {"description": "The ID of the managing account. This must be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "pageToken": {"description": "The token returned by the previous request.", "location": "query", "type": "string"}}, "path": "{merchantId}/accountstatuses", "response": {"$ref": "AccountstatusesListResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}, "accounttax": {"methods": {"custombatch": {"description": "Retrieves and updates tax settings of multiple accounts in a single request.", "flatPath": "accounttax/batch", "httpMethod": "POST", "id": "content.accounttax.custombatch", "parameterOrder": [], "parameters": {"dryRun": {"description": "Flag to simulate a request like in a live environment. If set to true, dry-run mode checks the validity of the request and returns errors (if any).", "location": "query", "type": "boolean"}}, "path": "accounttax/batch", "request": {"$ref": "AccounttaxCustomBatchRequest"}, "response": {"$ref": "AccounttaxCustomBatchResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "get": {"description": "Retrieves the tax settings of the account.", "flatPath": "{merchantId}/accounttax/{accountId}", "httpMethod": "GET", "id": "content.accounttax.get", "parameterOrder": ["merchantId", "accountId"], "parameters": {"accountId": {"description": "The ID of the account for which to get/update account tax settings.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "merchantId": {"description": "The ID of the managing account. If this parameter is not the same as accountId, then this account must be a multi-client account and `accountId` must be the ID of a sub-account of this account.", "format": "uint64", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/accounttax/{accountId}", "response": {"$ref": "AccountTax"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "list": {"description": "Lists the tax settings of the sub-accounts in your Merchant Center account.", "flatPath": "{merchantId}/accounttax", "httpMethod": "GET", "id": "content.accounttax.list", "parameterOrder": ["merchantId"], "parameters": {"maxResults": {"description": "The maximum number of tax settings to return in the response, used for paging.", "format": "uint32", "location": "query", "type": "integer"}, "merchantId": {"description": "The ID of the managing account. This must be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "pageToken": {"description": "The token returned by the previous request.", "location": "query", "type": "string"}}, "path": "{merchantId}/accounttax", "response": {"$ref": "AccounttaxListResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "update": {"description": "Updates the tax settings of the account. Any fields that are not provided are deleted from the resource.", "flatPath": "{merchantId}/accounttax/{accountId}", "httpMethod": "PUT", "id": "content.accounttax.update", "parameterOrder": ["merchantId", "accountId"], "parameters": {"accountId": {"description": "The ID of the account for which to get/update account tax settings.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "dryRun": {"description": "Flag to simulate a request like in a live environment. If set to true, dry-run mode checks the validity of the request and returns errors (if any).", "location": "query", "type": "boolean"}, "merchantId": {"description": "The ID of the managing account. If this parameter is not the same as accountId, then this account must be a multi-client account and `accountId` must be the ID of a sub-account of this account.", "format": "uint64", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/accounttax/{accountId}", "request": {"$ref": "AccountTax"}, "response": {"$ref": "AccountTax"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}, "datafeeds": {"methods": {"custombatch": {"description": "Deletes, fetches, gets, inserts and updates multiple datafeeds in a single request.", "flatPath": "datafeeds/batch", "httpMethod": "POST", "id": "content.datafeeds.custombatch", "parameterOrder": [], "parameters": {"dryRun": {"description": "Flag to simulate a request like in a live environment. If set to true, dry-run mode checks the validity of the request and returns errors (if any).", "location": "query", "type": "boolean"}}, "path": "datafeeds/batch", "request": {"$ref": "DatafeedsCustomBatchRequest"}, "response": {"$ref": "DatafeedsCustomBatchResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "delete": {"description": "Deletes a datafeed configuration from your Merchant Center account.", "flatPath": "{merchantId}/datafeeds/{datafeedId}", "httpMethod": "DELETE", "id": "content.datafeeds.delete", "parameterOrder": ["merchantId", "datafeedId"], "parameters": {"datafeedId": {"description": "The ID of the datafeed.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "dryRun": {"description": "Flag to simulate a request like in a live environment. If set to true, dry-run mode checks the validity of the request and returns errors (if any).", "location": "query", "type": "boolean"}, "merchantId": {"description": "The ID of the account that manages the datafeed. This account cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/datafeeds/{datafeedId}", "scopes": ["https://www.googleapis.com/auth/content"]}, "fetchnow": {"description": "Invokes a fetch for the datafeed in your Merchant Center account. If you need to call this method more than once per day, we recommend you use the Products service to update your product data.", "flatPath": "{merchantId}/datafeeds/{datafeedId}/fetchNow", "httpMethod": "POST", "id": "content.datafeeds.fetchnow", "parameterOrder": ["merchantId", "datafeedId"], "parameters": {"datafeedId": {"description": "The ID of the datafeed to be fetched.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "dryRun": {"description": "Flag to simulate a request like in a live environment. If set to true, dry-run mode checks the validity of the request and returns errors (if any).", "location": "query", "type": "boolean"}, "merchantId": {"description": "The ID of the account that manages the datafeed. This account cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/datafeeds/{datafeedId}/fetchNow", "response": {"$ref": "DatafeedsFetchNowResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "get": {"description": "Retrieves a datafeed configuration from your Merchant Center account.", "flatPath": "{merchantId}/datafeeds/{datafeedId}", "httpMethod": "GET", "id": "content.datafeeds.get", "parameterOrder": ["merchantId", "datafeedId"], "parameters": {"datafeedId": {"description": "The ID of the datafeed.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "merchantId": {"description": "The ID of the account that manages the datafeed. This account cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/datafeeds/{datafeedId}", "response": {"$ref": "Datafeed"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "insert": {"description": "Registers a datafeed configuration with your Merchant Center account.", "flatPath": "{merchantId}/datafeeds", "httpMethod": "POST", "id": "content.datafeeds.insert", "parameterOrder": ["merchantId"], "parameters": {"dryRun": {"description": "Flag to simulate a request like in a live environment. If set to true, dry-run mode checks the validity of the request and returns errors (if any).", "location": "query", "type": "boolean"}, "merchantId": {"description": "The ID of the account that manages the datafeed. This account cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/datafeeds", "request": {"$ref": "Datafeed"}, "response": {"$ref": "Datafeed"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "list": {"description": "Lists the configurations for datafeeds in your Merchant Center account.", "flatPath": "{merchantId}/datafeeds", "httpMethod": "GET", "id": "content.datafeeds.list", "parameterOrder": ["merchantId"], "parameters": {"maxResults": {"description": "The maximum number of products to return in the response, used for paging.", "format": "uint32", "location": "query", "type": "integer"}, "merchantId": {"description": "The ID of the account that manages the datafeeds. This account cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "pageToken": {"description": "The token returned by the previous request.", "location": "query", "type": "string"}}, "path": "{merchantId}/datafeeds", "response": {"$ref": "DatafeedsListResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "update": {"description": "Updates a datafeed configuration of your Merchant Center account. Any fields that are not provided are deleted from the resource.", "flatPath": "{merchantId}/datafeeds/{datafeedId}", "httpMethod": "PUT", "id": "content.datafeeds.update", "parameterOrder": ["merchantId", "datafeedId"], "parameters": {"datafeedId": {"description": "The ID of the datafeed.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "dryRun": {"description": "Flag to simulate a request like in a live environment. If set to true, dry-run mode checks the validity of the request and returns errors (if any).", "location": "query", "type": "boolean"}, "merchantId": {"description": "The ID of the account that manages the datafeed. This account cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/datafeeds/{datafeedId}", "request": {"$ref": "Datafeed"}, "response": {"$ref": "Datafeed"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}, "datafeedstatuses": {"methods": {"custombatch": {"description": "Gets multiple Merchant Center datafeed statuses in a single request.", "flatPath": "datafeedstatuses/batch", "httpMethod": "POST", "id": "content.datafeedstatuses.custombatch", "parameterOrder": [], "parameters": {}, "path": "datafeedstatuses/batch", "request": {"$ref": "DatafeedstatusesCustomBatchRequest"}, "response": {"$ref": "DatafeedstatusesCustomBatchResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "get": {"description": "Retrieves the status of a datafeed from your Merchant Center account.", "flatPath": "{merchantId}/datafeedstatuses/{datafeedId}", "httpMethod": "GET", "id": "content.datafeedstatuses.get", "parameterOrder": ["merchantId", "datafeedId"], "parameters": {"country": {"description": "The country for which to get the datafeed status. If this parameter is provided then language must also be provided. Note that this parameter is required for feeds targeting multiple countries and languages, since a feed may have a different status for each target.", "location": "query", "type": "string"}, "datafeedId": {"description": "The ID of the datafeed.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "language": {"description": "The language for which to get the datafeed status. If this parameter is provided then country must also be provided. Note that this parameter is required for feeds targeting multiple countries and languages, since a feed may have a different status for each target.", "location": "query", "type": "string"}, "merchantId": {"description": "The ID of the account that manages the datafeed. This account cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/datafeedstatuses/{datafeedId}", "response": {"$ref": "DatafeedStatus"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "list": {"description": "Lists the statuses of the datafeeds in your Merchant Center account.", "flatPath": "{merchantId}/datafeedstatuses", "httpMethod": "GET", "id": "content.datafeedstatuses.list", "parameterOrder": ["merchantId"], "parameters": {"maxResults": {"description": "The maximum number of products to return in the response, used for paging.", "format": "uint32", "location": "query", "type": "integer"}, "merchantId": {"description": "The ID of the account that manages the datafeeds. This account cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "pageToken": {"description": "The token returned by the previous request.", "location": "query", "type": "string"}}, "path": "{merchantId}/datafeedstatuses", "response": {"$ref": "DatafeedstatusesListResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}, "liasettings": {"methods": {"custombatch": {"description": "Retrieves and/or updates the LIA settings of multiple accounts in a single request.", "flatPath": "liasettings/batch", "httpMethod": "POST", "id": "content.liasettings.custombatch", "parameterOrder": [], "parameters": {"dryRun": {"description": "Flag to simulate a request like in a live environment. If set to true, dry-run mode checks the validity of the request and returns errors (if any).", "location": "query", "type": "boolean"}}, "path": "liasettings/batch", "request": {"$ref": "LiasettingsCustomBatchRequest"}, "response": {"$ref": "LiasettingsCustomBatchResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "get": {"description": "Retrieves the LIA settings of the account.", "flatPath": "{merchantId}/liasettings/{accountId}", "httpMethod": "GET", "id": "content.liasettings.get", "parameterOrder": ["merchantId", "accountId"], "parameters": {"accountId": {"description": "The ID of the account for which to get or update LIA settings.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "merchantId": {"description": "The ID of the managing account. If this parameter is not the same as accountId, then this account must be a multi-client account and `accountId` must be the ID of a sub-account of this account.", "format": "uint64", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/liasettings/{accountId}", "response": {"$ref": "LiaSettings"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "getaccessiblegmbaccounts": {"description": "Retrieves the list of accessible Google My Business accounts.", "flatPath": "{merchantId}/liasettings/{accountId}/accessiblegmbaccounts", "httpMethod": "GET", "id": "content.liasettings.getaccessiblegmbaccounts", "parameterOrder": ["merchantId", "accountId"], "parameters": {"accountId": {"description": "The ID of the account for which to retrieve accessible Google My Business accounts.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "merchantId": {"description": "The ID of the managing account. If this parameter is not the same as accountId, then this account must be a multi-client account and `accountId` must be the ID of a sub-account of this account.", "format": "uint64", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/liasettings/{accountId}/accessiblegmbaccounts", "response": {"$ref": "LiasettingsGetAccessibleGmbAccountsResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "list": {"description": "Lists the LIA settings of the sub-accounts in your Merchant Center account.", "flatPath": "{merchantId}/liasettings", "httpMethod": "GET", "id": "content.liasettings.list", "parameterOrder": ["merchantId"], "parameters": {"maxResults": {"description": "The maximum number of LIA settings to return in the response, used for paging.", "format": "uint32", "location": "query", "type": "integer"}, "merchantId": {"description": "The ID of the managing account. This must be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "pageToken": {"description": "The token returned by the previous request.", "location": "query", "type": "string"}}, "path": "{merchantId}/liasettings", "response": {"$ref": "LiasettingsListResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "listposdataproviders": {"description": "Retrieves the list of POS data providers that have active settings for the all eiligible countries.", "flatPath": "liasettings/posdataproviders", "httpMethod": "GET", "id": "content.liasettings.listposdataproviders", "parameterOrder": [], "parameters": {}, "path": "liasettings/posdataproviders", "response": {"$ref": "LiasettingsListPosDataProvidersResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "requestgmbaccess": {"description": "Requests access to a specified Google My Business account.", "flatPath": "{merchantId}/liasettings/{accountId}/requestgmbaccess", "httpMethod": "POST", "id": "content.liasettings.requestgmbaccess", "parameterOrder": ["merchantId", "accountId", "gmbEmail"], "parameters": {"accountId": {"description": "The ID of the account for which GMB access is requested.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "gmbEmail": {"description": "The email of the Google My Business account.", "location": "query", "required": true, "type": "string"}, "merchantId": {"description": "The ID of the managing account. If this parameter is not the same as accountId, then this account must be a multi-client account and `accountId` must be the ID of a sub-account of this account.", "format": "uint64", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/liasettings/{accountId}/requestgmbaccess", "response": {"$ref": "LiasettingsRequestGmbAccessResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "requestinventoryverification": {"description": "Requests inventory validation for the specified country.", "flatPath": "{merchantId}/liasettings/{accountId}/requestinventoryverification/{country}", "httpMethod": "POST", "id": "content.liasettings.requestinventoryverification", "parameterOrder": ["merchantId", "accountId", "country"], "parameters": {"accountId": {"description": "The ID of the account that manages the order. This cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "country": {"description": "The country for which inventory validation is requested.", "location": "path", "required": true, "type": "string"}, "merchantId": {"description": "The ID of the managing account. If this parameter is not the same as accountId, then this account must be a multi-client account and `accountId` must be the ID of a sub-account of this account.", "format": "uint64", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/liasettings/{accountId}/requestinventoryverification/{country}", "response": {"$ref": "LiasettingsRequestInventoryVerificationResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "setinventoryverificationcontact": {"description": "Sets the inventory verification contract for the specified country.", "flatPath": "{merchantId}/liasettings/{accountId}/setinventoryverificationcontact", "httpMethod": "POST", "id": "content.liasettings.setinventoryverificationcontact", "parameterOrder": ["merchantId", "accountId", "country", "language", "contactName", "contactEmail"], "parameters": {"accountId": {"description": "The ID of the account that manages the order. This cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "contactEmail": {"description": "The email of the inventory verification contact.", "location": "query", "required": true, "type": "string"}, "contactName": {"description": "The name of the inventory verification contact.", "location": "query", "required": true, "type": "string"}, "country": {"description": "The country for which inventory verification is requested.", "location": "query", "required": true, "type": "string"}, "language": {"description": "The language for which inventory verification is requested.", "location": "query", "required": true, "type": "string"}, "merchantId": {"description": "The ID of the managing account. If this parameter is not the same as accountId, then this account must be a multi-client account and `accountId` must be the ID of a sub-account of this account.", "format": "uint64", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/liasettings/{accountId}/setinventoryverificationcontact", "response": {"$ref": "LiasettingsSetInventoryVerificationContactResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "setposdataprovider": {"description": "Sets the POS data provider for the specified country.", "flatPath": "{merchantId}/liasettings/{accountId}/setposdataprovider", "httpMethod": "POST", "id": "content.liasettings.setposdataprovider", "parameterOrder": ["merchantId", "accountId", "country"], "parameters": {"accountId": {"description": "The ID of the account for which to retrieve accessible Google My Business accounts.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "country": {"description": "The country for which the POS data provider is selected.", "location": "query", "required": true, "type": "string"}, "merchantId": {"description": "The ID of the managing account. If this parameter is not the same as accountId, then this account must be a multi-client account and `accountId` must be the ID of a sub-account of this account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "posDataProviderId": {"description": "The ID of POS data provider.", "format": "uint64", "location": "query", "type": "string"}, "posExternalAccountId": {"description": "The account ID by which this merchant is known to the POS data provider.", "location": "query", "type": "string"}}, "path": "{merchantId}/liasettings/{accountId}/setposdataprovider", "response": {"$ref": "LiasettingsSetPosDataProviderResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "update": {"description": "Updates the LIA settings of the account. Any fields that are not provided are deleted from the resource.", "flatPath": "{merchantId}/liasettings/{accountId}", "httpMethod": "PUT", "id": "content.liasettings.update", "parameterOrder": ["merchantId", "accountId"], "parameters": {"accountId": {"description": "The ID of the account for which to get or update LIA settings.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "dryRun": {"description": "Flag to simulate a request like in a live environment. If set to true, dry-run mode checks the validity of the request and returns errors (if any).", "location": "query", "type": "boolean"}, "merchantId": {"description": "The ID of the managing account. If this parameter is not the same as accountId, then this account must be a multi-client account and `accountId` must be the ID of a sub-account of this account.", "format": "uint64", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/liasettings/{accountId}", "request": {"$ref": "LiaSettings"}, "response": {"$ref": "LiaSettings"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}, "orderinvoices": {"methods": {"createchargeinvoice": {"description": "Creates a charge invoice for a shipment group, and triggers a charge capture for orderinvoice enabled orders.", "flatPath": "{merchantId}/orderinvoices/{orderId}/createChargeInvoice", "httpMethod": "POST", "id": "content.orderinvoices.createchargeinvoice", "parameterOrder": ["merchantId", "orderId"], "parameters": {"merchantId": {"description": "The ID of the account that manages the order. This cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "orderId": {"description": "The ID of the order.", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/orderinvoices/{orderId}/createChargeInvoice", "request": {"$ref": "OrderinvoicesCreateChargeInvoiceRequest"}, "response": {"$ref": "OrderinvoicesCreateChargeInvoiceResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "createrefundinvoice": {"description": "Creates a refund invoice for one or more shipment groups, and triggers a refund for orderinvoice enabled orders. This can only be used for line items that have previously been charged using `createChargeInvoice`. All amounts (except for the summary) are incremental with respect to the previous invoice.", "flatPath": "{merchantId}/orderinvoices/{orderId}/createRefundInvoice", "httpMethod": "POST", "id": "content.orderinvoices.createrefundinvoice", "parameterOrder": ["merchantId", "orderId"], "parameters": {"merchantId": {"description": "The ID of the account that manages the order. This cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "orderId": {"description": "The ID of the order.", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/orderinvoices/{orderId}/createRefundInvoice", "request": {"$ref": "OrderinvoicesCreateRefundInvoiceRequest"}, "response": {"$ref": "OrderinvoicesCreateRefundInvoiceResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}, "orderreports": {"methods": {"listdisbursements": {"description": "Retrieves a report for disbursements from your Merchant Center account.", "flatPath": "{merchantId}/orderreports/disbursements", "httpMethod": "GET", "id": "content.orderreports.listdisbursements", "parameterOrder": ["merchantId"], "parameters": {"disbursementEndDate": {"description": "The last date which disbursements occurred. In ISO 8601 format. Default: current date.", "location": "query", "type": "string"}, "disbursementStartDate": {"description": "The first date which disbursements occurred. In ISO 8601 format.", "location": "query", "type": "string"}, "maxResults": {"description": "The maximum number of disbursements to return in the response, used for paging.", "format": "uint32", "location": "query", "type": "integer"}, "merchantId": {"description": "The ID of the account that manages the order. This cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "pageToken": {"description": "The token returned by the previous request.", "location": "query", "type": "string"}}, "path": "{merchantId}/orderreports/disbursements", "response": {"$ref": "OrderreportsListDisbursementsResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "listtransactions": {"description": "Retrieves a list of transactions for a disbursement from your Merchant Center account.", "flatPath": "{merchantId}/orderreports/disbursements/{disbursementId}/transactions", "httpMethod": "GET", "id": "content.orderreports.listtransactions", "parameterOrder": ["merchantId", "disbursementId"], "parameters": {"disbursementId": {"description": "The Google-provided ID of the disbursement (found in Wallet).", "location": "path", "required": true, "type": "string"}, "maxResults": {"description": "The maximum number of disbursements to return in the response, used for paging.", "format": "uint32", "location": "query", "type": "integer"}, "merchantId": {"description": "The ID of the account that manages the order. This cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "pageToken": {"description": "The token returned by the previous request.", "location": "query", "type": "string"}, "transactionEndDate": {"description": "The last date in which transaction occurred. In ISO 8601 format. Default: current date.", "location": "query", "type": "string"}, "transactionStartDate": {"description": "The first date in which transaction occurred. In ISO 8601 format.", "location": "query", "type": "string"}}, "path": "{merchantId}/orderreports/disbursements/{disbursementId}/transactions", "response": {"$ref": "OrderreportsListTransactionsResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}, "orderreturns": {"methods": {"get": {"description": "Retrieves an order return from your Merchant Center account.", "flatPath": "{merchantId}/orderreturns/{returnId}", "httpMethod": "GET", "id": "content.orderreturns.get", "parameterOrder": ["merchantId", "returnId"], "parameters": {"merchantId": {"description": "The ID of the account that manages the order. This cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "returnId": {"description": "Merchant order return ID generated by Google.", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/orderreturns/{returnId}", "response": {"$ref": "MerchantOrderReturn"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "list": {"description": "Lists order returns in your Merchant Center account.", "flatPath": "{merchantId}/orderreturns", "httpMethod": "GET", "id": "content.orderreturns.list", "parameterOrder": ["merchantId"], "parameters": {"createdEndDate": {"description": "Obtains order returns created before this date (inclusively), in ISO 8601 format.", "location": "query", "type": "string"}, "createdStartDate": {"description": "Obtains order returns created after this date (inclusively), in ISO 8601 format.", "location": "query", "type": "string"}, "maxResults": {"description": "The maximum number of order returns to return in the response, used for paging. The default value is 25 returns per page, and the maximum allowed value is 250 returns per page.", "format": "uint32", "location": "query", "type": "integer"}, "merchantId": {"description": "The ID of the account that manages the order. This cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "orderBy": {"description": "Return the results in the specified order.", "enum": ["RETURN_CREATION_TIME_DESC", "RETURN_CREATION_TIME_ASC"], "enumDescriptions": ["", ""], "location": "query", "type": "string"}, "pageToken": {"description": "The token returned by the previous request.", "location": "query", "type": "string"}}, "path": "{merchantId}/orderreturns", "response": {"$ref": "OrderreturnsListResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}, "orders": {"methods": {"acknowledge": {"description": "Marks an order as acknowledged.", "flatPath": "{merchantId}/orders/{orderId}/acknowledge", "httpMethod": "POST", "id": "content.orders.acknowledge", "parameterOrder": ["merchantId", "orderId"], "parameters": {"merchantId": {"description": "The ID of the account that manages the order. This cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "orderId": {"description": "The ID of the order.", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/orders/{orderId}/acknowledge", "request": {"$ref": "OrdersAcknowledgeRequest"}, "response": {"$ref": "OrdersAcknowledgeResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "advancetestorder": {"description": "Sandbox only. Moves a test order from state \"`inProgress`\" to state \"`pendingShipment`\".", "flatPath": "{merchantId}/testorders/{orderId}/advance", "httpMethod": "POST", "id": "content.orders.advancetestorder", "parameterOrder": ["merchantId", "orderId"], "parameters": {"merchantId": {"description": "The ID of the account that manages the order. This cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "orderId": {"description": "The ID of the test order to modify.", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/testorders/{orderId}/advance", "response": {"$ref": "OrdersAdvanceTestOrderResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "cancel": {"description": "Cancels all line items in an order, making a full refund.", "flatPath": "{merchantId}/orders/{orderId}/cancel", "httpMethod": "POST", "id": "content.orders.cancel", "parameterOrder": ["merchantId", "orderId"], "parameters": {"merchantId": {"description": "The ID of the account that manages the order. This cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "orderId": {"description": "The ID of the order to cancel.", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/orders/{orderId}/cancel", "request": {"$ref": "OrdersCancelRequest"}, "response": {"$ref": "OrdersCancelResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "cancellineitem": {"description": "Cancels a line item, making a full refund.", "flatPath": "{merchantId}/orders/{orderId}/cancelLineItem", "httpMethod": "POST", "id": "content.orders.cancellineitem", "parameterOrder": ["merchantId", "orderId"], "parameters": {"merchantId": {"description": "The ID of the account that manages the order. This cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "orderId": {"description": "The ID of the order.", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/orders/{orderId}/cancelLineItem", "request": {"$ref": "OrdersCancelLineItemRequest"}, "response": {"$ref": "OrdersCancelLineItemResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "canceltestorderbycustomer": {"description": "Sandbox only. Cancels a test order for customer-initiated cancellation.", "flatPath": "{merchantId}/testorders/{orderId}/cancelByCustomer", "httpMethod": "POST", "id": "content.orders.canceltestorderbycustomer", "parameterOrder": ["merchantId", "orderId"], "parameters": {"merchantId": {"description": "The ID of the account that manages the order. This cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "orderId": {"description": "The ID of the test order to cancel.", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/testorders/{orderId}/cancelByCustomer", "request": {"$ref": "OrdersCancelTestOrderByCustomerRequest"}, "response": {"$ref": "OrdersCancelTestOrderByCustomerResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "createtestorder": {"description": "Sandbox only. Creates a test order.", "flatPath": "{merchantId}/testorders", "httpMethod": "POST", "id": "content.orders.createtestorder", "parameterOrder": ["merchantId"], "parameters": {"merchantId": {"description": "The ID of the account that should manage the order. This cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/testorders", "request": {"$ref": "OrdersCreateTestOrderRequest"}, "response": {"$ref": "OrdersCreateTestOrderResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "createtestreturn": {"description": "Sandbox only. Creates a test return.", "flatPath": "{merchantId}/orders/{orderId}/testreturn", "httpMethod": "POST", "id": "content.orders.createtestreturn", "parameterOrder": ["merchantId", "orderId"], "parameters": {"merchantId": {"description": "The ID of the account that manages the order. This cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "orderId": {"description": "The ID of the order.", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/orders/{orderId}/testreturn", "request": {"$ref": "OrdersCreateTestReturnRequest"}, "response": {"$ref": "OrdersCreateTestReturnResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "custombatch": {"description": "Retrieves or modifies multiple orders in a single request.", "flatPath": "orders/batch", "httpMethod": "POST", "id": "content.orders.custombatch", "parameterOrder": [], "parameters": {}, "path": "orders/batch", "request": {"$ref": "OrdersCustomBatchRequest"}, "response": {"$ref": "OrdersCustomBatchResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "get": {"description": "Retrieves an order from your Merchant Center account.", "flatPath": "{merchantId}/orders/{orderId}", "httpMethod": "GET", "id": "content.orders.get", "parameterOrder": ["merchantId", "orderId"], "parameters": {"merchantId": {"description": "The ID of the account that manages the order. This cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "orderId": {"description": "The ID of the order.", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/orders/{orderId}", "response": {"$ref": "Order"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "getbymerchantorderid": {"description": "Retrieves an order using merchant order ID.", "flatPath": "{merchantId}/ordersbymerchantid/{merchantOrderId}", "httpMethod": "GET", "id": "content.orders.getbymerchantorderid", "parameterOrder": ["merchantId", "merchantOrderId"], "parameters": {"merchantId": {"description": "The ID of the account that manages the order. This cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "merchantOrderId": {"description": "The merchant order ID to be looked for.", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/ordersbymerchantid/{merchantOrderId}", "response": {"$ref": "OrdersGetByMerchantOrderIdResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "gettestordertemplate": {"description": "Sandbox only. Retrieves an order template that can be used to quickly create a new order in sandbox.", "flatPath": "{merchantId}/testordertemplates/{templateName}", "httpMethod": "GET", "id": "content.orders.gettestordertemplate", "parameterOrder": ["merchantId", "templateName"], "parameters": {"country": {"description": "The country of the template to retrieve. Defaults to `US`.", "location": "query", "type": "string"}, "merchantId": {"description": "The ID of the account that should manage the order. This cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "templateName": {"description": "The name of the template to retrieve.", "enum": ["TEMPLATE1", "TEMPLATE2", "TEMPLATE1A", "TEMPLATE1B", "TEMPLATE3"], "enumDescriptions": ["", "", "", "", ""], "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/testordertemplates/{templateName}", "response": {"$ref": "OrdersGetTestOrderTemplateResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "instorerefundlineitem": {"description": "Deprecated. Notifies that item return and refund was handled directly by merchant outside of Google payments processing (e.g. cash refund done in store). Note: We recommend calling the returnrefundlineitem method to refund in-store returns. We will issue the refund directly to the customer. This helps to prevent possible differences arising between merchant and Google transaction records. We also recommend having the point of sale system communicate with Google to ensure that customers do not receive a double refund by first refunding via Google then via an in-store return.", "flatPath": "{merchantId}/orders/{orderId}/inStoreRefundLineItem", "httpMethod": "POST", "id": "content.orders.instorerefundlineitem", "parameterOrder": ["merchantId", "orderId"], "parameters": {"merchantId": {"description": "The ID of the account that manages the order. This cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "orderId": {"description": "The ID of the order.", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/orders/{orderId}/inStoreRefundLineItem", "request": {"$ref": "OrdersInStoreRefundLineItemRequest"}, "response": {"$ref": "OrdersInStoreRefundLineItemResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "list": {"description": "Lists the orders in your Merchant Center account.", "flatPath": "{merchantId}/orders", "httpMethod": "GET", "id": "content.orders.list", "parameterOrder": ["merchantId"], "parameters": {"acknowledged": {"description": "Obtains orders that match the acknowledgement status. When set to true, obtains orders that have been acknowledged. When false, obtains orders that have not been acknowledged. We recommend using this filter set to `false`, in conjunction with the `acknowledge` call, such that only un-acknowledged orders are returned. ", "location": "query", "type": "boolean"}, "maxResults": {"description": "The maximum number of orders to return in the response, used for paging. The default value is 25 orders per page, and the maximum allowed value is 250 orders per page.", "format": "uint32", "location": "query", "type": "integer"}, "merchantId": {"description": "The ID of the account that manages the order. This cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "orderBy": {"description": "Order results by placement date in descending or ascending order. Acceptable values are: - placedDateAsc - placedDateDesc ", "location": "query", "type": "string"}, "pageToken": {"description": "The token returned by the previous request.", "location": "query", "type": "string"}, "placedDateEnd": {"description": "Obtains orders placed before this date (exclusively), in ISO 8601 format.", "location": "query", "type": "string"}, "placedDateStart": {"description": "Obtains orders placed after this date (inclusively), in ISO 8601 format.", "location": "query", "type": "string"}, "statuses": {"description": "Obtains orders that match any of the specified statuses. Please note that `active` is a shortcut for `pendingShipment` and `partiallyShipped`, and `completed` is a shortcut for `shipped`, `partiallyDelivered`, `delivered`, `partiallyReturned`, `returned`, and `canceled`.", "enum": ["ACTIVE", "COMPLETED", "CANCELED", "IN_PROGRESS", "PENDING_SHIPMENT", "PARTIALLY_SHIPPED", "SHIPPED", "PARTIALLY_DELIVERED", "DELIVERED", "PARTIALLY_RETURNED", "RETURNED"], "enumDescriptions": ["", "", "", "", "", "", "", "", "", "", ""], "location": "query", "repeated": true, "type": "string"}}, "path": "{merchantId}/orders", "response": {"$ref": "OrdersListResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "refund": {"description": "Deprecated, please use returnRefundLineItem instead.", "flatPath": "{merchantId}/orders/{orderId}/refund", "httpMethod": "POST", "id": "content.orders.refund", "parameterOrder": ["merchantId", "orderId"], "parameters": {"merchantId": {"description": "The ID of the account that manages the order. This cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "orderId": {"description": "The ID of the order to refund.", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/orders/{orderId}/refund", "request": {"$ref": "OrdersRefundRequest"}, "response": {"$ref": "OrdersRefundResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "rejectreturnlineitem": {"description": "Rejects return on an line item.", "flatPath": "{merchantId}/orders/{orderId}/rejectReturnLineItem", "httpMethod": "POST", "id": "content.orders.rejectreturnlineitem", "parameterOrder": ["merchantId", "orderId"], "parameters": {"merchantId": {"description": "The ID of the account that manages the order. This cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "orderId": {"description": "The ID of the order.", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/orders/{orderId}/rejectReturnLineItem", "request": {"$ref": "OrdersRejectReturnLineItemRequest"}, "response": {"$ref": "OrdersRejectReturnLineItemResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "returnlineitem": {"description": "Returns a line item.", "flatPath": "{merchantId}/orders/{orderId}/returnLineItem", "httpMethod": "POST", "id": "content.orders.returnlineitem", "parameterOrder": ["merchantId", "orderId"], "parameters": {"merchantId": {"description": "The ID of the account that manages the order. This cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "orderId": {"description": "The ID of the order.", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/orders/{orderId}/returnLineItem", "request": {"$ref": "OrdersReturnLineItemRequest"}, "response": {"$ref": "OrdersReturnLineItemResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "returnrefundlineitem": {"description": "Returns and refunds a line item. Note that this method can only be called on fully shipped orders. Please also note that the Orderreturns API is the preferred way to handle returns after you receive a return from a customer. You can use Orderreturns.list or Orderreturns.get to search for the return, and then use Orderreturns.processreturn to issue the refund. If the return cannot be found, then we recommend using this API to issue a refund.", "flatPath": "{merchantId}/orders/{orderId}/returnRefundLineItem", "httpMethod": "POST", "id": "content.orders.returnrefundlineitem", "parameterOrder": ["merchantId", "orderId"], "parameters": {"merchantId": {"description": "The ID of the account that manages the order. This cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "orderId": {"description": "The ID of the order.", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/orders/{orderId}/returnRefundLineItem", "request": {"$ref": "OrdersReturnRefundLineItemRequest"}, "response": {"$ref": "OrdersReturnRefundLineItemResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "setlineitemmetadata": {"description": "Sets (or overrides if it already exists) merchant provided annotations in the form of key-value pairs. A common use case would be to supply us with additional structured information about a line item that cannot be provided via other methods. Submitted key-value pairs can be retrieved as part of the orders resource.", "flatPath": "{merchantId}/orders/{orderId}/setLineItemMetadata", "httpMethod": "POST", "id": "content.orders.setlineitemmetadata", "parameterOrder": ["merchantId", "orderId"], "parameters": {"merchantId": {"description": "The ID of the account that manages the order. This cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "orderId": {"description": "The ID of the order.", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/orders/{orderId}/setLineItemMetadata", "request": {"$ref": "OrdersSetLineItemMetadataRequest"}, "response": {"$ref": "OrdersSetLineItemMetadataResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "shiplineitems": {"description": "Marks line item(s) as shipped.", "flatPath": "{merchantId}/orders/{orderId}/shipLineItems", "httpMethod": "POST", "id": "content.orders.shiplineitems", "parameterOrder": ["merchantId", "orderId"], "parameters": {"merchantId": {"description": "The ID of the account that manages the order. This cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "orderId": {"description": "The ID of the order.", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/orders/{orderId}/shipLineItems", "request": {"$ref": "OrdersShipLineItemsRequest"}, "response": {"$ref": "OrdersShipLineItemsResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "updatelineitemshippingdetails": {"description": "Updates ship by and delivery by dates for a line item.", "flatPath": "{merchantId}/orders/{orderId}/updateLineItemShippingDetails", "httpMethod": "POST", "id": "content.orders.updatelineitemshippingdetails", "parameterOrder": ["merchantId", "orderId"], "parameters": {"merchantId": {"description": "The ID of the account that manages the order. This cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "orderId": {"description": "The ID of the order.", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/orders/{orderId}/updateLineItemShippingDetails", "request": {"$ref": "OrdersUpdateLineItemShippingDetailsRequest"}, "response": {"$ref": "OrdersUpdateLineItemShippingDetailsResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "updatemerchantorderid": {"description": "Updates the merchant order ID for a given order.", "flatPath": "{merchantId}/orders/{orderId}/updateMerchantOrderId", "httpMethod": "POST", "id": "content.orders.updatemerchantorderid", "parameterOrder": ["merchantId", "orderId"], "parameters": {"merchantId": {"description": "The ID of the account that manages the order. This cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "orderId": {"description": "The ID of the order.", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/orders/{orderId}/updateMerchantOrderId", "request": {"$ref": "OrdersUpdateMerchantOrderIdRequest"}, "response": {"$ref": "OrdersUpdateMerchantOrderIdResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "updateshipment": {"description": "Updates a shipment's status, carrier, and/or tracking ID.", "flatPath": "{merchantId}/orders/{orderId}/updateShipment", "httpMethod": "POST", "id": "content.orders.updateshipment", "parameterOrder": ["merchantId", "orderId"], "parameters": {"merchantId": {"description": "The ID of the account that manages the order. This cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "orderId": {"description": "The ID of the order.", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/orders/{orderId}/updateShipment", "request": {"$ref": "OrdersUpdateShipmentRequest"}, "response": {"$ref": "OrdersUpdateShipmentResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}, "pos": {"methods": {"custombatch": {"description": "Batches multiple POS-related calls in a single request.", "flatPath": "pos/batch", "httpMethod": "POST", "id": "content.pos.custombatch", "parameterOrder": [], "parameters": {"dryRun": {"description": "Flag to simulate a request like in a live environment. If set to true, dry-run mode checks the validity of the request and returns errors (if any).", "location": "query", "type": "boolean"}}, "path": "pos/batch", "request": {"$ref": "PosCustomBatchRequest"}, "response": {"$ref": "PosCustomBatchResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "delete": {"description": "Deletes a store for the given merchant.", "flatPath": "{merchantId}/pos/{targetMerchantId}/store/{storeCode}", "httpMethod": "DELETE", "id": "content.pos.delete", "parameterOrder": ["merchantId", "targetMerchantId", "storeCode"], "parameters": {"dryRun": {"description": "Flag to simulate a request like in a live environment. If set to true, dry-run mode checks the validity of the request and returns errors (if any).", "location": "query", "type": "boolean"}, "merchantId": {"description": "The ID of the POS or inventory data provider.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "storeCode": {"description": "A store code that is unique per merchant.", "location": "path", "required": true, "type": "string"}, "targetMerchantId": {"description": "The ID of the target merchant.", "format": "uint64", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/pos/{targetMerchantId}/store/{storeCode}", "scopes": ["https://www.googleapis.com/auth/content"]}, "get": {"description": "Retrieves information about the given store.", "flatPath": "{merchantId}/pos/{targetMerchantId}/store/{storeCode}", "httpMethod": "GET", "id": "content.pos.get", "parameterOrder": ["merchantId", "targetMerchantId", "storeCode"], "parameters": {"merchantId": {"description": "The ID of the POS or inventory data provider.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "storeCode": {"description": "A store code that is unique per merchant.", "location": "path", "required": true, "type": "string"}, "targetMerchantId": {"description": "The ID of the target merchant.", "format": "uint64", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/pos/{targetMerchantId}/store/{storeCode}", "response": {"$ref": "PosStore"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "insert": {"description": "Creates a store for the given merchant.", "flatPath": "{merchantId}/pos/{targetMerchantId}/store", "httpMethod": "POST", "id": "content.pos.insert", "parameterOrder": ["merchantId", "targetMerchantId"], "parameters": {"dryRun": {"description": "Flag to simulate a request like in a live environment. If set to true, dry-run mode checks the validity of the request and returns errors (if any).", "location": "query", "type": "boolean"}, "merchantId": {"description": "The ID of the POS or inventory data provider.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "targetMerchantId": {"description": "The ID of the target merchant.", "format": "uint64", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/pos/{targetMerchantId}/store", "request": {"$ref": "PosStore"}, "response": {"$ref": "PosStore"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "inventory": {"description": "Submit inventory for the given merchant.", "flatPath": "{merchantId}/pos/{targetMerchantId}/inventory", "httpMethod": "POST", "id": "content.pos.inventory", "parameterOrder": ["merchantId", "targetMerchantId"], "parameters": {"dryRun": {"description": "Flag to simulate a request like in a live environment. If set to true, dry-run mode checks the validity of the request and returns errors (if any).", "location": "query", "type": "boolean"}, "merchantId": {"description": "The ID of the POS or inventory data provider.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "targetMerchantId": {"description": "The ID of the target merchant.", "format": "uint64", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/pos/{targetMerchantId}/inventory", "request": {"$ref": "PosInventoryRequest"}, "response": {"$ref": "PosInventoryResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "list": {"description": "Lists the stores of the target merchant.", "flatPath": "{merchantId}/pos/{targetMerchantId}/store", "httpMethod": "GET", "id": "content.pos.list", "parameterOrder": ["merchantId", "targetMerchantId"], "parameters": {"merchantId": {"description": "The ID of the POS or inventory data provider.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "targetMerchantId": {"description": "The ID of the target merchant.", "format": "uint64", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/pos/{targetMerchantId}/store", "response": {"$ref": "PosListResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "sale": {"description": "Submit a sale event for the given merchant.", "flatPath": "{merchantId}/pos/{targetMerchantId}/sale", "httpMethod": "POST", "id": "content.pos.sale", "parameterOrder": ["merchantId", "targetMerchantId"], "parameters": {"dryRun": {"description": "Flag to simulate a request like in a live environment. If set to true, dry-run mode checks the validity of the request and returns errors (if any).", "location": "query", "type": "boolean"}, "merchantId": {"description": "The ID of the POS or inventory data provider.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "targetMerchantId": {"description": "The ID of the target merchant.", "format": "uint64", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/pos/{targetMerchantId}/sale", "request": {"$ref": "PosSaleRequest"}, "response": {"$ref": "PosSaleResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}, "products": {"methods": {"custombatch": {"description": "Retrieves, inserts, and deletes multiple products in a single request.", "flatPath": "products/batch", "httpMethod": "POST", "id": "content.products.custombatch", "parameterOrder": [], "parameters": {"dryRun": {"description": "Flag to simulate a request like in a live environment. If set to true, dry-run mode checks the validity of the request and returns errors (if any).", "location": "query", "type": "boolean"}}, "path": "products/batch", "request": {"$ref": "ProductsCustomBatchRequest"}, "response": {"$ref": "ProductsCustomBatchResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "delete": {"description": "Deletes a product from your Merchant Center account.", "flatPath": "{merchantId}/products/{productId}", "httpMethod": "DELETE", "id": "content.products.delete", "parameterOrder": ["merchantId", "productId"], "parameters": {"dryRun": {"description": "Flag to simulate a request like in a live environment. If set to true, dry-run mode checks the validity of the request and returns errors (if any).", "location": "query", "type": "boolean"}, "merchantId": {"description": "The ID of the account that contains the product. This account cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "productId": {"description": "The REST ID of the product.", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/products/{productId}", "scopes": ["https://www.googleapis.com/auth/content"]}, "get": {"description": "Retrieves a product from your Merchant Center account.", "flatPath": "{merchantId}/products/{productId}", "httpMethod": "GET", "id": "content.products.get", "parameterOrder": ["merchantId", "productId"], "parameters": {"merchantId": {"description": "The ID of the account that contains the product. This account cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "productId": {"description": "The REST ID of the product.", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/products/{productId}", "response": {"$ref": "Product"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "insert": {"description": "Uploads a product to your Merchant Center account. If an item with the same channel, contentLanguage, offerId, and targetCountry already exists, this method updates that entry.", "flatPath": "{merchantId}/products", "httpMethod": "POST", "id": "content.products.insert", "parameterOrder": ["merchantId"], "parameters": {"dryRun": {"description": "Flag to simulate a request like in a live environment. If set to true, dry-run mode checks the validity of the request and returns errors (if any).", "location": "query", "type": "boolean"}, "merchantId": {"description": "The ID of the account that contains the product. This account cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/products", "request": {"$ref": "Product"}, "response": {"$ref": "Product"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "list": {"description": "Lists the products in your Merchant Center account. The response might contain fewer items than specified by maxResults. Rely on nextPageToken to determine if there are more items to be requested.", "flatPath": "{merchantId}/products", "httpMethod": "GET", "id": "content.products.list", "parameterOrder": ["merchantId"], "parameters": {"includeInvalidInsertedItems": {"description": "Flag to include the invalid inserted items in the result of the list request. By default the invalid items are not shown (the default value is false).", "location": "query", "type": "boolean"}, "maxResults": {"description": "The maximum number of products to return in the response, used for paging.", "format": "uint32", "location": "query", "type": "integer"}, "merchantId": {"description": "The ID of the account that contains the products. This account cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "pageToken": {"description": "The token returned by the previous request.", "location": "query", "type": "string"}}, "path": "{merchantId}/products", "response": {"$ref": "ProductsListResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}, "productstatuses": {"methods": {"custombatch": {"description": "Gets the statuses of multiple products in a single request.", "flatPath": "productstatuses/batch", "httpMethod": "POST", "id": "content.productstatuses.custombatch", "parameterOrder": [], "parameters": {"includeAttributes": {"description": "Flag to include full product data in the results of this request. The default value is false.", "location": "query", "type": "boolean"}}, "path": "productstatuses/batch", "request": {"$ref": "ProductstatusesCustomBatchRequest"}, "response": {"$ref": "ProductstatusesCustomBatchResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "get": {"description": "Gets the status of a product from your Merchant Center account.", "flatPath": "{merchantId}/productstatuses/{productId}", "httpMethod": "GET", "id": "content.productstatuses.get", "parameterOrder": ["merchantId", "productId"], "parameters": {"destinations": {"description": "If set, only issues for the specified destinations are returned, otherwise only issues for the Shopping destination.", "location": "query", "repeated": true, "type": "string"}, "includeAttributes": {"description": "Flag to include full product data in the result of this get request. The default value is false.", "location": "query", "type": "boolean"}, "merchantId": {"description": "The ID of the account that contains the product. This account cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "productId": {"description": "The REST ID of the product.", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/productstatuses/{productId}", "response": {"$ref": "ProductStatus"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "list": {"description": "Lists the statuses of the products in your Merchant Center account.", "flatPath": "{merchantId}/productstatuses", "httpMethod": "GET", "id": "content.productstatuses.list", "parameterOrder": ["merchantId"], "parameters": {"destinations": {"description": "If set, only issues for the specified destinations are returned, otherwise only issues for the Shopping destination.", "location": "query", "repeated": true, "type": "string"}, "includeAttributes": {"description": "Flag to include full product data in the results of the list request. The default value is false.", "location": "query", "type": "boolean"}, "includeInvalidInsertedItems": {"description": "Flag to include the invalid inserted items in the result of the list request. By default the invalid items are not shown (the default value is false).", "location": "query", "type": "boolean"}, "maxResults": {"description": "The maximum number of product statuses to return in the response, used for paging.", "format": "uint32", "location": "query", "type": "integer"}, "merchantId": {"description": "The ID of the account that contains the products. This account cannot be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "pageToken": {"description": "The token returned by the previous request.", "location": "query", "type": "string"}}, "path": "{merchantId}/productstatuses", "response": {"$ref": "ProductstatusesListResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}, "shippingsettings": {"methods": {"custombatch": {"description": "Retrieves and updates the shipping settings of multiple accounts in a single request.", "flatPath": "shippingsettings/batch", "httpMethod": "POST", "id": "content.shippingsettings.custombatch", "parameterOrder": [], "parameters": {"dryRun": {"description": "Flag to simulate a request like in a live environment. If set to true, dry-run mode checks the validity of the request and returns errors (if any).", "location": "query", "type": "boolean"}}, "path": "shippingsettings/batch", "request": {"$ref": "ShippingsettingsCustomBatchRequest"}, "response": {"$ref": "ShippingsettingsCustomBatchResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "get": {"description": "Retrieves the shipping settings of the account.", "flatPath": "{merchantId}/shippingsettings/{accountId}", "httpMethod": "GET", "id": "content.shippingsettings.get", "parameterOrder": ["merchantId", "accountId"], "parameters": {"accountId": {"description": "The ID of the account for which to get/update shipping settings.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "merchantId": {"description": "The ID of the managing account. If this parameter is not the same as accountId, then this account must be a multi-client account and `accountId` must be the ID of a sub-account of this account.", "format": "uint64", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/shippingsettings/{accountId}", "response": {"$ref": "ShippingSettings"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "getsupportedcarriers": {"description": "Retrieves supported carriers and carrier services for an account.", "flatPath": "{merchantId}/supportedCarriers", "httpMethod": "GET", "id": "content.shippingsettings.getsupportedcarriers", "parameterOrder": ["merchantId"], "parameters": {"merchantId": {"description": "The ID of the account for which to retrieve the supported carriers.", "format": "uint64", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/supportedCarriers", "response": {"$ref": "ShippingsettingsGetSupportedCarriersResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "getsupportedholidays": {"description": "Retrieves supported holidays for an account.", "flatPath": "{merchantId}/supportedHolidays", "httpMethod": "GET", "id": "content.shippingsettings.getsupportedholidays", "parameterOrder": ["merchantId"], "parameters": {"merchantId": {"description": "The ID of the account for which to retrieve the supported holidays.", "format": "uint64", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/supportedHolidays", "response": {"$ref": "ShippingsettingsGetSupportedHolidaysResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "getsupportedpickupservices": {"description": "Retrieves supported pickup services for an account.", "flatPath": "{merchantId}/supportedPickupServices", "httpMethod": "GET", "id": "content.shippingsettings.getsupportedpickupservices", "parameterOrder": ["merchantId"], "parameters": {"merchantId": {"description": "The ID of the account for which to retrieve the supported pickup services.", "format": "uint64", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/supportedPickupServices", "response": {"$ref": "ShippingsettingsGetSupportedPickupServicesResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "list": {"description": "Lists the shipping settings of the sub-accounts in your Merchant Center account.", "flatPath": "{merchantId}/shippingsettings", "httpMethod": "GET", "id": "content.shippingsettings.list", "parameterOrder": ["merchantId"], "parameters": {"maxResults": {"description": "The maximum number of shipping settings to return in the response, used for paging.", "format": "uint32", "location": "query", "type": "integer"}, "merchantId": {"description": "The ID of the managing account. This must be a multi-client account.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "pageToken": {"description": "The token returned by the previous request.", "location": "query", "type": "string"}}, "path": "{merchantId}/shippingsettings", "response": {"$ref": "ShippingsettingsListResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "update": {"description": "Updates the shipping settings of the account. Any fields that are not provided are deleted from the resource.", "flatPath": "{merchantId}/shippingsettings/{accountId}", "httpMethod": "PUT", "id": "content.shippingsettings.update", "parameterOrder": ["merchantId", "accountId"], "parameters": {"accountId": {"description": "The ID of the account for which to get/update shipping settings.", "format": "uint64", "location": "path", "required": true, "type": "string"}, "dryRun": {"description": "Flag to simulate a request like in a live environment. If set to true, dry-run mode checks the validity of the request and returns errors (if any).", "location": "query", "type": "boolean"}, "merchantId": {"description": "The ID of the managing account. If this parameter is not the same as accountId, then this account must be a multi-client account and `accountId` must be the ID of a sub-account of this account.", "format": "uint64", "location": "path", "required": true, "type": "string"}}, "path": "{merchantId}/shippingsettings/{accountId}", "request": {"$ref": "ShippingSettings"}, "response": {"$ref": "ShippingSettings"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}}, "revision": "********", "rootUrl": "https://shoppingcontent.googleapis.com/", "schemas": {"Account": {"description": "Account data. After the creation of a new account it may take a few minutes before it is fully operational. The methods delete, insert, and update require the admin role.", "id": "Account", "properties": {"adultContent": {"description": "Indicates whether the merchant sells adult content.", "type": "boolean"}, "adwordsLinks": {"description": "List of linked AdWords accounts that are active or pending approval. To create a new link request, add a new link with status `active` to the list. It will remain in a `pending` state until approved or rejected either in the AdWords interface or through the AdWords API. To delete an active link, or to cancel a link request, remove it from the list.", "items": {"$ref": "AccountAdwordsLink"}, "type": "array"}, "businessInformation": {"$ref": "AccountBusinessInformation", "description": "The business information of the account."}, "googleMyBusinessLink": {"$ref": "AccountGoogleMyBusinessLink", "description": "The GMB account which is linked or in the process of being linked with the Merchant Center account."}, "id": {"description": "Required for update. Merchant Center account ID.", "format": "uint64", "type": "string"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"`content#account`\"", "type": "string"}, "name": {"description": "Required. Display name for the account.", "type": "string"}, "reviewsUrl": {"description": "[DEPRECATED] This field is never returned and will be ignored if provided.", "type": "string"}, "sellerId": {"description": "Client-specific, locally-unique, internal ID for the child account.", "type": "string"}, "users": {"description": "Users with access to the account. Every account (except for subaccounts) must have at least one admin user.", "items": {"$ref": "Account<PERSON>ser"}, "type": "array"}, "websiteUrl": {"description": "The merchant's website.", "type": "string"}, "youtubeChannelLinks": {"description": "List of linked YouTube channels that are active or pending approval. To create a new link request, add a new link with status `active` to the list. It will remain in a `pending` state until approved or rejected in the YT Creator Studio interface. To delete an active link, or to cancel a link request, remove it from the list.", "items": {"$ref": "AccountYouTubeChannelLink"}, "type": "array"}}, "type": "object"}, "AccountAddress": {"id": "Account<PERSON><PERSON><PERSON>", "properties": {"country": {"description": "CLDR country code (e.g. \"US\"). This value cannot be set for a sub-account of an MCA. All MCA sub-accounts inherit the country of their parent MCA.", "type": "string"}, "locality": {"description": "City, town or commune. May also include dependent localities or sublocalities (e.g. neighborhoods or suburbs).", "type": "string"}, "postalCode": {"description": "Postal code or ZIP (e.g. \"94043\").", "type": "string"}, "region": {"description": "Top-level administrative subdivision of the country. For example, a state like California (\"CA\") or a province like Quebec (\"QC\").", "type": "string"}, "streetAddress": {"description": "Street-level part of the address.", "type": "string"}}, "type": "object"}, "AccountAdwordsLink": {"id": "AccountAdwordsLink", "properties": {"adwordsId": {"description": "Customer ID of the AdWords account.", "format": "uint64", "type": "string"}, "status": {"description": "Status of the link between this Merchant Center account and the AdWords account. Upon retrieval, it represents the actual status of the link and can be either `active` if it was approved in Google AdWords or `pending` if it's pending approval. Upon insertion, it represents the *intended* status of the link. Re-uploading a link with status `active` when it's still pending or with status `pending` when it's already active will have no effect: the status will remain unchanged. Re-uploading a link with deprecated status `inactive` is equivalent to not submitting the link at all and will delete the link if it was active or cancel the link request if it was pending. Acceptable values are: - \"`active`\" - \"`pending`\" ", "type": "string"}}, "type": "object"}, "AccountBusinessInformation": {"id": "AccountBusinessInformation", "properties": {"address": {"$ref": "Account<PERSON><PERSON><PERSON>", "description": "The address of the business."}, "customerService": {"$ref": "AccountCustomerService", "description": "The customer service information of the business."}, "koreanBusinessRegistrationNumber": {"description": "The 10-digit [Korean business registration number](https://support.google.com/merchants/answer/9037766) separated with dashes in the format: XXX-XX-XXXXX. This field will only be updated if explicitly set.", "type": "string"}, "phoneNumber": {"description": "The phone number of the business.", "type": "string"}}, "type": "object"}, "AccountCustomerService": {"id": "AccountCustomerService", "properties": {"email": {"description": "Customer service email.", "type": "string"}, "phoneNumber": {"description": "Customer service phone number.", "type": "string"}, "url": {"description": "Customer service URL.", "type": "string"}}, "type": "object"}, "AccountGoogleMyBusinessLink": {"id": "AccountGoogleMyBusinessLink", "properties": {"gmbEmail": {"description": "The GMB email address of which a specific account within a GMB account. A sample account within a GMB account could be a business account with set of locations, managed under the GMB account.", "type": "string"}, "status": {"description": "Status of the link between this Merchant Center account and the GMB account. Acceptable values are: - \"`active`\" - \"`pending`\" ", "type": "string"}}, "type": "object"}, "AccountIdentifier": {"id": "AccountIdentifier", "properties": {"aggregatorId": {"description": "The aggregator ID, set for aggregators and subaccounts (in that case, it represents the aggregator of the subaccount).", "format": "uint64", "type": "string"}, "merchantId": {"description": "The merchant account ID, set for individual accounts and subaccounts.", "format": "uint64", "type": "string"}}, "type": "object"}, "AccountStatus": {"description": "The status of an account, i.e., information about its products, which is computed offline and not returned immediately at insertion time.", "id": "Account<PERSON><PERSON><PERSON>", "properties": {"accountId": {"description": "The ID of the account for which the status is reported.", "type": "string"}, "accountLevelIssues": {"description": "A list of account level issues.", "items": {"$ref": "AccountStatusAccountLevelIssue"}, "type": "array"}, "dataQualityIssues": {"description": "DEPRECATED - never populated.", "items": {"$ref": "AccountStatusDataQualityIssue"}, "type": "array"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"`content#accountStatus`\"", "type": "string"}, "products": {"description": "List of product-related data by channel, destination, and country. Data in this field may be delayed by up to 30 minutes.", "items": {"$ref": "AccountStatusProducts"}, "type": "array"}, "websiteClaimed": {"description": "Whether the account's website is claimed or not.", "type": "boolean"}}, "type": "object"}, "AccountStatusAccountLevelIssue": {"id": "AccountStatusAccountLevelIssue", "properties": {"country": {"description": "Country for which this issue is reported.", "type": "string"}, "destination": {"description": "The destination the issue applies to. If this field is empty then the issue applies to all available destinations.", "type": "string"}, "detail": {"description": "Additional details about the issue.", "type": "string"}, "documentation": {"description": "The URL of a web page to help resolving this issue.", "type": "string"}, "id": {"description": "Issue identifier.", "type": "string"}, "severity": {"description": "Severity of the issue. Acceptable values are: - \"`critical`\" - \"`error`\" - \"`suggestion`\" ", "type": "string"}, "title": {"description": "Short description of the issue.", "type": "string"}}, "type": "object"}, "AccountStatusDataQualityIssue": {"id": "AccountStatusDataQualityIssue", "properties": {"country": {"type": "string"}, "destination": {"type": "string"}, "detail": {"type": "string"}, "displayedValue": {"type": "string"}, "exampleItems": {"items": {"$ref": "AccountStatusExampleItem"}, "type": "array"}, "id": {"type": "string"}, "lastChecked": {"type": "string"}, "location": {"type": "string"}, "numItems": {"format": "uint32", "type": "integer"}, "severity": {"description": " Acceptable values are: - \"`critical`\" - \"`error`\" - \"`suggestion`\" ", "type": "string"}, "submittedValue": {"type": "string"}}, "type": "object"}, "AccountStatusExampleItem": {"id": "AccountStatusExampleItem", "properties": {"itemId": {"type": "string"}, "link": {"type": "string"}, "submittedValue": {"type": "string"}, "title": {"type": "string"}, "valueOnLandingPage": {"type": "string"}}, "type": "object"}, "AccountStatusItemLevelIssue": {"id": "AccountStatusItemLevelIssue", "properties": {"attributeName": {"description": "The attribute's name, if the issue is caused by a single attribute.", "type": "string"}, "code": {"description": "The error code of the issue.", "type": "string"}, "description": {"description": "A short issue description in English.", "type": "string"}, "detail": {"description": "A detailed issue description in English.", "type": "string"}, "documentation": {"description": "The URL of a web page to help with resolving this issue.", "type": "string"}, "numItems": {"description": "Number of items with this issue.", "format": "int64", "type": "string"}, "resolution": {"description": "Whether the issue can be resolved by the merchant.", "type": "string"}, "servability": {"description": "How this issue affects serving of the offer.", "type": "string"}}, "type": "object"}, "AccountStatusProducts": {"id": "AccountStatusProducts", "properties": {"channel": {"description": "The channel the data applies to. Acceptable values are: - \"`local`\" - \"`online`\" ", "type": "string"}, "country": {"description": "The country the data applies to.", "type": "string"}, "destination": {"description": "The destination the data applies to.", "type": "string"}, "itemLevelIssues": {"description": "List of item-level issues.", "items": {"$ref": "AccountStatusItemLevelIssue"}, "type": "array"}, "statistics": {"$ref": "AccountStatusStatistics", "description": "Aggregated product statistics."}}, "type": "object"}, "AccountStatusStatistics": {"id": "AccountStatusStatistics", "properties": {"active": {"description": "Number of active offers.", "format": "int64", "type": "string"}, "disapproved": {"description": "Number of disapproved offers.", "format": "int64", "type": "string"}, "expiring": {"description": "Number of expiring offers.", "format": "int64", "type": "string"}, "pending": {"description": "Number of pending offers.", "format": "int64", "type": "string"}}, "type": "object"}, "AccountTax": {"description": "The tax settings of a merchant account. All methods require the admin role.", "id": "AccountTax", "properties": {"accountId": {"description": "Required. The ID of the account to which these account tax settings belong.", "format": "uint64", "type": "string"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#accountTax\".", "type": "string"}, "rules": {"description": "Tax rules. Updating the tax rules will enable US taxes (not reversible). Defining no rules is equivalent to not charging tax at all.", "items": {"$ref": "AccountTaxTaxRule"}, "type": "array"}}, "type": "object"}, "AccountTaxTaxRule": {"description": "Tax calculation rule to apply in a state or province (USA only).", "id": "AccountTaxTaxRule", "properties": {"country": {"description": "Country code in which tax is applicable.", "type": "string"}, "locationId": {"description": "Required. State (or province) is which the tax is applicable, described by its location ID (also called criteria ID).", "format": "uint64", "type": "string"}, "ratePercent": {"description": "Explicit tax rate in percent, represented as a floating point number without the percentage character. Must not be negative.", "type": "string"}, "shippingTaxed": {"description": "If true, shipping charges are also taxed.", "type": "boolean"}, "useGlobalRate": {"description": "Whether the tax rate is taken from a global tax table or specified explicitly.", "type": "boolean"}}, "type": "object"}, "AccountUser": {"id": "Account<PERSON>ser", "properties": {"admin": {"description": "Whether user is an admin.", "type": "boolean"}, "emailAddress": {"description": "User's email address.", "type": "string"}, "orderManager": {"description": "Whether user is an order manager.", "type": "boolean"}, "paymentsAnalyst": {"description": "Whether user can access payment statements.", "type": "boolean"}, "paymentsManager": {"description": "Whether user can manage payment settings.", "type": "boolean"}}, "type": "object"}, "AccountYouTubeChannelLink": {"id": "AccountYouTubeChannelLink", "properties": {"channelId": {"description": "Channel ID.", "type": "string"}, "status": {"description": "Status of the link between this Merchant Center account and the YouTube channel. Upon retrieval, it represents the actual status of the link and can be either `active` if it was approved in YT Creator Studio or `pending` if it's pending approval. Upon insertion, it represents the *intended* status of the link. Re-uploading a link with status `active` when it's still pending or with status `pending` when it's already active will have no effect: the status will remain unchanged. Re-uploading a link with deprecated status `inactive` is equivalent to not submitting the link at all and will delete the link if it was active or cancel the link request if it was pending.", "type": "string"}}, "type": "object"}, "AccountsAuthInfoResponse": {"id": "AccountsAuthInfoResponse", "properties": {"accountIdentifiers": {"description": "The account identifiers corresponding to the authenticated user. - For an individual account: only the merchant ID is defined - For an aggregator: only the aggregator ID is defined - For a subaccount of an MCA: both the merchant ID and the aggregator ID are defined. ", "items": {"$ref": "AccountIdentifier"}, "type": "array"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#accountsAuthInfoResponse\".", "type": "string"}}, "type": "object"}, "AccountsClaimWebsiteResponse": {"id": "AccountsClaimWebsiteResponse", "properties": {"kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#accountsClaimWebsiteResponse\".", "type": "string"}}, "type": "object"}, "AccountsCustomBatchRequest": {"id": "AccountsCustomBatchRequest", "properties": {"entries": {"description": "The request entries to be processed in the batch.", "items": {"$ref": "AccountsCustomBatchRequestEntry"}, "type": "array"}}, "type": "object"}, "AccountsCustomBatchRequestEntry": {"description": "A batch entry encoding a single non-batch accounts request.", "id": "AccountsCustomBatchRequestEntry", "properties": {"account": {"$ref": "Account", "description": "The account to create or update. Only defined if the method is `insert` or `update`."}, "accountId": {"description": "The ID of the targeted account. Only defined if the method is not `insert`.", "format": "uint64", "type": "string"}, "batchId": {"description": "An entry ID, unique within the batch request.", "format": "uint32", "type": "integer"}, "force": {"description": "Whether the account should be deleted if the account has offers. Only applicable if the method is `delete`.", "type": "boolean"}, "labelIds": {"description": "Label IDs for the 'updatelabels' request.", "items": {"format": "uint64", "type": "string"}, "type": "array"}, "linkRequest": {"$ref": "AccountsCustomBatchRequestEntryLinkRequest", "description": "Details about the `link` request."}, "merchantId": {"description": "The ID of the managing account.", "format": "uint64", "type": "string"}, "method": {"description": "The method of the batch entry. Acceptable values are: - \"`claimWebsite`\" - \"`delete`\" - \"`get`\" - \"`insert`\" - \"`link`\" - \"`update`\" ", "type": "string"}, "overwrite": {"description": "Only applicable if the method is `claimwebsite`. Indicates whether or not to take the claim from another account in case there is a conflict.", "type": "boolean"}}, "type": "object"}, "AccountsCustomBatchRequestEntryLinkRequest": {"id": "AccountsCustomBatchRequestEntryLinkRequest", "properties": {"action": {"description": "Action to perform for this link. The `\"request\"` action is only available to select merchants. Acceptable values are: - \"`approve`\" - \"`remove`\" - \"`request`\" ", "type": "string"}, "linkType": {"description": "Type of the link between the two accounts. Acceptable values are: - \"`channelPartner`\" - \"`eCommercePlatform`\" ", "type": "string"}, "linkedAccountId": {"description": "The ID of the linked account.", "type": "string"}}, "type": "object"}, "AccountsCustomBatchResponse": {"id": "AccountsCustomBatchResponse", "properties": {"entries": {"description": "The result of the execution of the batch requests.", "items": {"$ref": "AccountsCustomBatchResponseEntry"}, "type": "array"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#accountsCustomBatchResponse\".", "type": "string"}}, "type": "object"}, "AccountsCustomBatchResponseEntry": {"description": "A batch entry encoding a single non-batch accounts response.", "id": "AccountsCustomBatchResponseEntry", "properties": {"account": {"$ref": "Account", "description": "The retrieved, created, or updated account. Not defined if the method was `delete`, `claimwebsite` or `link`."}, "batchId": {"description": "The ID of the request entry this entry responds to.", "format": "uint32", "type": "integer"}, "errors": {"$ref": "Errors", "description": "A list of errors defined if and only if the request failed."}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"`content#accountsCustomBatchResponseEntry`\"", "type": "string"}, "linkStatus": {"description": "Deprecated. This field is never set. Acceptable values are: - \"`active`\" - \"`inactive`\" - \"`pending`\" ", "type": "string"}}, "type": "object"}, "AccountsLinkRequest": {"id": "AccountsLinkRequest", "properties": {"action": {"description": "Action to perform for this link. The `\"request\"` action is only available to select merchants. Acceptable values are: - \"`approve`\" - \"`remove`\" - \"`request`\" ", "type": "string"}, "linkType": {"description": "Type of the link between the two accounts. Acceptable values are: - \"`channelPartner`\" - \"`eCommercePlatform`\" ", "type": "string"}, "linkedAccountId": {"description": "The ID of the linked account.", "type": "string"}}, "type": "object"}, "AccountsLinkResponse": {"id": "AccountsLinkResponse", "properties": {"kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#accountsLinkResponse\".", "type": "string"}}, "type": "object"}, "AccountsListResponse": {"id": "AccountsListResponse", "properties": {"kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#accountsListResponse\".", "type": "string"}, "nextPageToken": {"description": "The token for the retrieval of the next page of accounts.", "type": "string"}, "resources": {"items": {"$ref": "Account"}, "type": "array"}}, "type": "object"}, "AccountstatusesCustomBatchRequest": {"id": "AccountstatusesCustomBatchRequest", "properties": {"entries": {"description": "The request entries to be processed in the batch.", "items": {"$ref": "AccountstatusesCustomBatchRequestEntry"}, "type": "array"}}, "type": "object"}, "AccountstatusesCustomBatchRequestEntry": {"description": "A batch entry encoding a single non-batch accountstatuses request.", "id": "AccountstatusesCustomBatchRequestEntry", "properties": {"accountId": {"description": "The ID of the (sub-)account whose status to get.", "format": "uint64", "type": "string"}, "batchId": {"description": "An entry ID, unique within the batch request.", "format": "uint32", "type": "integer"}, "destinations": {"description": "If set, only issues for the specified destinations are returned, otherwise only issues for the Shopping destination.", "items": {"type": "string"}, "type": "array"}, "merchantId": {"description": "The ID of the managing account.", "format": "uint64", "type": "string"}, "method": {"description": "The method of the batch entry. Acceptable values are: - \"`get`\" ", "type": "string"}}, "type": "object"}, "AccountstatusesCustomBatchResponse": {"id": "AccountstatusesCustomBatchResponse", "properties": {"entries": {"description": "The result of the execution of the batch requests.", "items": {"$ref": "AccountstatusesCustomBatchResponseEntry"}, "type": "array"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#accountstatusesCustomBatchResponse\".", "type": "string"}}, "type": "object"}, "AccountstatusesCustomBatchResponseEntry": {"description": "A batch entry encoding a single non-batch accountstatuses response.", "id": "AccountstatusesCustomBatchResponseEntry", "properties": {"accountStatus": {"$ref": "Account<PERSON><PERSON><PERSON>", "description": "The requested account status. Defined if and only if the request was successful."}, "batchId": {"description": "The ID of the request entry this entry responds to.", "format": "uint32", "type": "integer"}, "errors": {"$ref": "Errors", "description": "A list of errors defined if and only if the request failed."}}, "type": "object"}, "AccountstatusesListResponse": {"id": "AccountstatusesListResponse", "properties": {"kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#accountstatusesListResponse\".", "type": "string"}, "nextPageToken": {"description": "The token for the retrieval of the next page of account statuses.", "type": "string"}, "resources": {"items": {"$ref": "Account<PERSON><PERSON><PERSON>"}, "type": "array"}}, "type": "object"}, "AccounttaxCustomBatchRequest": {"id": "AccounttaxCustomBatchRequest", "properties": {"entries": {"description": "The request entries to be processed in the batch.", "items": {"$ref": "AccounttaxCustomBatchRequestEntry"}, "type": "array"}}, "type": "object"}, "AccounttaxCustomBatchRequestEntry": {"description": "A batch entry encoding a single non-batch accounttax request.", "id": "AccounttaxCustomBatchRequestEntry", "properties": {"accountId": {"description": "The ID of the account for which to get/update account tax settings.", "format": "uint64", "type": "string"}, "accountTax": {"$ref": "AccountTax", "description": "The account tax settings to update. Only defined if the method is `update`."}, "batchId": {"description": "An entry ID, unique within the batch request.", "format": "uint32", "type": "integer"}, "merchantId": {"description": "The ID of the managing account.", "format": "uint64", "type": "string"}, "method": {"description": "The method of the batch entry. Acceptable values are: - \"`get`\" - \"`update`\" ", "type": "string"}}, "type": "object"}, "AccounttaxCustomBatchResponse": {"id": "AccounttaxCustomBatchResponse", "properties": {"entries": {"description": "The result of the execution of the batch requests.", "items": {"$ref": "AccounttaxCustomBatchResponseEntry"}, "type": "array"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#accounttaxCustomBatchResponse\".", "type": "string"}}, "type": "object"}, "AccounttaxCustomBatchResponseEntry": {"description": "A batch entry encoding a single non-batch accounttax response.", "id": "AccounttaxCustomBatchResponseEntry", "properties": {"accountTax": {"$ref": "AccountTax", "description": "The retrieved or updated account tax settings."}, "batchId": {"description": "The ID of the request entry this entry responds to.", "format": "uint32", "type": "integer"}, "errors": {"$ref": "Errors", "description": "A list of errors defined if and only if the request failed."}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"`content#accounttaxCustomBatchResponseEntry`\"", "type": "string"}}, "type": "object"}, "AccounttaxListResponse": {"id": "AccounttaxListResponse", "properties": {"kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#accounttaxListResponse\".", "type": "string"}, "nextPageToken": {"description": "The token for the retrieval of the next page of account tax settings.", "type": "string"}, "resources": {"items": {"$ref": "AccountTax"}, "type": "array"}}, "type": "object"}, "Address": {"id": "Address", "properties": {"administrativeArea": {"description": "Required. Top-level administrative subdivision of the country. For example, a state like California (\"CA\") or a province like Quebec (\"QC\").", "type": "string"}, "city": {"description": "Required. City, town or commune. May also include dependent localities or sublocalities (e.g. neighborhoods or suburbs).", "type": "string"}, "country": {"description": "Required. [CLDR country code](http://www.unicode.org/repos/cldr/tags/latest/common/main/en.xml)(e.g. \"US\").", "type": "string"}, "postalCode": {"description": "Required. Postal code or ZIP (e.g. \"94043\"). Required.", "type": "string"}, "streetAddress": {"description": "Street-level part of the address.", "type": "string"}}, "type": "object"}, "Amount": {"id": "Amount", "properties": {"pretax": {"$ref": "Price", "description": "[required] Value before taxes."}, "tax": {"$ref": "Price", "description": "[required] Tax value."}}, "type": "object"}, "BusinessDayConfig": {"id": "BusinessDayConfig", "properties": {"businessDays": {"description": "Regular business days, such as '\"monday\"'. May not be empty.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "CarrierRate": {"id": "CarrierRate", "properties": {"carrierName": {"description": "Carrier service, such as `\"UPS\"` or `\"Fedex\"`. The list of supported carriers can be retrieved via the `getSupportedCarriers` method. Required.", "type": "string"}, "carrierService": {"description": "Carrier service, such as `\"ground\"` or `\"2 days\"`. The list of supported services for a carrier can be retrieved via the `getSupportedCarriers` method. Required.", "type": "string"}, "flatAdjustment": {"$ref": "Price", "description": "Additive shipping rate modifier. Can be negative. For example `{ \"value\": \"1\", \"currency\" : \"USD\" }` adds $1 to the rate, `{ \"value\": \"-3\", \"currency\" : \"USD\" }` removes $3 from the rate. Optional."}, "name": {"description": "Name of the carrier rate. Must be unique per rate group. Required.", "type": "string"}, "originPostalCode": {"description": "Shipping origin for this carrier rate. Required.", "type": "string"}, "percentageAdjustment": {"description": "Multiplicative shipping rate modifier as a number in decimal notation. Can be negative. For example `\"5.4\"` increases the rate by 5.4%, `\"-3\"` decreases the rate by 3%. Optional.", "type": "string"}}, "type": "object"}, "CarriersCarrier": {"id": "CarriersCarrier", "properties": {"country": {"description": "The CLDR country code of the carrier (e.g., \"US\"). Always present.", "type": "string"}, "eddServices": {"description": "A list of services supported for EDD (Estimated Delivery Date) calculation. This is the list of valid values for WarehouseBasedDeliveryTime.carrierService.", "items": {"type": "string"}, "type": "array"}, "name": {"description": "The name of the carrier (e.g., `\"UPS\"`). Always present.", "type": "string"}, "services": {"description": "A list of supported services (e.g., `\"ground\"`) for that carrier. Contains at least one service. This is the list of valid values for CarrierRate.carrierService.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "CustomAttribute": {"id": "CustomAttribute", "properties": {"name": {"description": "The name of the attribute. Underscores will be replaced by spaces upon insertion.", "type": "string"}, "type": {"description": "The type of the attribute. Acceptable values are: - \"`boolean`\" - \"`datetimerange`\" - \"`float`\" - \"`group`\" - \"`int`\" - \"`price`\" - \"`text`\" - \"`time`\" - \"`url`\" ", "type": "string"}, "unit": {"description": "Free-form unit of the attribute. Unit can only be used for values of type int, float, or price.", "type": "string"}, "value": {"description": "The value of the attribute.", "type": "string"}}, "type": "object"}, "CustomGroup": {"id": "CustomGroup", "properties": {"attributes": {"description": "The sub-attributes.", "items": {"$ref": "CustomAttribute"}, "type": "array"}, "name": {"description": "The name of the group. Underscores will be replaced by spaces upon insertion.", "type": "string"}}, "type": "object"}, "CustomerReturnReason": {"id": "CustomerReturnReason", "properties": {"description": {"description": "Description of the reason.", "type": "string"}, "reasonCode": {"description": "Code of the return reason. Acceptable values are: - \"`betterPriceFound`\" - \"`changedMind`\" - \"`damagedOrDefectiveItem`\" - \"`didNotMatchDescription`\" - \"`doesNotFit`\" - \"`expiredItem`\" - \"`incorrectItemReceived`\" - \"`noLongerNeeded`\" - \"`notSpecified`\" - \"`orderedWrongItem`\" - \"`other`\" - \"`qualityNotExpected`\" - \"`receivedTooLate`\" - \"`undeliverable`\" ", "type": "string"}}, "type": "object"}, "CutoffTime": {"id": "CutoffTime", "properties": {"hour": {"description": "Hour of the cutoff time until which an order has to be placed to be processed in the same day. Required.", "format": "uint32", "type": "integer"}, "minute": {"description": "Minute of the cutoff time until which an order has to be placed to be processed in the same day. Required.", "format": "uint32", "type": "integer"}, "timezone": {"description": "Timezone identifier for the cutoff time. A list of identifiers can be found in the AdWords API documentation. E.g. \"Europe/Zurich\". Required.", "type": "string"}}, "type": "object"}, "Datafeed": {"description": "Datafeed configuration data.", "id": "Datafeed", "properties": {"attributeLanguage": {"description": "The two-letter ISO 639-1 language in which the attributes are defined in the data feed.", "type": "string"}, "contentLanguage": {"description": "[DEPRECATED] Please use targets[].language instead. The two-letter ISO 639-1 language of the items in the feed. Must be a valid language for `targetCountry`.", "type": "string"}, "contentType": {"description": "Required. The type of data feed. For product inventory feeds, only feeds for local stores, not online stores, are supported. Acceptable values are: - \"`local products`\" - \"`product inventory`\" - \"`products`\" ", "type": "string"}, "fetchSchedule": {"$ref": "DatafeedFetchSchedule", "description": "Fetch schedule for the feed file."}, "fileName": {"description": "Required. The filename of the feed. All feeds must have a unique file name.", "type": "string"}, "format": {"$ref": "DatafeedFormat", "description": "Format of the feed file."}, "id": {"description": "Required for update. The ID of the data feed.", "format": "int64", "type": "string"}, "intendedDestinations": {"description": "[DEPRECATED] Please use targets[].includedDestinations instead. The list of intended destinations (corresponds to checked check boxes in Merchant Center).", "items": {"type": "string"}, "type": "array"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"`content#datafeed`\"", "type": "string"}, "name": {"description": "Required for insert. A descriptive name of the data feed.", "type": "string"}, "targetCountry": {"description": "[DEPRECATED] Please use targets[].country instead. The country where the items in the feed will be included in the search index, represented as a CLDR territory code.", "type": "string"}, "targets": {"description": "The targets this feed should apply to (country, language, destinations).", "items": {"$ref": "DatafeedTarget"}, "type": "array"}}, "type": "object"}, "DatafeedFetchSchedule": {"description": "The required fields vary based on the frequency of fetching. For a monthly fetch schedule, day_of_month and hour are required. For a weekly fetch schedule, weekday and hour are required. For a daily fetch schedule, only hour is required.", "id": "DatafeedFetchSchedule", "properties": {"dayOfMonth": {"description": "The day of the month the feed file should be fetched (1-31).", "format": "uint32", "type": "integer"}, "fetchUrl": {"description": "The URL where the feed file can be fetched. Google Merchant Center will support automatic scheduled uploads using the HTTP, HTTPS, FTP, or SFTP protocols, so the value will need to be a valid link using one of those four protocols.", "type": "string"}, "hour": {"description": "The hour of the day the feed file should be fetched (0-23).", "format": "uint32", "type": "integer"}, "minuteOfHour": {"description": "The minute of the hour the feed file should be fetched (0-59). Read-only.", "format": "uint32", "type": "integer"}, "password": {"description": "An optional password for fetch_url.", "type": "string"}, "paused": {"description": "Whether the scheduled fetch is paused or not.", "type": "boolean"}, "timeZone": {"description": "Time zone used for schedule. UTC by default. E.g., \"America/Los_Angeles\".", "type": "string"}, "username": {"description": "An optional user name for fetch_url.", "type": "string"}, "weekday": {"description": "The day of the week the feed file should be fetched. Acceptable values are: - \"`monday`\" - \"`tuesday`\" - \"`wednesday`\" - \"`thursday`\" - \"`friday`\" - \"`saturday`\" - \"`sunday`\" ", "type": "string"}}, "type": "object"}, "DatafeedFormat": {"id": "DatafeedFormat", "properties": {"columnDelimiter": {"description": "Delimiter for the separation of values in a delimiter-separated values feed. If not specified, the delimiter will be auto-detected. Ignored for non-DSV data feeds. Acceptable values are: - \"`pipe`\" - \"`tab`\" - \"`tilde`\" ", "type": "string"}, "fileEncoding": {"description": "Character encoding scheme of the data feed. If not specified, the encoding will be auto-detected. Acceptable values are: - \"`latin-1`\" - \"`utf-16be`\" - \"`utf-16le`\" - \"`utf-8`\" - \"`windows-1252`\" ", "type": "string"}, "quotingMode": {"description": "Specifies how double quotes are interpreted. If not specified, the mode will be auto-detected. Ignored for non-DSV data feeds. Acceptable values are: - \"`normal character`\" - \"`value quoting`\" ", "type": "string"}}, "type": "object"}, "DatafeedStatus": {"description": "The status of a datafeed, i.e., the result of the last retrieval of the datafeed computed asynchronously when the feed processing is finished.", "id": "DatafeedStatus", "properties": {"country": {"description": "The country for which the status is reported, represented as a CLDR territory code.", "type": "string"}, "datafeedId": {"description": "The ID of the feed for which the status is reported.", "format": "uint64", "type": "string"}, "errors": {"description": "The list of errors occurring in the feed.", "items": {"$ref": "DatafeedStatusError"}, "type": "array"}, "itemsTotal": {"description": "The number of items in the feed that were processed.", "format": "uint64", "type": "string"}, "itemsValid": {"description": "The number of items in the feed that were valid.", "format": "uint64", "type": "string"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"`content#datafeedStatus`\"", "type": "string"}, "language": {"description": "The two-letter ISO 639-1 language for which the status is reported.", "type": "string"}, "lastUploadDate": {"description": "The last date at which the feed was uploaded.", "type": "string"}, "processingStatus": {"description": "The processing status of the feed. Acceptable values are: - \"`\"`failure`\": The feed could not be processed or all items had errors.`\" - \"`in progress`\": The feed is being processed. - \"`none`\": The feed has not yet been processed. For example, a feed that has never been uploaded will have this processing status. - \"`success`\": The feed was processed successfully, though some items might have had errors. ", "type": "string"}, "warnings": {"description": "The list of errors occurring in the feed.", "items": {"$ref": "DatafeedStatusError"}, "type": "array"}}, "type": "object"}, "DatafeedStatusError": {"description": "An error occurring in the feed, like \"invalid price\".", "id": "DatafeedStatusError", "properties": {"code": {"description": "The code of the error, e.g., \"validation/invalid_value\".", "type": "string"}, "count": {"description": "The number of occurrences of the error in the feed.", "format": "uint64", "type": "string"}, "examples": {"description": "A list of example occurrences of the error, grouped by product.", "items": {"$ref": "DatafeedStatusExample"}, "type": "array"}, "message": {"description": "The error message, e.g., \"Invalid price\".", "type": "string"}}, "type": "object"}, "DatafeedStatusExample": {"description": "An example occurrence for a particular error.", "id": "DatafeedStatusExample", "properties": {"itemId": {"description": "The ID of the example item.", "type": "string"}, "lineNumber": {"description": "Line number in the data feed where the example is found.", "format": "uint64", "type": "string"}, "value": {"description": "The problematic value.", "type": "string"}}, "type": "object"}, "DatafeedTarget": {"id": "DatafeedTarget", "properties": {"country": {"description": "The country where the items in the feed will be included in the search index, represented as a CLDR territory code.", "type": "string"}, "excludedDestinations": {"description": "The list of destinations to exclude for this target (corresponds to unchecked check boxes in Merchant Center).", "items": {"type": "string"}, "type": "array"}, "includedDestinations": {"description": "The list of destinations to include for this target (corresponds to checked check boxes in Merchant Center). Default destinations are always included unless provided in `excludedDestinations`. List of supported destinations (if available to the account): - DisplayAds - Shopping - ShoppingActions - SurfacesAcrossGoogle ", "items": {"type": "string"}, "type": "array"}, "language": {"description": "The two-letter ISO 639-1 language of the items in the feed. Must be a valid language for `targets[].country`.", "type": "string"}}, "type": "object"}, "DatafeedsCustomBatchRequest": {"id": "DatafeedsCustomBatchRequest", "properties": {"entries": {"description": "The request entries to be processed in the batch.", "items": {"$ref": "DatafeedsCustomBatchRequestEntry"}, "type": "array"}}, "type": "object"}, "DatafeedsCustomBatchRequestEntry": {"description": "A batch entry encoding a single non-batch datafeeds request.", "id": "DatafeedsCustomBatchRequestEntry", "properties": {"batchId": {"description": "An entry ID, unique within the batch request.", "format": "uint32", "type": "integer"}, "datafeed": {"$ref": "Datafeed", "description": "The data feed to insert."}, "datafeedId": {"description": "The ID of the data feed to get, delete or fetch.", "format": "uint64", "type": "string"}, "merchantId": {"description": "The ID of the managing account.", "format": "uint64", "type": "string"}, "method": {"description": "The method of the batch entry. Acceptable values are: - \"`delete`\" - \"`fetchNow`\" - \"`get`\" - \"`insert`\" - \"`update`\" ", "type": "string"}}, "type": "object"}, "DatafeedsCustomBatchResponse": {"id": "DatafeedsCustomBatchResponse", "properties": {"entries": {"description": "The result of the execution of the batch requests.", "items": {"$ref": "DatafeedsCustomBatchResponseEntry"}, "type": "array"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#datafeedsCustomBatchResponse\".", "type": "string"}}, "type": "object"}, "DatafeedsCustomBatchResponseEntry": {"description": "A batch entry encoding a single non-batch datafeeds response.", "id": "DatafeedsCustomBatchResponseEntry", "properties": {"batchId": {"description": "The ID of the request entry this entry responds to.", "format": "uint32", "type": "integer"}, "datafeed": {"$ref": "Datafeed", "description": "The requested data feed. Defined if and only if the request was successful."}, "errors": {"$ref": "Errors", "description": "A list of errors defined if and only if the request failed."}}, "type": "object"}, "DatafeedsFetchNowResponse": {"id": "DatafeedsFetchNowResponse", "properties": {"kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#datafeedsFetchNowResponse\".", "type": "string"}}, "type": "object"}, "DatafeedsListResponse": {"id": "DatafeedsListResponse", "properties": {"kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#datafeedsListResponse\".", "type": "string"}, "nextPageToken": {"description": "The token for the retrieval of the next page of datafeeds.", "type": "string"}, "resources": {"items": {"$ref": "Datafeed"}, "type": "array"}}, "type": "object"}, "DatafeedstatusesCustomBatchRequest": {"id": "DatafeedstatusesCustomBatchRequest", "properties": {"entries": {"description": "The request entries to be processed in the batch.", "items": {"$ref": "DatafeedstatusesCustomBatchRequestEntry"}, "type": "array"}}, "type": "object"}, "DatafeedstatusesCustomBatchRequestEntry": {"description": "A batch entry encoding a single non-batch datafeedstatuses request.", "id": "DatafeedstatusesCustomBatchRequestEntry", "properties": {"batchId": {"description": "An entry ID, unique within the batch request.", "format": "uint32", "type": "integer"}, "country": {"description": "The country for which to get the datafeed status. If this parameter is provided then language must also be provided. Note that for multi-target datafeeds this parameter is required.", "type": "string"}, "datafeedId": {"description": "The ID of the data feed to get.", "format": "uint64", "type": "string"}, "language": {"description": "The language for which to get the datafeed status. If this parameter is provided then country must also be provided. Note that for multi-target datafeeds this parameter is required.", "type": "string"}, "merchantId": {"description": "The ID of the managing account.", "format": "uint64", "type": "string"}, "method": {"description": "The method of the batch entry. Acceptable values are: - \"`get`\" ", "type": "string"}}, "type": "object"}, "DatafeedstatusesCustomBatchResponse": {"id": "DatafeedstatusesCustomBatchResponse", "properties": {"entries": {"description": "The result of the execution of the batch requests.", "items": {"$ref": "DatafeedstatusesCustomBatchResponseEntry"}, "type": "array"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#datafeedstatusesCustomBatchResponse\".", "type": "string"}}, "type": "object"}, "DatafeedstatusesCustomBatchResponseEntry": {"description": "A batch entry encoding a single non-batch datafeedstatuses response.", "id": "DatafeedstatusesCustomBatchResponseEntry", "properties": {"batchId": {"description": "The ID of the request entry this entry responds to.", "format": "uint32", "type": "integer"}, "datafeedStatus": {"$ref": "DatafeedStatus", "description": "The requested data feed status. Defined if and only if the request was successful."}, "errors": {"$ref": "Errors", "description": "A list of errors defined if and only if the request failed."}}, "type": "object"}, "DatafeedstatusesListResponse": {"id": "DatafeedstatusesListResponse", "properties": {"kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#datafeedstatusesListResponse\".", "type": "string"}, "nextPageToken": {"description": "The token for the retrieval of the next page of datafeed statuses.", "type": "string"}, "resources": {"items": {"$ref": "DatafeedStatus"}, "type": "array"}}, "type": "object"}, "DeliveryTime": {"id": "DeliveryTime", "properties": {"cutoffTime": {"$ref": "CutoffTime", "description": "Business days cutoff time definition. If not configured the cutoff time will be defaulted to 8AM PST."}, "handlingBusinessDayConfig": {"$ref": "BusinessDayConfig", "description": "The business days during which orders can be handled. If not provided, Monday to Friday business days will be assumed."}, "holidayCutoffs": {"description": "Holiday cutoff definitions. If configured, they specify order cutoff times for holiday-specific shipping.", "items": {"$ref": "<PERSON><PERSON><PERSON><PERSON>"}, "type": "array"}, "maxHandlingTimeInDays": {"description": "Maximum number of business days spent before an order is shipped. 0 means same day shipped, 1 means next day shipped. Must be greater than or equal to `minHandlingTimeInDays`.", "format": "uint32", "type": "integer"}, "maxTransitTimeInDays": {"description": "Maximum number of business days that is spent in transit. 0 means same day delivery, 1 means next day delivery. Must be greater than or equal to `minTransitTimeInDays`.", "format": "uint32", "type": "integer"}, "minHandlingTimeInDays": {"description": "Minimum number of business days spent before an order is shipped. 0 means same day shipped, 1 means next day shipped.", "format": "uint32", "type": "integer"}, "minTransitTimeInDays": {"description": "Minimum number of business days that is spent in transit. 0 means same day delivery, 1 means next day delivery. Either `{min,max}TransitTimeInDays` or `transitTimeTable` must be set, but not both.", "format": "uint32", "type": "integer"}, "transitBusinessDayConfig": {"$ref": "BusinessDayConfig", "description": "The business days during which orders can be in-transit. If not provided, Monday to Friday business days will be assumed."}, "transitTimeTable": {"$ref": "TransitTable", "description": "Transit time table, number of business days spent in transit based on row and column dimensions. Either `{min,max}TransitTimeInDays` or `transitTimeTable` can be set, but not both."}, "warehouseBasedDeliveryTimes": {"description": "Indicates that the delivery time should be calculated per warehouse (shipping origin location) based on the settings of the selected carrier. When set, no other transit time related field in DeliveryTime should be set.", "items": {"$ref": "WarehouseBasedDeliveryTime"}, "type": "array"}}, "type": "object"}, "Error": {"description": "An error returned by the API.", "id": "Error", "properties": {"domain": {"description": "The domain of the error.", "type": "string"}, "message": {"description": "A description of the error.", "type": "string"}, "reason": {"description": "The error code.", "type": "string"}}, "type": "object"}, "Errors": {"description": "A list of errors returned by a failed batch entry.", "id": "Errors", "properties": {"code": {"description": "The HTTP status of the first error in `errors`.", "format": "uint32", "type": "integer"}, "errors": {"description": "A list of errors.", "items": {"$ref": "Error"}, "type": "array"}, "message": {"description": "The message of the first error in `errors`.", "type": "string"}}, "type": "object"}, "GmbAccounts": {"id": "GmbAccounts", "properties": {"accountId": {"description": "The ID of the Merchant Center account.", "format": "uint64", "type": "string"}, "gmbAccounts": {"description": "A list of GMB accounts which are available to the merchant.", "items": {"$ref": "GmbAccountsGmbAccount"}, "type": "array"}}, "type": "object"}, "GmbAccountsGmbAccount": {"id": "GmbAccountsGmbAccount", "properties": {"email": {"description": "The email which identifies the GMB account.", "type": "string"}, "listingCount": {"description": "Number of listings under this account.", "format": "uint64", "type": "string"}, "name": {"description": "The name of the GMB account.", "type": "string"}, "type": {"description": "The type of the GMB account (User or Business).", "type": "string"}}, "type": "object"}, "Headers": {"description": "A non-empty list of row or column headers for a table. Exactly one of `prices`, `weights`, `numItems`, `postalCodeGroupNames`, or `location` must be set.", "id": "Headers", "properties": {"locations": {"description": "A list of location ID sets. Must be non-empty. Can only be set if all other fields are not set.", "items": {"$ref": "LocationIdSet"}, "type": "array"}, "numberOfItems": {"description": "A list of inclusive number of items upper bounds. The last value can be `\"infinity\"`. For example `[\"10\", \"50\", \"infinity\"]` represents the headers \"<= 10 items\", \"<= 50 items\", and \"> 50 items\". Must be non-empty. Can only be set if all other fields are not set.", "items": {"type": "string"}, "type": "array"}, "postalCodeGroupNames": {"description": "A list of postal group names. The last value can be `\"all other locations\"`. Example: `[\"zone 1\", \"zone 2\", \"all other locations\"]`. The referred postal code groups must match the delivery country of the service. Must be non-empty. Can only be set if all other fields are not set.", "items": {"type": "string"}, "type": "array"}, "prices": {"description": "A list of inclusive order price upper bounds. The last price's value can be `\"infinity\"`. For example `[{\"value\": \"10\", \"currency\": \"USD\"}, {\"value\": \"500\", \"currency\": \"USD\"}, {\"value\": \"infinity\", \"currency\": \"USD\"}]` represents the headers \"<= $10\", \"<= $500\", and \"> $500\". All prices within a service must have the same currency. Must be non-empty. Can only be set if all other fields are not set.", "items": {"$ref": "Price"}, "type": "array"}, "weights": {"description": "A list of inclusive order weight upper bounds. The last weight's value can be `\"infinity\"`. For example `[{\"value\": \"10\", \"unit\": \"kg\"}, {\"value\": \"50\", \"unit\": \"kg\"}, {\"value\": \"infinity\", \"unit\": \"kg\"}]` represents the headers \"<= 10kg\", \"<= 50kg\", and \"> 50kg\". All weights within a service must have the same unit. Must be non-empty. Can only be set if all other fields are not set.", "items": {"$ref": "Weight"}, "type": "array"}}, "type": "object"}, "HolidayCutoff": {"id": "<PERSON><PERSON><PERSON><PERSON>", "properties": {"deadlineDate": {"description": "Date of the order deadline, in ISO 8601 format. E.g. \"2016-11-29\" for 29th November 2016. Required.", "type": "string"}, "deadlineHour": {"description": "Hour of the day on the deadline date until which the order has to be placed to qualify for the delivery guarantee. Possible values are: 0 (midnight), 1, ..., 12 (noon), 13, ..., 23. Required.", "format": "uint32", "type": "integer"}, "deadlineTimezone": {"description": "Timezone identifier for the deadline hour. A list of identifiers can be found in the AdWords API documentation. E.g. \"Europe/Zurich\". Required.", "type": "string"}, "holidayId": {"description": "Unique identifier for the holiday. Required.", "type": "string"}, "visibleFromDate": {"description": "Date on which the deadline will become visible to consumers in ISO 8601 format. E.g. \"2016-10-31\" for 31st October 2016. Required.", "type": "string"}}, "type": "object"}, "HolidaysHoliday": {"id": "HolidaysHoliday", "properties": {"countryCode": {"description": "The CLDR territory code of the country in which the holiday is available. E.g. \"US\", \"DE\", \"GB\". A holiday cutoff can only be configured in a shipping settings service with matching delivery country. Always present.", "type": "string"}, "date": {"description": "Date of the holiday, in ISO 8601 format. E.g. \"2016-12-25\" for Christmas 2016. Always present.", "type": "string"}, "deliveryGuaranteeDate": {"description": "Date on which the order has to arrive at the customer's, in ISO 8601 format. E.g. \"2016-12-24\" for 24th December 2016. Always present.", "type": "string"}, "deliveryGuaranteeHour": {"description": "Hour of the day in the delivery location's timezone on the guaranteed delivery date by which the order has to arrive at the customer's. Possible values are: 0 (midnight), 1, ..., 12 (noon), 13, ..., 23. Always present.", "format": "uint64", "type": "string"}, "id": {"description": "Unique identifier for the holiday to be used when configuring holiday cutoffs. Always present.", "type": "string"}, "type": {"description": "The holiday type. Always present. Acceptable values are: - \"`Christmas`\" - \"`Easter`\" - \"`Father's Day`\" - \"`Halloween`\" - \"`Independence Day (USA)`\" - \"`Mother's Day`\" - \"`Thanksgiving`\" - \"`Valentine's Day`\" ", "type": "string"}}, "type": "object"}, "Installment": {"id": "Installment", "properties": {"amount": {"$ref": "Price", "description": "The amount the buyer has to pay per month."}, "months": {"description": "The number of installments the buyer has to pay.", "format": "int64", "type": "string"}}, "type": "object"}, "InvoiceSummary": {"id": "InvoiceSummary", "properties": {"additionalChargeSummaries": {"description": "Summary of the total amounts of the additional charges.", "items": {"$ref": "InvoiceSummaryAdditionalChargeSummary"}, "type": "array"}, "customerBalance": {"$ref": "Amount", "description": "Deprecated."}, "googleBalance": {"$ref": "Amount", "description": "Deprecated."}, "merchantBalance": {"$ref": "Amount", "description": "Deprecated."}, "productTotal": {"$ref": "Amount", "description": "[required] Total price for the product."}, "promotionSummaries": {"description": "Deprecated.", "items": {"$ref": "Promotion"}, "type": "array"}}, "type": "object"}, "InvoiceSummaryAdditionalChargeSummary": {"id": "InvoiceSummaryAdditionalChargeSummary", "properties": {"totalAmount": {"$ref": "Amount", "description": "[required] Total additional charge for this type."}, "type": {"description": "[required] Type of the additional charge. Acceptable values are: - \"`shipping`\" ", "type": "string"}}, "type": "object"}, "LiaAboutPageSettings": {"id": "LiaAboutPageSettings", "properties": {"status": {"description": "The status of the verification process for the About page. Acceptable values are: - \"`active`\" - \"`inactive`\" - \"`pending`\" ", "type": "string"}, "url": {"description": "The URL for the About page.", "type": "string"}}, "type": "object"}, "LiaCountrySettings": {"id": "LiaCountrySettings", "properties": {"about": {"$ref": "LiaAboutPageSettings", "description": "The settings for the About page."}, "country": {"description": "Required. CLDR country code (e.g. \"US\").", "type": "string"}, "hostedLocalStorefrontActive": {"description": "The status of the \"Merchant hosted local storefront\" feature.", "type": "boolean"}, "inventory": {"$ref": "LiaInventorySettings", "description": "LIA inventory verification settings."}, "onDisplayToOrder": {"$ref": "LiaOnDisplayToOrderSettings", "description": "LIA \"On Display To Order\" settings."}, "posDataProvider": {"$ref": "LiaPosDataProvider", "description": "The POS data provider linked with this country."}, "storePickupActive": {"description": "The status of the \"Store pickup\" feature.", "type": "boolean"}}, "type": "object"}, "LiaInventorySettings": {"id": "LiaInventorySettings", "properties": {"inventoryVerificationContactEmail": {"description": "The email of the contact for the inventory verification process.", "type": "string"}, "inventoryVerificationContactName": {"description": "The name of the contact for the inventory verification process.", "type": "string"}, "inventoryVerificationContactStatus": {"description": "The status of the verification contact. Acceptable values are: - \"`active`\" - \"`inactive`\" - \"`pending`\" ", "type": "string"}, "status": {"description": "The status of the inventory verification process. Acceptable values are: - \"`active`\" - \"`inactive`\" - \"`pending`\" ", "type": "string"}}, "type": "object"}, "LiaOnDisplayToOrderSettings": {"id": "LiaOnDisplayToOrderSettings", "properties": {"shippingCostPolicyUrl": {"description": "Shipping cost and policy URL.", "type": "string"}, "status": {"description": "The status of the ?On display to order? feature. Acceptable values are: - \"`active`\" - \"`inactive`\" - \"`pending`\" ", "type": "string"}}, "type": "object"}, "LiaPosDataProvider": {"id": "LiaPosDataProvider", "properties": {"posDataProviderId": {"description": "The ID of the POS data provider.", "format": "uint64", "type": "string"}, "posExternalAccountId": {"description": "The account ID by which this merchant is known to the POS data provider.", "type": "string"}}, "type": "object"}, "LiaSettings": {"description": "Local Inventory ads (LIA) settings. All methods except listposdataproviders require the admin role.", "id": "LiaSettings", "properties": {"accountId": {"description": "The ID of the account to which these LIA settings belong. Ignored upon update, always present in get request responses.", "format": "uint64", "type": "string"}, "countrySettings": {"description": "The LIA settings for each country.", "items": {"$ref": "LiaCountrySettings"}, "type": "array"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"`content#liaSettings`\"", "type": "string"}}, "type": "object"}, "LiasettingsCustomBatchRequest": {"id": "LiasettingsCustomBatchRequest", "properties": {"entries": {"description": "The request entries to be processed in the batch.", "items": {"$ref": "LiasettingsCustomBatchRequestEntry"}, "type": "array"}}, "type": "object"}, "LiasettingsCustomBatchRequestEntry": {"id": "LiasettingsCustomBatchRequestEntry", "properties": {"accountId": {"description": "The ID of the account for which to get/update account LIA settings.", "format": "uint64", "type": "string"}, "batchId": {"description": "An entry ID, unique within the batch request.", "format": "uint32", "type": "integer"}, "contactEmail": {"description": "Inventory validation contact email. Required only for SetInventoryValidationContact.", "type": "string"}, "contactName": {"description": "Inventory validation contact name. Required only for SetInventoryValidationContact.", "type": "string"}, "country": {"description": "The country code. Required only for RequestInventoryVerification.", "type": "string"}, "gmbEmail": {"description": "The GMB account. Required only for RequestGmbAccess.", "type": "string"}, "liaSettings": {"$ref": "LiaSettings", "description": "The account Lia settings to update. Only defined if the method is `update`."}, "merchantId": {"description": "The ID of the managing account.", "format": "uint64", "type": "string"}, "method": {"description": "The method of the batch entry. Acceptable values are: - \"`get`\" - \"`getAccessibleGmbAccounts`\" - \"`requestGmbAccess`\" - \"`requestInventoryVerification`\" - \"`setInventoryVerificationContact`\" - \"`update`\" ", "type": "string"}, "posDataProviderId": {"description": "The ID of POS data provider. Required only for SetPosProvider.", "format": "uint64", "type": "string"}, "posExternalAccountId": {"description": "The account ID by which this merchant is known to the POS provider.", "type": "string"}}, "type": "object"}, "LiasettingsCustomBatchResponse": {"id": "LiasettingsCustomBatchResponse", "properties": {"entries": {"description": "The result of the execution of the batch requests.", "items": {"$ref": "LiasettingsCustomBatchResponseEntry"}, "type": "array"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#liasettingsCustomBatchResponse\".", "type": "string"}}, "type": "object"}, "LiasettingsCustomBatchResponseEntry": {"id": "LiasettingsCustomBatchResponseEntry", "properties": {"batchId": {"description": "The ID of the request entry to which this entry responds.", "format": "uint32", "type": "integer"}, "errors": {"$ref": "Errors", "description": "A list of errors defined if, and only if, the request failed."}, "gmbAccounts": {"$ref": "GmbAccounts", "description": "The list of accessible GMB accounts."}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"`content#liasettingsCustomBatchResponseEntry`\"", "type": "string"}, "liaSettings": {"$ref": "LiaSettings", "description": "The retrieved or updated Lia settings."}, "posDataProviders": {"description": "The list of POS data providers.", "items": {"$ref": "PosDataProviders"}, "type": "array"}}, "type": "object"}, "LiasettingsGetAccessibleGmbAccountsResponse": {"id": "LiasettingsGetAccessibleGmbAccountsResponse", "properties": {"accountId": {"description": "The ID of the Merchant Center account.", "format": "uint64", "type": "string"}, "gmbAccounts": {"description": "A list of GMB accounts which are available to the merchant.", "items": {"$ref": "GmbAccountsGmbAccount"}, "type": "array"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#liasettingsGetAccessibleGmbAccountsResponse\".", "type": "string"}}, "type": "object"}, "LiasettingsListPosDataProvidersResponse": {"id": "LiasettingsListPosDataProvidersResponse", "properties": {"kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#liasettingsListPosDataProvidersResponse\".", "type": "string"}, "posDataProviders": {"description": "The list of POS data providers for each eligible country", "items": {"$ref": "PosDataProviders"}, "type": "array"}}, "type": "object"}, "LiasettingsListResponse": {"id": "LiasettingsListResponse", "properties": {"kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#liasettingsListResponse\".", "type": "string"}, "nextPageToken": {"description": "The token for the retrieval of the next page of LIA settings.", "type": "string"}, "resources": {"items": {"$ref": "LiaSettings"}, "type": "array"}}, "type": "object"}, "LiasettingsRequestGmbAccessResponse": {"id": "LiasettingsRequestGmbAccessResponse", "properties": {"kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#liasettingsRequestGmbAccessResponse\".", "type": "string"}}, "type": "object"}, "LiasettingsRequestInventoryVerificationResponse": {"id": "LiasettingsRequestInventoryVerificationResponse", "properties": {"kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#liasettingsRequestInventoryVerificationResponse\".", "type": "string"}}, "type": "object"}, "LiasettingsSetInventoryVerificationContactResponse": {"id": "LiasettingsSetInventoryVerificationContactResponse", "properties": {"kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#liasettingsSetInventoryVerificationContactResponse\".", "type": "string"}}, "type": "object"}, "LiasettingsSetPosDataProviderResponse": {"id": "LiasettingsSetPosDataProviderResponse", "properties": {"kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#liasettingsSetPosDataProviderResponse\".", "type": "string"}}, "type": "object"}, "LocationIdSet": {"id": "LocationIdSet", "properties": {"locationIds": {"description": "A non-empty list of location IDs. They must all be of the same location type (e.g., state).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "LoyaltyPoints": {"id": "LoyaltyPoints", "properties": {"name": {"description": "Name of loyalty points program. It is recommended to limit the name to 12 full-width characters or 24 Roman characters.", "type": "string"}, "pointsValue": {"description": "The retailer's loyalty points in absolute value.", "format": "int64", "type": "string"}, "ratio": {"description": "The ratio of a point when converted to currency. Google assumes currency based on Merchant Center settings. If ratio is left out, it defaults to 1.0.", "format": "double", "type": "number"}}, "type": "object"}, "MerchantOrderReturn": {"description": "Order return. Production access (all methods) requires the order manager role. Sandbox access does not.", "id": "MerchantOrderReturn", "properties": {"creationDate": {"description": "The date of creation of the return, in ISO 8601 format.", "type": "string"}, "merchantOrderId": {"description": "Merchant defined order ID.", "type": "string"}, "orderId": {"description": "Google order ID.", "type": "string"}, "orderReturnId": {"description": "Order return ID generated by Google.", "type": "string"}, "returnItems": {"description": "Items of the return.", "items": {"$ref": "MerchantOrderReturnItem"}, "type": "array"}, "returnShipments": {"description": "Shipments of the return.", "items": {"$ref": "ReturnShipment"}, "type": "array"}}, "type": "object"}, "MerchantOrderReturnItem": {"id": "MerchantOrderReturnItem", "properties": {"customerReturnReason": {"$ref": "CustomerReturnReason", "description": "The reason that the customer chooses to return an item."}, "itemId": {"description": "Product level item ID. If the returned items are of the same product, they will have the same ID.", "type": "string"}, "merchantReturnReason": {"$ref": "RefundReason", "description": "The reason that merchant chooses to accept a return item."}, "product": {"$ref": "OrderLineItemProduct", "description": "Product data from the time of the order placement."}, "returnShipmentIds": {"description": "IDs of the return shipments that this return item belongs to.", "items": {"type": "string"}, "type": "array"}, "state": {"description": "State of the item. Acceptable values are: - \"`canceled`\" - \"`new`\" - \"`received`\" - \"`refunded`\" - \"`rejected`\" ", "type": "string"}}, "type": "object"}, "MinimumOrderValueTable": {"id": "MinimumOrderValueTable", "properties": {"storeCodeSetWithMovs": {"items": {"$ref": "MinimumOrderValueTableStoreCodeSetWithMov"}, "type": "array"}}, "type": "object"}, "MinimumOrderValueTableStoreCodeSetWithMov": {"description": "A list of store code sets sharing the same minimum order value. At least two sets are required and the last one must be empty, which signifies 'MOV for all other stores'. Each store code can only appear once across all the sets. All prices within a service must have the same currency.", "id": "MinimumOrderValueTableStoreCodeSetWithMov", "properties": {"storeCodes": {"description": "A list of unique store codes or empty for the catch all.", "items": {"type": "string"}, "type": "array"}, "value": {"$ref": "Price", "description": "The minimum order value for the given stores."}}, "type": "object"}, "Order": {"description": "Order. Production access (all methods) requires the order manager role. Sandbox access does not.", "id": "Order", "properties": {"acknowledged": {"description": "Whether the order was acknowledged.", "type": "boolean"}, "channelType": {"description": "Deprecated. Acceptable values are: - \"`googleExpress`\" - \"`purchasesOnGoogle`\" ", "type": "string"}, "customer": {"$ref": "OrderCustomer", "description": "The details of the customer who placed the order."}, "deliveryDetails": {"$ref": "OrderDeliveryDetails", "description": "Delivery details for shipments of type `delivery`."}, "id": {"description": "The REST ID of the order. Globally unique.", "type": "string"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"`content#order`\"", "type": "string"}, "lineItems": {"description": "Line items that are ordered.", "items": {"$ref": "OrderLineItem"}, "type": "array"}, "merchantId": {"format": "uint64", "type": "string"}, "merchantOrderId": {"description": "Merchant-provided ID of the order.", "type": "string"}, "netAmount": {"$ref": "Price", "description": "The net amount for the order. For example, if an order was originally for a grand total of $100 and a refund was issued for $20, the net amount will be $80."}, "paymentMethod": {"$ref": "OrderPaymentMethod", "description": "The details of the payment method."}, "paymentStatus": {"description": "The status of the payment. Acceptable values are: - \"`paymentCaptured`\" - \"`paymentRejected`\" - \"`paymentSecured`\" - \"`pendingAuthorization`\" ", "type": "string"}, "pickupDetails": {"$ref": "OrderPickupDetails", "description": "Pickup details for shipments of type `pickup`."}, "placedDate": {"description": "The date when the order was placed, in ISO 8601 format.", "type": "string"}, "promotions": {"description": "The details of the merchant provided promotions applied to the order. To determine which promotions apply to which products, check the `Promotions[].Benefits[].OfferIds` field against the `LineItems[].Product.OfferId` field for each promotion. If a promotion is applied to more than 1 `offerId`, divide the discount value by the number of affected offers to determine how much discount to apply to each `offerId`. Examples: 1. To calculate the line item level discount for a single specific item: For each promotion, subtract the `Promotions[].Benefits[].Discount.value` amount from the `LineItems[].Price.value`. 2. To calculate the line item level discount for multiple quantity of a specific item: For each promotion, divide the `Promotions[].Benefits[].Discount.value` by the quantity of products and substract it from `LineItems[].Product.Price.value` for each quantity item. Only 1 promotion can be applied to an offerId in a given order. To refund an item which had a promotion applied to it, make sure to refund the amount after first subtracting the promotion discount from the item price. More details about the program are here.", "items": {"$ref": "OrderLegacyPromotion"}, "type": "array"}, "refunds": {"description": "Refunds for the order.", "items": {"$ref": "OrderRefund"}, "type": "array"}, "shipments": {"description": "Shipments of the order.", "items": {"$ref": "OrderShipment"}, "type": "array"}, "shippingCost": {"$ref": "Price", "description": "The total cost of shipping for all items."}, "shippingCostTax": {"$ref": "Price", "description": "The tax for the total shipping cost."}, "shippingOption": {"description": "Deprecated. Shipping details are provided with line items instead. Acceptable values are: - \"`economy`\" - \"`expedited`\" - \"`oneDay`\" - \"`sameDay`\" - \"`standard`\" - \"`twoDay`\" ", "type": "string"}, "status": {"description": "The status of the order. Acceptable values are: - \"`canceled`\" - \"`delivered`\" - \"`inProgress`\" - \"`partiallyDelivered`\" - \"`partiallyReturned`\" - \"`partiallyShipped`\" - \"`pendingShipment`\" - \"`returned`\" - \"`shipped`\" ", "type": "string"}, "taxCollector": {"description": "The party responsible for collecting and remitting taxes. Acceptable values are: - \"`marketplaceFacilitator`\" - \"`merchant`\" ", "type": "string"}}, "type": "object"}, "OrderAddress": {"id": "OrderAddress", "properties": {"country": {"description": "CLDR country code (e.g. \"US\").", "type": "string"}, "fullAddress": {"description": "Strings representing the lines of the printed label for mailing the order, for example: <PERSON> 1600 Amphitheatre Parkway Mountain View, CA, 94043 United States ", "items": {"type": "string"}, "type": "array"}, "isPostOfficeBox": {"description": "Whether the address is a post office box.", "type": "boolean"}, "locality": {"description": "City, town or commune. May also include dependent localities or sublocalities (e.g. neighborhoods or suburbs).", "type": "string"}, "postalCode": {"description": "Postal Code or ZIP (e.g. \"94043\").", "type": "string"}, "recipientName": {"description": "Name of the recipient.", "type": "string"}, "region": {"description": "Top-level administrative subdivision of the country. For example, a state like California (\"CA\") or a province like Quebec (\"QC\").", "type": "string"}, "streetAddress": {"description": "Street-level part of the address.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "OrderCancellation": {"id": "OrderCancellation", "properties": {"actor": {"description": "The actor that created the cancellation. Acceptable values are: - \"`customer`\" - \"`googleBot`\" - \"`googleCustomerService`\" - \"`googlePayments`\" - \"`googleSabre`\" - \"`merchant`\" ", "type": "string"}, "creationDate": {"description": "Date on which the cancellation has been created, in ISO 8601 format.", "type": "string"}, "quantity": {"description": "The quantity that was canceled.", "format": "uint32", "type": "integer"}, "reason": {"description": "The reason for the cancellation. Orders that are canceled with a noInventory reason will lead to the removal of the product from Buy on Google until you make an update to that product. This will not affect your Shopping ads. Acceptable values are: - \"`autoPostInternal`\" - \"`autoPostInvalidBillingAddress`\" - \"`autoPostNoInventory`\" - \"`autoPostPriceError`\" - \"`autoPostUndeliverableShippingAddress`\" - \"`couponAbuse`\" - \"`customerCanceled`\" - \"`customerInitiatedCancel`\" - \"`customerSupportRequested`\" - \"`failToPushOrderGoogleError`\" - \"`failToPushOrderMerchantError`\" - \"`failToPushOrderMerchantFulfillmentError`\" - \"`failToPushOrderToMerchant`\" - \"`failToPushOrderToMerchantOutOfStock`\" - \"`invalidCoupon`\" - \"`malformedShippingAddress`\" - \"`merchantDidNotShipOnTime`\" - \"`noInventory`\" - \"`orderTimeout`\" - \"`other`\" - \"`paymentAbuse`\" - \"`paymentDeclined`\" - \"`priceError`\" - \"`returnRefundAbuse`\" - \"`shippingPriceError`\" - \"`taxError`\" - \"`undeliverableShippingAddress`\" - \"`unsupportedPoBoxAddress`\" ", "type": "string"}, "reasonText": {"description": "The explanation of the reason.", "type": "string"}}, "type": "object"}, "OrderCustomer": {"id": "OrderCustomer", "properties": {"email": {"description": "Deprecated.", "type": "string"}, "explicitMarketingPreference": {"description": "Deprecated. Please use marketingRightsInfo instead.", "type": "boolean"}, "fullName": {"description": "Full name of the customer.", "type": "string"}, "invoiceReceivingEmail": {"description": "Email address for the merchant to send value-added tax or invoice documentation of the order. Only the last document sent is made available to the customer. For more information, see About automated VAT invoicing for Buy on Google.", "type": "string"}, "marketingRightsInfo": {"$ref": "OrderCustomerMarketingRightsInfo", "description": "Customer's marketing preferences. Contains the marketing opt-in information that is current at the time that the merchant call. User preference selections can change from one order to the next so preferences must be checked with every order."}}, "type": "object"}, "OrderCustomerMarketingRightsInfo": {"id": "OrderCustomerMarketingRightsInfo", "properties": {"explicitMarketingPreference": {"description": "Last known customer selection regarding marketing preferences. In certain cases this selection might not be known, so this field would be empty. If a customer selected `granted` in their most recent order, they can be subscribed to marketing emails. Customers who have chosen `denied` must not be subscribed, or must be unsubscribed if already opted-in. Acceptable values are: - \"`denied`\" - \"`granted`\" ", "type": "string"}, "lastUpdatedTimestamp": {"description": "Timestamp when last time marketing preference was updated. Could be empty, if user wasn't offered a selection yet.", "type": "string"}, "marketingEmailAddress": {"description": "Email address that can be used for marketing purposes. The field may be empty even if `explicitMarketingPreference` is 'granted'. This happens when retrieving an old order from the customer who deleted their account.", "type": "string"}}, "type": "object"}, "OrderDeliveryDetails": {"id": "OrderDeliveryDetails", "properties": {"address": {"$ref": "OrderAddress", "description": "The delivery address"}, "phoneNumber": {"description": "The phone number of the person receiving the delivery.", "type": "string"}}, "type": "object"}, "OrderLegacyPromotion": {"id": "OrderLegacyPromotion", "properties": {"benefits": {"items": {"$ref": "OrderLegacyPromotionBenefit"}, "type": "array"}, "effectiveDates": {"description": "The date and time frame when the promotion is active and ready for validation review. Note that the promotion live time may be delayed for a few hours due to the validation review. Start date and end date are separated by a forward slash (/). The start date is specified by the format (YYYY-MM-DD), followed by the letter ?T?, the time of the day when the sale starts (in Greenwich Mean Time, GMT), followed by an expression of the time zone for the sale. The end date is in the same format.", "type": "string"}, "genericRedemptionCode": {"description": "Optional. The text code that corresponds to the promotion when applied on the retailer?s website.", "type": "string"}, "id": {"description": "The unique ID of the promotion.", "type": "string"}, "longTitle": {"description": "The full title of the promotion.", "type": "string"}, "productApplicability": {"description": "Whether the promotion is applicable to all products or only specific products. Acceptable values are: - \"`allProducts`\" - \"`specificProducts`\" ", "type": "string"}, "redemptionChannel": {"description": "Indicates that the promotion is valid online. Acceptable values are: - \"`online`\" ", "type": "string"}}, "type": "object"}, "OrderLegacyPromotionBenefit": {"id": "OrderLegacyPromotionBenefit", "properties": {"discount": {"$ref": "Price", "description": "The discount in the order price when the promotion is applied."}, "offerIds": {"description": "The OfferId(s) that were purchased in this order and map to this specific benefit of the promotion.", "items": {"type": "string"}, "type": "array"}, "subType": {"description": "Further describes the benefit of the promotion. Note that we will expand on this enumeration as we support new promotion sub-types. Acceptable values are: - \"`buyMGetMoneyOff`\" - \"`buyMGetNMoneyOff`\" - \"`buyMGetNPercentOff`\" - \"`buyMGetPercentOff`\" - \"`freeGift`\" - \"`freeGiftWithItemId`\" - \"`freeGiftWithValue`\" - \"`freeOvernightShipping`\" - \"`freeShipping`\" - \"`freeTwoDayShipping`\" - \"`moneyOff`\" - \"`percentageOff`\" - \"`rewardPoints`\" - \"`salePrice`\" ", "type": "string"}, "taxImpact": {"$ref": "Price", "description": "The impact on tax when the promotion is applied."}, "type": {"description": "Describes whether the promotion applies to products (e.g. 20% off) or to shipping (e.g. Free Shipping). Acceptable values are: - \"`product`\" - \"`shipping`\" ", "type": "string"}}, "type": "object"}, "OrderLineItem": {"id": "OrderLineItem", "properties": {"annotations": {"description": "Annotations that are attached to the line item.", "items": {"$ref": "OrderMerchantProvidedAnnotation"}, "type": "array"}, "cancellations": {"description": "Cancellations of the line item.", "items": {"$ref": "OrderCancellation"}, "type": "array"}, "id": {"description": "The ID of the line item.", "type": "string"}, "price": {"$ref": "Price", "description": "Total price for the line item. For example, if two items for $10 are purchased, the total price will be $20."}, "product": {"$ref": "OrderLineItemProduct", "description": "Product data as seen by customer from the time of the order placement. Note that certain attributes values (e.g. title or gtin) might be reformatted and no longer match values submitted via product feed."}, "quantityCanceled": {"description": "Number of items canceled.", "format": "uint32", "type": "integer"}, "quantityDelivered": {"description": "Number of items delivered.", "format": "uint32", "type": "integer"}, "quantityOrdered": {"description": "Number of items ordered.", "format": "uint32", "type": "integer"}, "quantityPending": {"description": "Number of items pending.", "format": "uint32", "type": "integer"}, "quantityReadyForPickup": {"description": "Number of items ready for pickup.", "format": "uint32", "type": "integer"}, "quantityReturned": {"description": "Number of items returned.", "format": "uint32", "type": "integer"}, "quantityShipped": {"description": "Number of items shipped.", "format": "uint32", "type": "integer"}, "returnInfo": {"$ref": "OrderLineItemReturnInfo", "description": "Details of the return policy for the line item."}, "returns": {"description": "Returns of the line item.", "items": {"$ref": "OrderReturn"}, "type": "array"}, "shippingDetails": {"$ref": "OrderLineItemShippingDetails", "description": "Details of the requested shipping for the line item."}, "tax": {"$ref": "Price", "description": "Total tax amount for the line item. For example, if two items are purchased, and each have a cost tax of $2, the total tax amount will be $4."}}, "type": "object"}, "OrderLineItemProduct": {"id": "OrderLineItemProduct", "properties": {"brand": {"description": "Brand of the item.", "type": "string"}, "channel": {"description": "The item's channel (online or local). Acceptable values are: - \"`local`\" - \"`online`\" ", "type": "string"}, "condition": {"description": "Condition or state of the item. Acceptable values are: - \"`new`\" - \"`refurbished`\" - \"`used`\" ", "type": "string"}, "contentLanguage": {"description": "The two-letter ISO 639-1 language code for the item.", "type": "string"}, "fees": {"description": "Associated fees at order creation time.", "items": {"$ref": "OrderLineItemProductFee"}, "type": "array"}, "gtin": {"description": "Global Trade Item Number (GTIN) of the item.", "type": "string"}, "id": {"description": "The REST ID of the product.", "type": "string"}, "imageLink": {"description": "URL of an image of the item.", "type": "string"}, "itemGroupId": {"description": "Shared identifier for all variants of the same product.", "type": "string"}, "mpn": {"description": "Manufacturer Part Number (MPN) of the item.", "type": "string"}, "offerId": {"description": "An identifier of the item.", "type": "string"}, "price": {"$ref": "Price", "description": "Price of the item."}, "shownImage": {"description": "URL to the cached image shown to the user when order was placed.", "type": "string"}, "targetCountry": {"description": "The CLDR territory // code of the target country of the product.", "type": "string"}, "title": {"description": "The title of the product.", "type": "string"}, "variantAttributes": {"description": "Variant attributes for the item. These are dimensions of the product, such as color, gender, material, pattern, and size. You can find a comprehensive list of variant attributes here.", "items": {"$ref": "OrderLineItemProductVariantAttribute"}, "type": "array"}}, "type": "object"}, "OrderLineItemProductFee": {"id": "OrderLineItemProductFee", "properties": {"amount": {"$ref": "Price", "description": "Amount of the fee."}, "name": {"description": "Name of the fee.", "type": "string"}}, "type": "object"}, "OrderLineItemProductVariantAttribute": {"id": "OrderLineItemProductVariantAttribute", "properties": {"dimension": {"description": "The dimension of the variant.", "type": "string"}, "value": {"description": "The value for the dimension.", "type": "string"}}, "type": "object"}, "OrderLineItemReturnInfo": {"id": "OrderLineItemReturnInfo", "properties": {"daysToReturn": {"description": "Required. How many days later the item can be returned.", "format": "int32", "type": "integer"}, "isReturnable": {"description": "Required. Whether the item is returnable.", "type": "boolean"}, "policyUrl": {"description": "Required. URL of the item return policy.", "type": "string"}}, "type": "object"}, "OrderLineItemShippingDetails": {"id": "OrderLineItemShippingDetails", "properties": {"deliverByDate": {"description": "Required. The delivery by date, in ISO 8601 format.", "type": "string"}, "method": {"$ref": "OrderLineItemShippingDetailsMethod", "description": "Required. Details of the shipping method."}, "shipByDate": {"description": "Required. The ship by date, in ISO 8601 format.", "type": "string"}, "type": {"description": "Type of shipment. Indicates whether `deliveryDetails` or `pickupDetails` is applicable for this shipment. Acceptable values are: - \"`delivery`\" - \"`pickup`\" ", "type": "string"}}, "type": "object"}, "OrderLineItemShippingDetailsMethod": {"id": "OrderLineItemShippingDetailsMethod", "properties": {"carrier": {"description": "The carrier for the shipping. Optional. See `shipments[].carrier` for a list of acceptable values.", "type": "string"}, "maxDaysInTransit": {"description": "Required. Maximum transit time.", "format": "uint32", "type": "integer"}, "methodName": {"description": "Required. The name of the shipping method.", "type": "string"}, "minDaysInTransit": {"description": "Required. Minimum transit time.", "format": "uint32", "type": "integer"}}, "type": "object"}, "OrderMerchantProvidedAnnotation": {"id": "OrderMerchantProvidedAnnotation", "properties": {"key": {"description": "Key for additional merchant provided (as key-value pairs) annotation about the line item.", "type": "string"}, "value": {"description": "Value for additional merchant provided (as key-value pairs) annotation about the line item.", "type": "string"}}, "type": "object"}, "OrderPaymentMethod": {"id": "OrderPaymentMethod", "properties": {"billingAddress": {"$ref": "OrderAddress", "description": "The billing address."}, "expirationMonth": {"description": "The card expiration month (January = 1, February = 2 etc.).", "format": "int32", "type": "integer"}, "expirationYear": {"description": "The card expiration year (4-digit, e.g. 2015).", "format": "int32", "type": "integer"}, "lastFourDigits": {"description": "The last four digits of the card number.", "type": "string"}, "phoneNumber": {"description": "The billing phone number.", "type": "string"}, "type": {"description": "The type of instrument. Acceptable values are: - \"`AMEX`\" - \"`DISCOVER`\" - \"`JCB`\" - \"`MASTERCARD`\" - \"`UNIONPAY`\" - \"`VISA`\" - \"``\" ", "type": "string"}}, "type": "object"}, "OrderPickupDetails": {"id": "OrderPickupDetails", "properties": {"address": {"$ref": "OrderAddress", "description": "Address of the pickup location where the shipment should be sent. Note that `recipient<PERSON>ame` in the address is the name of the business at the pickup location."}, "collectors": {"description": "Collectors authorized to pick up shipment from the pickup location.", "items": {"$ref": "OrderPickupDetailsCollector"}, "type": "array"}, "locationId": {"description": "ID of the pickup location.", "type": "string"}}, "type": "object"}, "OrderPickupDetailsCollector": {"id": "OrderPickupDetailsCollector", "properties": {"name": {"description": "Name of the person picking up the shipment.", "type": "string"}, "phoneNumber": {"description": "Phone number of the person picking up the shipment.", "type": "string"}}, "type": "object"}, "OrderRefund": {"id": "OrderRefund", "properties": {"actor": {"description": "The actor that created the refund. Acceptable values are: - \"`customer`\" - \"`googleBot`\" - \"`googleCustomerService`\" - \"`googlePayments`\" - \"`googleSabre`\" - \"`merchant`\" ", "type": "string"}, "amount": {"$ref": "Price", "description": "The amount that is refunded."}, "creationDate": {"description": "Date on which the item has been created, in ISO 8601 format.", "type": "string"}, "reason": {"description": "The reason for the refund. Acceptable values are: - \"`adjustment`\" - \"`autoPostInternal`\" - \"`autoPostInvalidBillingAddress`\" - \"`autoPostNoInventory`\" - \"`autoPostPriceError`\" - \"`autoPostUndeliverableShippingAddress`\" - \"`couponAbuse`\" - \"`courtesyAdjustment`\" - \"`customerCanceled`\" - \"`customerDiscretionaryReturn`\" - \"`customerInitiatedMerchantCancel`\" - \"`customerSupportRequested`\" - \"`deliveredLateByCarrier`\" - \"`deliveredTooLate`\" - \"`expiredItem`\" - \"`failToPushOrderGoogleError`\" - \"`failToPushOrderMerchantError`\" - \"`failToPushOrderMerchantFulfillmentError`\" - \"`failToPushOrderToMerchant`\" - \"`failToPushOrderToMerchantOutOfStock`\" - \"`feeAdjustment`\" - \"`invalidCoupon`\" - \"`lateShipmentCredit`\" - \"`malformedShippingAddress`\" - \"`merchantDidNotShipOnTime`\" - \"`noInventory`\" - \"`orderTimeout`\" - \"`other`\" - \"`paymentAbuse`\" - \"`paymentDeclined`\" - \"`priceAdjustment`\" - \"`priceError`\" - \"`productArrivedDamaged`\" - \"`productNotAsDescribed`\" - \"`promoReallocation`\" - \"`qualityNotAsExpected`\" - \"`returnRefundAbuse`\" - \"`shippingCostAdjustment`\" - \"`shippingPriceError`\" - \"`taxAdjustment`\" - \"`taxError`\" - \"`undeliverableShippingAddress`\" - \"`unsupportedPoBoxAddress`\" - \"`wrongProductShipped`\" ", "type": "string"}, "reasonText": {"description": "The explanation of the reason.", "type": "string"}}, "type": "object"}, "OrderReportDisbursement": {"description": "Order disbursement. All methods require the payment analyst role.", "id": "OrderReportDisbursement", "properties": {"disbursementAmount": {"$ref": "Price", "description": "The disbursement amount."}, "disbursementCreationDate": {"description": "The disbursement date, in ISO 8601 format.", "type": "string"}, "disbursementDate": {"description": "The date the disbursement was initiated, in ISO 8601 format.", "type": "string"}, "disbursementId": {"description": "The ID of the disbursement.", "type": "string"}, "merchantId": {"description": "The ID of the managing account.", "format": "uint64", "type": "string"}}, "type": "object"}, "OrderReportTransaction": {"id": "OrderReportTransaction", "properties": {"disbursementAmount": {"$ref": "Price", "description": "The disbursement amount."}, "disbursementCreationDate": {"description": "The date the disbursement was created, in ISO 8601 format.", "type": "string"}, "disbursementDate": {"description": "The date the disbursement was initiated, in ISO 8601 format.", "type": "string"}, "disbursementId": {"description": "The ID of the disbursement.", "type": "string"}, "merchantId": {"description": "The ID of the managing account.", "format": "uint64", "type": "string"}, "merchantOrderId": {"description": "Merchant-provided ID of the order.", "type": "string"}, "orderId": {"description": "The ID of the order.", "type": "string"}, "productAmount": {"$ref": "Amount", "description": "Total amount for the items."}, "productAmountWithRemittedTax": {"$ref": "ProductAmount", "description": "Total amount with remitted tax for the items."}, "transactionDate": {"description": "The date of the transaction, in ISO 8601 format.", "type": "string"}}, "type": "object"}, "OrderReturn": {"id": "OrderReturn", "properties": {"actor": {"description": "The actor that created the refund. Acceptable values are: - \"`customer`\" - \"`googleBot`\" - \"`googleCustomerService`\" - \"`googlePayments`\" - \"`googleSabre`\" - \"`merchant`\" ", "type": "string"}, "creationDate": {"description": "Date on which the item has been created, in ISO 8601 format.", "type": "string"}, "quantity": {"description": "Quantity that is returned.", "format": "uint32", "type": "integer"}, "reason": {"description": "The reason for the return. Acceptable values are: - \"`customerDiscretionaryReturn`\" - \"`customerInitiatedMerchantCancel`\" - \"`deliveredTooLate`\" - \"`expiredItem`\" - \"`invalidCoupon`\" - \"`malformedShippingAddress`\" - \"`other`\" - \"`productArrivedDamaged`\" - \"`productNotAsDescribed`\" - \"`qualityNotAsExpected`\" - \"`undeliverableShippingAddress`\" - \"`unsupportedPoBoxAddress`\" - \"`wrongProductShipped`\" ", "type": "string"}, "reasonText": {"description": "The explanation of the reason.", "type": "string"}}, "type": "object"}, "OrderShipment": {"id": "OrderShipment", "properties": {"carrier": {"description": "The carrier handling the shipment. For supported carriers, Google includes the carrier name and tracking URL in emails to customers. For select supported carriers, Google also automatically updates the shipment status based on the provided shipment ID. *Note:* You can also use unsupported carriers, but emails to customers will not include the carrier name or tracking URL, and there will be no automatic order status updates. Supported carriers for US are: - \"`ups`\" (United Parcel Service) *automatic status updates* - \"`usps`\" (United States Postal Service) *automatic status updates* - \"`fedex`\" (FedEx) *automatic status updates * - \"`dhl`\" (DHL eCommerce) *automatic status updates* (US only) - \"`ontrac`\" (OnTrac) *automatic status updates * - \"`dhl express`\" (DHL Express) - \"`deliv`\" (Deliv) - \"`dynamex`\" (TForce) - \"`lasership`\" (LaserShip) - \"`mpx`\" (Military Parcel Xpress) - \"`uds`\" (United Delivery Service) - \"`efw`\" (Estes Forwarding Worldwide) - \"`jd logistics`\" (JD Logistics) - \"`yunexpress`\" (YunExpress) - \"`china post`\" (China Post) - \"`china ems`\" (China Post Express Mail Service) - \"`singapore post`\" (Singapore Post) - \"`pos malaysia`\" (Pos Malaysia) - \"`postnl`\" (PostNL) - \"`ptt`\" (PTT Turkish Post) - \"`eub`\" (ePacket) - \"`chukou1`\" (Chukou1 Logistics) - \"`bestex`\" (Best Express) - \"`canada post`\" (Canada Post) - \"`purolator`\" (Purolator) - \"`canpar`\" (Canpar) - \"`india post`\" (India Post) - \"`blue dart`\" (Blue Dart) - \"`delhivery`\" (Delhivery) - \"`dtdc`\" (DTDC) - \"`tpc india`\" (TPC India) Supported carriers for FR are: - \"`la poste`\" (La Poste) *automatic status updates * - \"`colissimo`\" (Colissimo by La Poste) *automatic status updates* - \"`ups`\" (United Parcel Service) *automatic status updates * - \"`chronopost`\" (Chronopost by La Poste) - \"`gls`\" (General Logistics Systems France) - \"`dpd`\" (DPD Group by GeoPost) - \"`bpost`\" (Belgian Post Group) - \"`colis prive`\" (Colis Privé) - \"`boxtal`\" (Boxtal) - \"`geodis`\" (GEODIS) - \"`tnt`\" (TNT) - \"`db schenker`\" (DB Schenker) - \"`aramex`\" (Aramex) ", "type": "string"}, "creationDate": {"description": "Date on which the shipment has been created, in ISO 8601 format.", "type": "string"}, "deliveryDate": {"description": "Date on which the shipment has been delivered, in ISO 8601 format. Present only if `status` is `delivered`", "type": "string"}, "id": {"description": "The ID of the shipment.", "type": "string"}, "lineItems": {"description": "The line items that are shipped.", "items": {"$ref": "OrderShipmentLineItemShipment"}, "type": "array"}, "scheduledDeliveryDetails": {"$ref": "OrderShipmentScheduledDeliveryDetails", "description": "Delivery details of the shipment if scheduling is needed."}, "status": {"description": "The status of the shipment. Acceptable values are: - \"`delivered`\" - \"`readyForPickup`\" - \"`shipped`\" - \"`undeliverable`\" ", "type": "string"}, "trackingId": {"description": "The tracking ID for the shipment.", "type": "string"}}, "type": "object"}, "OrderShipmentLineItemShipment": {"id": "OrderShipmentLineItemShipment", "properties": {"lineItemId": {"description": "The ID of the line item that is shipped. This value is assigned by Google when an order is created. Either lineItemId or productId is required.", "type": "string"}, "productId": {"description": "The ID of the product to ship. This is the REST ID used in the products service. Either lineItemId or productId is required.", "type": "string"}, "quantity": {"description": "The quantity that is shipped.", "format": "uint32", "type": "integer"}}, "type": "object"}, "OrderShipmentScheduledDeliveryDetails": {"id": "OrderShipmentScheduledDeliveryDetails", "properties": {"carrierPhoneNumber": {"description": "The phone number of the carrier fulfilling the delivery. The phone number is formatted as the international notation in ITU-T Recommendation E.123 (e.g., \"+41 44 668 1800\").", "type": "string"}, "scheduledDate": {"description": "The date a shipment is scheduled for delivery, in ISO 8601 format.", "type": "string"}}, "type": "object"}, "OrderinvoicesCreateChargeInvoiceRequest": {"id": "OrderinvoicesCreateChargeInvoiceRequest", "properties": {"invoiceId": {"description": "[required] The ID of the invoice.", "type": "string"}, "invoiceSummary": {"$ref": "InvoiceSummary", "description": "[required] Invoice summary."}, "lineItemInvoices": {"description": "[required] Invoice details per line item.", "items": {"$ref": "ShipmentInvoiceLineItemInvoice"}, "type": "array"}, "operationId": {"description": "[required] The ID of the operation, unique across all operations for a given order.", "type": "string"}, "shipmentGroupId": {"description": "[required] ID of the shipment group. It is assigned by the merchant in the `shipLineItems` method and is used to group multiple line items that have the same kind of shipping charges.", "type": "string"}}, "type": "object"}, "OrderinvoicesCreateChargeInvoiceResponse": {"id": "OrderinvoicesCreateChargeInvoiceResponse", "properties": {"executionStatus": {"description": "The status of the execution. Acceptable values are: - \"`duplicate`\" - \"`executed`\" ", "type": "string"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#orderinvoicesCreateChargeInvoiceResponse\".", "type": "string"}}, "type": "object"}, "OrderinvoicesCreateRefundInvoiceRequest": {"id": "OrderinvoicesCreateRefundInvoiceRequest", "properties": {"invoiceId": {"description": "[required] The ID of the invoice.", "type": "string"}, "operationId": {"description": "[required] The ID of the operation, unique across all operations for a given order.", "type": "string"}, "refundOnlyOption": {"$ref": "OrderinvoicesCustomBatchRequestEntryCreateRefundInvoiceRefundOption", "description": "Option to create a refund-only invoice. Exactly one of `refundOnlyOption` or `returnOption` must be provided."}, "returnOption": {"$ref": "OrderinvoicesCustomBatchRequestEntryCreateRefundInvoiceReturnOption", "description": "Option to create an invoice for a refund and mark all items within the invoice as returned. Exactly one of `refundOnlyOption` or `returnOption` must be provided."}, "shipmentInvoices": {"description": "Invoice details for different shipment groups.", "items": {"$ref": "ShipmentInvoice"}, "type": "array"}}, "type": "object"}, "OrderinvoicesCreateRefundInvoiceResponse": {"id": "OrderinvoicesCreateRefundInvoiceResponse", "properties": {"executionStatus": {"description": "The status of the execution. Acceptable values are: - \"`duplicate`\" - \"`executed`\" ", "type": "string"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#orderinvoicesCreateRefundInvoiceResponse\".", "type": "string"}}, "type": "object"}, "OrderinvoicesCustomBatchRequestEntryCreateRefundInvoiceRefundOption": {"id": "OrderinvoicesCustomBatchRequestEntryCreateRefundInvoiceRefundOption", "properties": {"description": {"description": "Optional description of the refund reason.", "type": "string"}, "reason": {"description": "[required] Reason for the refund. Acceptable values are: - \"`adjustment`\" - \"`autoPostInternal`\" - \"`autoPostInvalidBillingAddress`\" - \"`autoPostNoInventory`\" - \"`autoPostPriceError`\" - \"`autoPostUndeliverableShippingAddress`\" - \"`couponAbuse`\" - \"`courtesyAdjustment`\" - \"`customerCanceled`\" - \"`customerDiscretionaryReturn`\" - \"`customerInitiatedMerchantCancel`\" - \"`customerSupportRequested`\" - \"`deliveredLateByCarrier`\" - \"`deliveredTooLate`\" - \"`expiredItem`\" - \"`failToPushOrderGoogleError`\" - \"`failToPushOrderMerchantError`\" - \"`failToPushOrderMerchantFulfillmentError`\" - \"`failToPushOrderToMerchant`\" - \"`failToPushOrderToMerchantOutOfStock`\" - \"`feeAdjustment`\" - \"`invalidCoupon`\" - \"`lateShipmentCredit`\" - \"`malformedShippingAddress`\" - \"`merchantDidNotShipOnTime`\" - \"`noInventory`\" - \"`orderTimeout`\" - \"`other`\" - \"`paymentAbuse`\" - \"`paymentDeclined`\" - \"`priceAdjustment`\" - \"`priceError`\" - \"`productArrivedDamaged`\" - \"`productNotAsDescribed`\" - \"`promoReallocation`\" - \"`qualityNotAsExpected`\" - \"`returnRefundAbuse`\" - \"`shippingCostAdjustment`\" - \"`shippingPriceError`\" - \"`taxAdjustment`\" - \"`taxError`\" - \"`undeliverableShippingAddress`\" - \"`unsupportedPoBoxAddress`\" - \"`wrongProductShipped`\" ", "type": "string"}}, "type": "object"}, "OrderinvoicesCustomBatchRequestEntryCreateRefundInvoiceReturnOption": {"id": "OrderinvoicesCustomBatchRequestEntryCreateRefundInvoiceReturnOption", "properties": {"description": {"description": "Optional description of the return reason.", "type": "string"}, "reason": {"description": "[required] Reason for the return. Acceptable values are: - \"`customerDiscretionaryReturn`\" - \"`customerInitiatedMerchantCancel`\" - \"`deliveredTooLate`\" - \"`expiredItem`\" - \"`invalidCoupon`\" - \"`malformedShippingAddress`\" - \"`other`\" - \"`productArrivedDamaged`\" - \"`productNotAsDescribed`\" - \"`qualityNotAsExpected`\" - \"`undeliverableShippingAddress`\" - \"`unsupportedPoBoxAddress`\" - \"`wrongProductShipped`\" ", "type": "string"}}, "type": "object"}, "OrderreportsListDisbursementsResponse": {"id": "OrderreportsListDisbursementsResponse", "properties": {"disbursements": {"description": "The list of disbursements.", "items": {"$ref": "OrderReportDisbursement"}, "type": "array"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#orderreportsListDisbursementsResponse\".", "type": "string"}, "nextPageToken": {"description": "The token for the retrieval of the next page of disbursements.", "type": "string"}}, "type": "object"}, "OrderreportsListTransactionsResponse": {"id": "OrderreportsListTransactionsResponse", "properties": {"kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#orderreportsListTransactionsResponse\".", "type": "string"}, "nextPageToken": {"description": "The token for the retrieval of the next page of transactions.", "type": "string"}, "transactions": {"description": "The list of transactions.", "items": {"$ref": "OrderReportTransaction"}, "type": "array"}}, "type": "object"}, "OrderreturnsListResponse": {"id": "OrderreturnsListResponse", "properties": {"kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#orderreturnsListResponse\".", "type": "string"}, "nextPageToken": {"description": "The token for the retrieval of the next page of returns.", "type": "string"}, "resources": {"items": {"$ref": "MerchantOrderReturn"}, "type": "array"}}, "type": "object"}, "OrdersAcknowledgeRequest": {"id": "OrdersAcknowledgeRequest", "properties": {"operationId": {"description": "The ID of the operation. Unique across all operations for a given order.", "type": "string"}}, "type": "object"}, "OrdersAcknowledgeResponse": {"id": "OrdersAcknowledgeResponse", "properties": {"executionStatus": {"description": "The status of the execution. Acceptable values are: - \"`duplicate`\" - \"`executed`\" ", "type": "string"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#ordersAcknowledgeResponse\".", "type": "string"}}, "type": "object"}, "OrdersAdvanceTestOrderResponse": {"id": "OrdersAdvanceTestOrderResponse", "properties": {"kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#ordersAdvanceTestOrderResponse\".", "type": "string"}}, "type": "object"}, "OrdersCancelLineItemRequest": {"id": "OrdersCancelLineItemRequest", "properties": {"amount": {"$ref": "Price", "description": "Deprecated. Please use amountPretax and amountTax instead."}, "amountPretax": {"$ref": "Price", "description": "Amount to refund for the cancelation. Optional. If not set, Google will calculate the default based on the price and tax of the items involved. The amount must not be larger than the net amount left on the order."}, "amountTax": {"$ref": "Price", "description": "Tax amount that corresponds to cancellation amount in amountPretax. Optional, but if filled, then amountPretax must be set. Calculated automatically if not provided."}, "lineItemId": {"description": "The ID of the line item to cancel. Either lineItemId or productId is required.", "type": "string"}, "operationId": {"description": "The ID of the operation. Unique across all operations for a given order.", "type": "string"}, "productId": {"description": "The ID of the product to cancel. This is the REST ID used in the products service. Either lineItemId or productId is required.", "type": "string"}, "quantity": {"description": "The quantity to cancel.", "format": "uint32", "type": "integer"}, "reason": {"description": "The reason for the cancellation. Acceptable values are: - \"`customerInitiatedCancel`\" - \"`invalidCoupon`\" - \"`malformedShippingAddress`\" - \"`noInventory`\" - \"`other`\" - \"`priceError`\" - \"`shippingPriceError`\" - \"`taxError`\" - \"`undeliverableShippingAddress`\" - \"`unsupportedPoBoxAddress`\" ", "type": "string"}, "reasonText": {"description": "The explanation of the reason.", "type": "string"}}, "type": "object"}, "OrdersCancelLineItemResponse": {"id": "OrdersCancelLineItemResponse", "properties": {"executionStatus": {"description": "The status of the execution. Acceptable values are: - \"`duplicate`\" - \"`executed`\" ", "type": "string"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#ordersCancelLineItemResponse\".", "type": "string"}}, "type": "object"}, "OrdersCancelRequest": {"id": "OrdersCancelRequest", "properties": {"operationId": {"description": "The ID of the operation. Unique across all operations for a given order.", "type": "string"}, "reason": {"description": "The reason for the cancellation. Acceptable values are: - \"`customerInitiatedCancel`\" - \"`invalidCoupon`\" - \"`malformedShippingAddress`\" - \"`noInventory`\" - \"`other`\" - \"`priceError`\" - \"`shippingPriceError`\" - \"`taxError`\" - \"`undeliverableShippingAddress`\" - \"`unsupportedPoBoxAddress`\" ", "type": "string"}, "reasonText": {"description": "The explanation of the reason.", "type": "string"}}, "type": "object"}, "OrdersCancelResponse": {"id": "OrdersCancelResponse", "properties": {"executionStatus": {"description": "The status of the execution. Acceptable values are: - \"`duplicate`\" - \"`executed`\" ", "type": "string"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#ordersCancelResponse\".", "type": "string"}}, "type": "object"}, "OrdersCancelTestOrderByCustomerRequest": {"id": "OrdersCancelTestOrderByCustomerRequest", "properties": {"reason": {"description": "The reason for the cancellation. Acceptable values are: - \"`changedMind`\" - \"`orderedWrongItem`\" - \"`other`\" ", "type": "string"}}, "type": "object"}, "OrdersCancelTestOrderByCustomerResponse": {"id": "OrdersCancelTestOrderByCustomerResponse", "properties": {"kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#ordersCancelTestOrderByCustomerResponse\".", "type": "string"}}, "type": "object"}, "OrdersCreateTestOrderRequest": {"id": "OrdersCreateTestOrderRequest", "properties": {"country": {"description": "The CLDR territory code of the country of the test order to create. Affects the currency and addresses of orders created via `template_name`, or the addresses of orders created via `test_order`. Acceptable values are: - \"`US`\" - \"`FR`\" Defaults to `US`.", "type": "string"}, "templateName": {"description": "The test order template to use. Specify as an alternative to `testOrder` as a shortcut for retrieving a template and then creating an order using that template. Acceptable values are: - \"`template1`\" - \"`template1a`\" - \"`template1b`\" - \"`template2`\" - \"`template3`\" ", "type": "string"}, "testOrder": {"$ref": "TestOrder", "description": "The test order to create."}}, "type": "object"}, "OrdersCreateTestOrderResponse": {"id": "OrdersCreateTestOrderResponse", "properties": {"kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#ordersCreateTestOrderResponse\".", "type": "string"}, "orderId": {"description": "The ID of the newly created test order.", "type": "string"}}, "type": "object"}, "OrdersCreateTestReturnRequest": {"id": "OrdersCreateTestReturnRequest", "properties": {"items": {"description": "Returned items.", "items": {"$ref": "OrdersCustomBatchRequestEntryCreateTestReturnReturnItem"}, "type": "array"}}, "type": "object"}, "OrdersCreateTestReturnResponse": {"id": "OrdersCreateTestReturnResponse", "properties": {"kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#ordersCreateTestReturnResponse\".", "type": "string"}, "returnId": {"description": "The ID of the newly created test order return.", "type": "string"}}, "type": "object"}, "OrdersCustomBatchRequest": {"id": "OrdersCustomBatchRequest", "properties": {"entries": {"description": "The request entries to be processed in the batch.", "items": {"$ref": "OrdersCustomBatchRequestEntry"}, "type": "array"}}, "type": "object"}, "OrdersCustomBatchRequestEntry": {"id": "OrdersCustomBatchRequestEntry", "properties": {"batchId": {"description": "An entry ID, unique within the batch request.", "format": "uint32", "type": "integer"}, "cancel": {"$ref": "OrdersCustomBatchRequestEntryCancel", "description": "Required for `cancel` method."}, "cancelLineItem": {"$ref": "OrdersCustomBatchRequestEntryCancelLineItem", "description": "Required for `cancelLineItem` method."}, "inStoreRefundLineItem": {"$ref": "OrdersCustomBatchRequestEntryInStoreRefundLineItem", "description": "Required for `inStoreReturnLineItem` method."}, "merchantId": {"description": "The ID of the managing account.", "format": "uint64", "type": "string"}, "merchantOrderId": {"description": "The merchant order ID. Required for `updateMerchantOrderId` and `getByMerchantOrderId` methods.", "type": "string"}, "method": {"description": "The method of the batch entry. Acceptable values are: - \"`acknowledge`\" - \"`cancel`\" - \"`cancelLineItem`\" - \"`get`\" - \"`getByMerchantOrderId`\" - \"`inStoreRefundLineItem`\" - \"`refund`\" - \"`rejectReturnLineItem`\" - \"`returnLineItem`\" - \"`returnRefundLineItem`\" - \"`setLineItemMetadata`\" - \"`shipLineItems`\" - \"`updateLineItemShippingDetails`\" - \"`updateMerchantOrderId`\" - \"`updateShipment`\" ", "type": "string"}, "operationId": {"description": "The ID of the operation. Unique across all operations for a given order. Required for all methods beside `get` and `getByMerchantOrderId`.", "type": "string"}, "orderId": {"description": "The ID of the order. Required for all methods beside `getByMerchantOrderId`.", "type": "string"}, "refund": {"$ref": "OrdersCustomBatchRequestEntryRefund", "description": "Required for `refund` method."}, "rejectReturnLineItem": {"$ref": "OrdersCustomBatchRequestEntryRejectReturnLineItem", "description": "Required for `rejectReturnLineItem` method."}, "returnLineItem": {"$ref": "OrdersCustomBatchRequestEntryReturnLineItem", "description": "Required for `returnLineItem` method."}, "returnRefundLineItem": {"$ref": "OrdersCustomBatchRequestEntryReturnRefundLineItem", "description": "Required for `returnRefundLineItem` method."}, "setLineItemMetadata": {"$ref": "OrdersCustomBatchRequestEntrySetLineItemMetadata", "description": "Required for `setLineItemMetadata` method."}, "shipLineItems": {"$ref": "OrdersCustomBatchRequestEntryShipLineItems", "description": "Required for `shipLineItems` method."}, "updateLineItemShippingDetails": {"$ref": "OrdersCustomBatchRequestEntryUpdateLineItemShippingDetails", "description": "Required for `updateLineItemShippingDate` method."}, "updateShipment": {"$ref": "OrdersCustomBatchRequestEntryUpdateShipment", "description": "Required for `updateShipment` method."}}, "type": "object"}, "OrdersCustomBatchRequestEntryCancel": {"id": "OrdersCustomBatchRequestEntryCancel", "properties": {"reason": {"description": "The reason for the cancellation. Acceptable values are: - \"`customerInitiatedCancel`\" - \"`invalidCoupon`\" - \"`malformedShippingAddress`\" - \"`noInventory`\" - \"`other`\" - \"`priceError`\" - \"`shippingPriceError`\" - \"`taxError`\" - \"`undeliverableShippingAddress`\" - \"`unsupportedPoBoxAddress`\" ", "type": "string"}, "reasonText": {"description": "The explanation of the reason.", "type": "string"}}, "type": "object"}, "OrdersCustomBatchRequestEntryCancelLineItem": {"id": "OrdersCustomBatchRequestEntryCancelLineItem", "properties": {"amount": {"$ref": "Price", "description": "Deprecated. Please use amountPretax and amountTax instead."}, "amountPretax": {"$ref": "Price", "description": "Amount to refund for the cancelation. Optional. If not set, Google will calculate the default based on the price and tax of the items involved. The amount must not be larger than the net amount left on the order."}, "amountTax": {"$ref": "Price", "description": "Tax amount that corresponds to cancellation amount in amountPretax. Optional, but if filled, then amountPretax must be set. Calculated automatically if not provided."}, "lineItemId": {"description": "The ID of the line item to cancel. Either lineItemId or productId is required.", "type": "string"}, "productId": {"description": "The ID of the product to cancel. This is the REST ID used in the products service. Either lineItemId or productId is required.", "type": "string"}, "quantity": {"description": "The quantity to cancel.", "format": "uint32", "type": "integer"}, "reason": {"description": "The reason for the cancellation. Acceptable values are: - \"`customerInitiatedCancel`\" - \"`invalidCoupon`\" - \"`malformedShippingAddress`\" - \"`noInventory`\" - \"`other`\" - \"`priceError`\" - \"`shippingPriceError`\" - \"`taxError`\" - \"`undeliverableShippingAddress`\" - \"`unsupportedPoBoxAddress`\" ", "type": "string"}, "reasonText": {"description": "The explanation of the reason.", "type": "string"}}, "type": "object"}, "OrdersCustomBatchRequestEntryCreateTestReturnReturnItem": {"id": "OrdersCustomBatchRequestEntryCreateTestReturnReturnItem", "properties": {"lineItemId": {"description": "The ID of the line item to return.", "type": "string"}, "quantity": {"description": "Quantity that is returned.", "format": "uint32", "type": "integer"}}, "type": "object"}, "OrdersCustomBatchRequestEntryInStoreRefundLineItem": {"id": "OrdersCustomBatchRequestEntryInStoreRefundLineItem", "properties": {"amountPretax": {"$ref": "Price", "description": "The amount that is refunded. Required."}, "amountTax": {"$ref": "Price", "description": "Tax amount that correspond to refund amount in amountPretax. Required."}, "lineItemId": {"description": "The ID of the line item to return. Either lineItemId or productId is required.", "type": "string"}, "productId": {"description": "The ID of the product to return. This is the REST ID used in the products service. Either lineItemId or productId is required.", "type": "string"}, "quantity": {"description": "The quantity to return and refund.", "format": "uint32", "type": "integer"}, "reason": {"description": "The reason for the return. Acceptable values are: - \"`customerDiscretionaryReturn`\" - \"`customerInitiatedMerchantCancel`\" - \"`deliveredTooLate`\" - \"`expiredItem`\" - \"`invalidCoupon`\" - \"`malformedShippingAddress`\" - \"`other`\" - \"`productArrivedDamaged`\" - \"`productNotAsDescribed`\" - \"`qualityNotAsExpected`\" - \"`undeliverableShippingAddress`\" - \"`unsupportedPoBoxAddress`\" - \"`wrongProductShipped`\" ", "type": "string"}, "reasonText": {"description": "The explanation of the reason.", "type": "string"}}, "type": "object"}, "OrdersCustomBatchRequestEntryRefund": {"id": "OrdersCustomBatchRequestEntryRefund", "properties": {"amount": {"$ref": "Price", "description": "Deprecated. Please use amountPretax and amountTax instead."}, "amountPretax": {"$ref": "Price", "description": "The amount that is refunded. Either amount or amountPretax should be filled."}, "amountTax": {"$ref": "Price", "description": "Tax amount that corresponds to refund amount in amountPretax. Optional, but if filled, amountPretax must be set. Calculated automatically if not provided."}, "reason": {"description": "The reason for the refund. Acceptable values are: - \"`adjustment`\" - \"`courtesyAdjustment`\" - \"`customerCanceled`\" - \"`customerDiscretionaryReturn`\" - \"`deliveredLateByCarrier`\" - \"`feeAdjustment`\" - \"`lateShipmentCredit`\" - \"`noInventory`\" - \"`other`\" - \"`priceError`\" - \"`productArrivedDamaged`\" - \"`productNotAsDescribed`\" - \"`shippingCostAdjustment`\" - \"`taxAdjustment`\" - \"`undeliverableShippingAddress`\" - \"`wrongProductShipped`\" ", "type": "string"}, "reasonText": {"description": "The explanation of the reason.", "type": "string"}}, "type": "object"}, "OrdersCustomBatchRequestEntryRejectReturnLineItem": {"id": "OrdersCustomBatchRequestEntryRejectReturnLineItem", "properties": {"lineItemId": {"description": "The ID of the line item to return. Either lineItemId or productId is required.", "type": "string"}, "productId": {"description": "The ID of the product to return. This is the REST ID used in the products service. Either lineItemId or productId is required.", "type": "string"}, "quantity": {"description": "The quantity to return and refund.", "format": "uint32", "type": "integer"}, "reason": {"description": "The reason for the return. Acceptable values are: - \"`damagedOrUsed`\" - \"`missingComponent`\" - \"`notEligible`\" - \"`other`\" - \"`outOfReturnWindow`\" ", "type": "string"}, "reasonText": {"description": "The explanation of the reason.", "type": "string"}}, "type": "object"}, "OrdersCustomBatchRequestEntryReturnLineItem": {"id": "OrdersCustomBatchRequestEntryReturnLineItem", "properties": {"lineItemId": {"description": "The ID of the line item to return. Either lineItemId or productId is required.", "type": "string"}, "productId": {"description": "The ID of the product to return. This is the REST ID used in the products service. Either lineItemId or productId is required.", "type": "string"}, "quantity": {"description": "The quantity to return.", "format": "uint32", "type": "integer"}, "reason": {"description": "The reason for the return. Acceptable values are: - \"`customerDiscretionaryReturn`\" - \"`customerInitiatedMerchantCancel`\" - \"`deliveredTooLate`\" - \"`expiredItem`\" - \"`invalidCoupon`\" - \"`malformedShippingAddress`\" - \"`other`\" - \"`productArrivedDamaged`\" - \"`productNotAsDescribed`\" - \"`qualityNotAsExpected`\" - \"`undeliverableShippingAddress`\" - \"`unsupportedPoBoxAddress`\" - \"`wrongProductShipped`\" ", "type": "string"}, "reasonText": {"description": "The explanation of the reason.", "type": "string"}}, "type": "object"}, "OrdersCustomBatchRequestEntryReturnRefundLineItem": {"id": "OrdersCustomBatchRequestEntryReturnRefundLineItem", "properties": {"amountPretax": {"$ref": "Price", "description": "The amount that is refunded. If omitted, refundless return is assumed (same as calling returnLineItem method)."}, "amountTax": {"$ref": "Price", "description": "Tax amount that corresponds to refund amount in amountPretax. Optional, but if filled, then amountPretax must be set. Calculated automatically if not provided."}, "lineItemId": {"description": "The ID of the line item to return. Either lineItemId or productId is required.", "type": "string"}, "productId": {"description": "The ID of the product to return. This is the REST ID used in the products service. Either lineItemId or productId is required.", "type": "string"}, "quantity": {"description": "The quantity to return and refund.", "format": "uint32", "type": "integer"}, "reason": {"description": "The reason for the return. Acceptable values are: - \"`customerDiscretionaryReturn`\" - \"`customerInitiatedMerchantCancel`\" - \"`deliveredTooLate`\" - \"`expiredItem`\" - \"`invalidCoupon`\" - \"`malformedShippingAddress`\" - \"`other`\" - \"`productArrivedDamaged`\" - \"`productNotAsDescribed`\" - \"`qualityNotAsExpected`\" - \"`undeliverableShippingAddress`\" - \"`unsupportedPoBoxAddress`\" - \"`wrongProductShipped`\" ", "type": "string"}, "reasonText": {"description": "The explanation of the reason.", "type": "string"}}, "type": "object"}, "OrdersCustomBatchRequestEntrySetLineItemMetadata": {"id": "OrdersCustomBatchRequestEntrySetLineItemMetadata", "properties": {"annotations": {"items": {"$ref": "OrderMerchantProvidedAnnotation"}, "type": "array"}, "lineItemId": {"description": "The ID of the line item to set metadata. Either lineItemId or productId is required.", "type": "string"}, "productId": {"description": "The ID of the product to set metadata. This is the REST ID used in the products service. Either lineItemId or productId is required.", "type": "string"}}, "type": "object"}, "OrdersCustomBatchRequestEntryShipLineItems": {"id": "OrdersCustomBatchRequestEntryShipLineItems", "properties": {"carrier": {"description": "Deprecated. Please use shipmentInfo instead. The carrier handling the shipment. See `shipments[].carrier` in the Orders resource representation for a list of acceptable values.", "type": "string"}, "lineItems": {"description": "Line items to ship.", "items": {"$ref": "OrderShipmentLineItemShipment"}, "type": "array"}, "shipmentGroupId": {"description": "ID of the shipment group. Required for orders that use the orderinvoices service.", "type": "string"}, "shipmentId": {"description": "Deprecated. Please use shipmentInfo instead. The ID of the shipment.", "type": "string"}, "shipmentInfos": {"description": "Shipment information. This field is repeated because a single line item can be shipped in several packages (and have several tracking IDs).", "items": {"$ref": "OrdersCustomBatchRequestEntryShipLineItemsShipmentInfo"}, "type": "array"}, "trackingId": {"description": "Deprecated. Please use shipmentInfo instead. The tracking ID for the shipment.", "type": "string"}}, "type": "object"}, "OrdersCustomBatchRequestEntryShipLineItemsShipmentInfo": {"id": "OrdersCustomBatchRequestEntryShipLineItemsShipmentInfo", "properties": {"carrier": {"description": "The carrier handling the shipment. See `shipments[].carrier` in the Orders resource representation for a list of acceptable values.", "type": "string"}, "shipmentId": {"description": "Required. The ID of the shipment. This is assigned by the merchant and is unique to each shipment.", "type": "string"}, "trackingId": {"description": "The tracking ID for the shipment.", "type": "string"}}, "type": "object"}, "OrdersCustomBatchRequestEntryUpdateLineItemShippingDetails": {"id": "OrdersCustomBatchRequestEntryUpdateLineItemShippingDetails", "properties": {"deliverByDate": {"description": "Updated delivery by date, in ISO 8601 format. If not specified only ship by date is updated. Provided date should be within 1 year timeframe and can not be a date in the past.", "type": "string"}, "lineItemId": {"description": "The ID of the line item to set metadata. Either lineItemId or productId is required.", "type": "string"}, "productId": {"description": "The ID of the product to set metadata. This is the REST ID used in the products service. Either lineItemId or productId is required.", "type": "string"}, "shipByDate": {"description": "Updated ship by date, in ISO 8601 format. If not specified only deliver by date is updated. Provided date should be within 1 year timeframe and can not be a date in the past.", "type": "string"}}, "type": "object"}, "OrdersCustomBatchRequestEntryUpdateShipment": {"id": "OrdersCustomBatchRequestEntryUpdateShipment", "properties": {"carrier": {"description": "The carrier handling the shipment. Not updated if missing. See `shipments[].carrier` in the Orders resource representation for a list of acceptable values.", "type": "string"}, "deliveryDate": {"description": "Date on which the shipment has been delivered, in ISO 8601 format. Optional and can be provided only if `status` is `delivered`.", "type": "string"}, "shipmentId": {"description": "The ID of the shipment.", "type": "string"}, "status": {"description": "New status for the shipment. Not updated if missing. Acceptable values are: - \"`delivered`\" - \"`undeliverable`\" - \"`readyForPickup`\" ", "type": "string"}, "trackingId": {"description": "The tracking ID for the shipment. Not updated if missing.", "type": "string"}}, "type": "object"}, "OrdersCustomBatchResponse": {"id": "OrdersCustomBatchResponse", "properties": {"entries": {"description": "The result of the execution of the batch requests.", "items": {"$ref": "OrdersCustomBatchResponseEntry"}, "type": "array"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#ordersCustomBatchResponse\".", "type": "string"}}, "type": "object"}, "OrdersCustomBatchResponseEntry": {"id": "OrdersCustomBatchResponseEntry", "properties": {"batchId": {"description": "The ID of the request entry this entry responds to.", "format": "uint32", "type": "integer"}, "errors": {"$ref": "Errors", "description": "A list of errors defined if and only if the request failed."}, "executionStatus": {"description": "The status of the execution. Only defined if 1. the request was successful; and 2. the method is not `get`, `getByMerchantOrderId`, or one of the test methods. Acceptable values are: - \"`duplicate`\" - \"`executed`\" ", "type": "string"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"`content#ordersCustomBatchResponseEntry`\"", "type": "string"}, "order": {"$ref": "Order", "description": "The retrieved order. Only defined if the method is `get` and if the request was successful."}}, "type": "object"}, "OrdersGetByMerchantOrderIdResponse": {"id": "OrdersGetByMerchantOrderIdResponse", "properties": {"kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#ordersGetByMerchantOrderIdResponse\".", "type": "string"}, "order": {"$ref": "Order", "description": "The requested order."}}, "type": "object"}, "OrdersGetTestOrderTemplateResponse": {"id": "OrdersGetTestOrderTemplateResponse", "properties": {"kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#ordersGetTestOrderTemplateResponse\".", "type": "string"}, "template": {"$ref": "TestOrder", "description": "The requested test order template."}}, "type": "object"}, "OrdersInStoreRefundLineItemRequest": {"id": "OrdersInStoreRefundLineItemRequest", "properties": {"amountPretax": {"$ref": "Price", "description": "The amount that is refunded. Required."}, "amountTax": {"$ref": "Price", "description": "Tax amount that correspond to refund amount in amountPretax. Required."}, "lineItemId": {"description": "The ID of the line item to return. Either lineItemId or productId is required.", "type": "string"}, "operationId": {"description": "The ID of the operation. Unique across all operations for a given order.", "type": "string"}, "productId": {"description": "The ID of the product to return. This is the REST ID used in the products service. Either lineItemId or productId is required.", "type": "string"}, "quantity": {"description": "The quantity to return and refund.", "format": "uint32", "type": "integer"}, "reason": {"description": "The reason for the return. Acceptable values are: - \"`customerDiscretionaryReturn`\" - \"`customerInitiatedMerchantCancel`\" - \"`deliveredTooLate`\" - \"`expiredItem`\" - \"`invalidCoupon`\" - \"`malformedShippingAddress`\" - \"`other`\" - \"`productArrivedDamaged`\" - \"`productNotAsDescribed`\" - \"`qualityNotAsExpected`\" - \"`undeliverableShippingAddress`\" - \"`unsupportedPoBoxAddress`\" - \"`wrongProductShipped`\" ", "type": "string"}, "reasonText": {"description": "The explanation of the reason.", "type": "string"}}, "type": "object"}, "OrdersInStoreRefundLineItemResponse": {"id": "OrdersInStoreRefundLineItemResponse", "properties": {"executionStatus": {"description": "The status of the execution. Acceptable values are: - \"`duplicate`\" - \"`executed`\" ", "type": "string"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#ordersInStoreRefundLineItemResponse\".", "type": "string"}}, "type": "object"}, "OrdersListResponse": {"id": "OrdersListResponse", "properties": {"kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#ordersListResponse\".", "type": "string"}, "nextPageToken": {"description": "The token for the retrieval of the next page of orders.", "type": "string"}, "resources": {"items": {"$ref": "Order"}, "type": "array"}}, "type": "object"}, "OrdersRefundRequest": {"id": "OrdersRefundRequest", "properties": {"amount": {"$ref": "Price", "description": "Deprecated. Please use amountPretax and amountTax instead."}, "amountPretax": {"$ref": "Price", "description": "The amount that is refunded. Either amount or amountPretax should be filled."}, "amountTax": {"$ref": "Price", "description": "Tax amount that corresponds to refund amount in amountPretax. Optional, but if filled, amountPretax must be set. Calculated automatically if not provided."}, "operationId": {"description": "The ID of the operation. Unique across all operations for a given order.", "type": "string"}, "reason": {"description": "The reason for the refund. Acceptable values are: - \"`adjustment`\" - \"`courtesyAdjustment`\" - \"`customerCanceled`\" - \"`customerDiscretionaryReturn`\" - \"`deliveredLateByCarrier`\" - \"`feeAdjustment`\" - \"`lateShipmentCredit`\" - \"`noInventory`\" - \"`other`\" - \"`priceError`\" - \"`productArrivedDamaged`\" - \"`productNotAsDescribed`\" - \"`shippingCostAdjustment`\" - \"`taxAdjustment`\" - \"`undeliverableShippingAddress`\" - \"`wrongProductShipped`\" ", "type": "string"}, "reasonText": {"description": "The explanation of the reason.", "type": "string"}}, "type": "object"}, "OrdersRefundResponse": {"id": "OrdersRefundResponse", "properties": {"executionStatus": {"description": "The status of the execution. Acceptable values are: - \"`duplicate`\" - \"`executed`\" ", "type": "string"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#ordersRefundResponse\".", "type": "string"}}, "type": "object"}, "OrdersRejectReturnLineItemRequest": {"id": "OrdersRejectReturnLineItemRequest", "properties": {"lineItemId": {"description": "The ID of the line item to return. Either lineItemId or productId is required.", "type": "string"}, "operationId": {"description": "The ID of the operation. Unique across all operations for a given order.", "type": "string"}, "productId": {"description": "The ID of the product to return. This is the REST ID used in the products service. Either lineItemId or productId is required.", "type": "string"}, "quantity": {"description": "The quantity to return and refund.", "format": "uint32", "type": "integer"}, "reason": {"description": "The reason for the return. Acceptable values are: - \"`damagedOrUsed`\" - \"`missingComponent`\" - \"`notEligible`\" - \"`other`\" - \"`outOfReturnWindow`\" ", "type": "string"}, "reasonText": {"description": "The explanation of the reason.", "type": "string"}}, "type": "object"}, "OrdersRejectReturnLineItemResponse": {"id": "OrdersRejectReturnLineItemResponse", "properties": {"executionStatus": {"description": "The status of the execution. Acceptable values are: - \"`duplicate`\" - \"`executed`\" ", "type": "string"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#ordersRejectReturnLineItemResponse\".", "type": "string"}}, "type": "object"}, "OrdersReturnLineItemRequest": {"id": "OrdersReturnLineItemRequest", "properties": {"lineItemId": {"description": "The ID of the line item to return. Either lineItemId or productId is required.", "type": "string"}, "operationId": {"description": "The ID of the operation. Unique across all operations for a given order.", "type": "string"}, "productId": {"description": "The ID of the product to return. This is the REST ID used in the products service. Either lineItemId or productId is required.", "type": "string"}, "quantity": {"description": "The quantity to return.", "format": "uint32", "type": "integer"}, "reason": {"description": "The reason for the return. Acceptable values are: - \"`customerDiscretionaryReturn`\" - \"`customerInitiatedMerchantCancel`\" - \"`deliveredTooLate`\" - \"`expiredItem`\" - \"`invalidCoupon`\" - \"`malformedShippingAddress`\" - \"`other`\" - \"`productArrivedDamaged`\" - \"`productNotAsDescribed`\" - \"`qualityNotAsExpected`\" - \"`undeliverableShippingAddress`\" - \"`unsupportedPoBoxAddress`\" - \"`wrongProductShipped`\" ", "type": "string"}, "reasonText": {"description": "The explanation of the reason.", "type": "string"}}, "type": "object"}, "OrdersReturnLineItemResponse": {"id": "OrdersReturnLineItemResponse", "properties": {"executionStatus": {"description": "The status of the execution. Acceptable values are: - \"`duplicate`\" - \"`executed`\" ", "type": "string"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#ordersReturnLineItemResponse\".", "type": "string"}}, "type": "object"}, "OrdersReturnRefundLineItemRequest": {"id": "OrdersReturnRefundLineItemRequest", "properties": {"amountPretax": {"$ref": "Price", "description": "The amount that is refunded. If omitted, refundless return is assumed (same as calling returnLineItem method)."}, "amountTax": {"$ref": "Price", "description": "Tax amount that corresponds to refund amount in amountPretax. Optional, but if filled, then amountPretax must be set. Calculated automatically if not provided."}, "lineItemId": {"description": "The ID of the line item to return. Either lineItemId or productId is required.", "type": "string"}, "operationId": {"description": "The ID of the operation. Unique across all operations for a given order.", "type": "string"}, "productId": {"description": "The ID of the product to return. This is the REST ID used in the products service. Either lineItemId or productId is required.", "type": "string"}, "quantity": {"description": "The quantity to return and refund. Quantity is required.", "format": "uint32", "type": "integer"}, "reason": {"description": "The reason for the return. Acceptable values are: - \"`customerDiscretionaryReturn`\" - \"`customerInitiatedMerchantCancel`\" - \"`deliveredTooLate`\" - \"`expiredItem`\" - \"`invalidCoupon`\" - \"`malformedShippingAddress`\" - \"`other`\" - \"`productArrivedDamaged`\" - \"`productNotAsDescribed`\" - \"`qualityNotAsExpected`\" - \"`undeliverableShippingAddress`\" - \"`unsupportedPoBoxAddress`\" - \"`wrongProductShipped`\" ", "type": "string"}, "reasonText": {"description": "The explanation of the reason.", "type": "string"}}, "type": "object"}, "OrdersReturnRefundLineItemResponse": {"id": "OrdersReturnRefundLineItemResponse", "properties": {"executionStatus": {"description": "The status of the execution. Acceptable values are: - \"`duplicate`\" - \"`executed`\" ", "type": "string"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#ordersReturnRefundLineItemResponse\".", "type": "string"}}, "type": "object"}, "OrdersSetLineItemMetadataRequest": {"id": "OrdersSetLineItemMetadataRequest", "properties": {"annotations": {"items": {"$ref": "OrderMerchantProvidedAnnotation"}, "type": "array"}, "lineItemId": {"description": "The ID of the line item to set metadata. Either lineItemId or productId is required.", "type": "string"}, "operationId": {"description": "The ID of the operation. Unique across all operations for a given order.", "type": "string"}, "productId": {"description": "The ID of the product to set metadata. This is the REST ID used in the products service. Either lineItemId or productId is required.", "type": "string"}}, "type": "object"}, "OrdersSetLineItemMetadataResponse": {"id": "OrdersSetLineItemMetadataResponse", "properties": {"executionStatus": {"description": "The status of the execution. Acceptable values are: - \"`duplicate`\" - \"`executed`\" ", "type": "string"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#ordersSetLineItemMetadataResponse\".", "type": "string"}}, "type": "object"}, "OrdersShipLineItemsRequest": {"id": "OrdersShipLineItemsRequest", "properties": {"carrier": {"description": "Deprecated. Please use shipmentInfo instead. The carrier handling the shipment. See `shipments[].carrier` in the Orders resource representation for a list of acceptable values.", "type": "string"}, "lineItems": {"description": "Line items to ship.", "items": {"$ref": "OrderShipmentLineItemShipment"}, "type": "array"}, "operationId": {"description": "The ID of the operation. Unique across all operations for a given order.", "type": "string"}, "shipmentGroupId": {"description": "ID of the shipment group. Required for orders that use the orderinvoices service.", "type": "string"}, "shipmentId": {"description": "Deprecated. Please use shipmentInfo instead. The ID of the shipment.", "type": "string"}, "shipmentInfos": {"description": "Shipment information. This field is repeated because a single line item can be shipped in several packages (and have several tracking IDs).", "items": {"$ref": "OrdersCustomBatchRequestEntryShipLineItemsShipmentInfo"}, "type": "array"}, "trackingId": {"description": "Deprecated. Please use shipmentInfo instead. The tracking ID for the shipment.", "type": "string"}}, "type": "object"}, "OrdersShipLineItemsResponse": {"id": "OrdersShipLineItemsResponse", "properties": {"executionStatus": {"description": "The status of the execution. Acceptable values are: - \"`duplicate`\" - \"`executed`\" ", "type": "string"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#ordersShipLineItemsResponse\".", "type": "string"}}, "type": "object"}, "OrdersUpdateLineItemShippingDetailsRequest": {"id": "OrdersUpdateLineItemShippingDetailsRequest", "properties": {"deliverByDate": {"description": "Updated delivery by date, in ISO 8601 format. If not specified only ship by date is updated. Provided date should be within 1 year timeframe and can not be a date in the past.", "type": "string"}, "lineItemId": {"description": "The ID of the line item to set metadata. Either lineItemId or productId is required.", "type": "string"}, "operationId": {"description": "The ID of the operation. Unique across all operations for a given order.", "type": "string"}, "productId": {"description": "The ID of the product to set metadata. This is the REST ID used in the products service. Either lineItemId or productId is required.", "type": "string"}, "shipByDate": {"description": "Updated ship by date, in ISO 8601 format. If not specified only deliver by date is updated. Provided date should be within 1 year timeframe and can not be a date in the past.", "type": "string"}}, "type": "object"}, "OrdersUpdateLineItemShippingDetailsResponse": {"id": "OrdersUpdateLineItemShippingDetailsResponse", "properties": {"executionStatus": {"description": "The status of the execution. Acceptable values are: - \"`duplicate`\" - \"`executed`\" ", "type": "string"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#ordersUpdateLineItemShippingDetailsResponse\".", "type": "string"}}, "type": "object"}, "OrdersUpdateMerchantOrderIdRequest": {"id": "OrdersUpdateMerchantOrderIdRequest", "properties": {"merchantOrderId": {"description": "The merchant order id to be assigned to the order. Must be unique per merchant.", "type": "string"}, "operationId": {"description": "The ID of the operation. Unique across all operations for a given order.", "type": "string"}}, "type": "object"}, "OrdersUpdateMerchantOrderIdResponse": {"id": "OrdersUpdateMerchantOrderIdResponse", "properties": {"executionStatus": {"description": "The status of the execution. Acceptable values are: - \"`duplicate`\" - \"`executed`\" ", "type": "string"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#ordersUpdateMerchantOrderIdResponse\".", "type": "string"}}, "type": "object"}, "OrdersUpdateShipmentRequest": {"id": "OrdersUpdateShipmentRequest", "properties": {"carrier": {"description": "The carrier handling the shipment. Not updated if missing. See `shipments[].carrier` in the Orders resource representation for a list of acceptable values.", "type": "string"}, "deliveryDate": {"description": "Date on which the shipment has been delivered, in ISO 8601 format. Optional and can be provided only if `status` is `delivered`.", "type": "string"}, "operationId": {"description": "The ID of the operation. Unique across all operations for a given order.", "type": "string"}, "shipmentId": {"description": "The ID of the shipment.", "type": "string"}, "status": {"description": "New status for the shipment. Not updated if missing. Acceptable values are: - \"`delivered`\" - \"`undeliverable`\" - \"`readyForPickup`\" ", "type": "string"}, "trackingId": {"description": "The tracking ID for the shipment. Not updated if missing.", "type": "string"}}, "type": "object"}, "OrdersUpdateShipmentResponse": {"id": "OrdersUpdateShipmentResponse", "properties": {"executionStatus": {"description": "The status of the execution. Acceptable values are: - \"`duplicate`\" - \"`executed`\" ", "type": "string"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#ordersUpdateShipmentResponse\".", "type": "string"}}, "type": "object"}, "PickupCarrierService": {"id": "PickupCarrierService", "properties": {"carrierName": {"description": "The name of the pickup carrier (e.g., `\"UPS\"`). Required.", "type": "string"}, "serviceName": {"description": "The name of the pickup service (e.g., `\"Access point\"`). Required.", "type": "string"}}, "type": "object"}, "PickupServicesPickupService": {"id": "PickupServicesPickupService", "properties": {"carrierName": {"description": "The name of the carrier (e.g., `\"UPS\"`). Always present.", "type": "string"}, "country": {"description": "The CLDR country code of the carrier (e.g., \"US\"). Always present.", "type": "string"}, "serviceName": {"description": "The name of the pickup service (e.g., `\"Access point\"`). Always present.", "type": "string"}}, "type": "object"}, "PosCustomBatchRequest": {"id": "PosCustomBatchRequest", "properties": {"entries": {"description": "The request entries to be processed in the batch.", "items": {"$ref": "PosCustomBatchRequestEntry"}, "type": "array"}}, "type": "object"}, "PosCustomBatchRequestEntry": {"id": "PosCustomBatchRequestEntry", "properties": {"batchId": {"description": "An entry ID, unique within the batch request.", "format": "uint32", "type": "integer"}, "inventory": {"$ref": "PosInventory", "description": "The inventory to submit. This should be set only if the method is `inventory`."}, "merchantId": {"description": "The ID of the POS data provider.", "format": "uint64", "type": "string"}, "method": {"description": "The method of the batch entry. Acceptable values are: - \"`delete`\" - \"`get`\" - \"`insert`\" - \"`inventory`\" - \"`sale`\" ", "type": "string"}, "sale": {"$ref": "PosSale", "description": "The sale information to submit. This should be set only if the method is `sale`."}, "store": {"$ref": "PosStore", "description": "The store information to submit. This should be set only if the method is `insert`."}, "storeCode": {"description": "The store code. This should be set only if the method is `delete` or `get`.", "type": "string"}, "targetMerchantId": {"description": "The ID of the account for which to get/submit data.", "format": "uint64", "type": "string"}}, "type": "object"}, "PosCustomBatchResponse": {"id": "PosCustomBatchResponse", "properties": {"entries": {"description": "The result of the execution of the batch requests.", "items": {"$ref": "PosCustomBatchResponseEntry"}, "type": "array"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#posCustomBatchResponse\".", "type": "string"}}, "type": "object"}, "PosCustomBatchResponseEntry": {"id": "PosCustomBatchResponseEntry", "properties": {"batchId": {"description": "The ID of the request entry to which this entry responds.", "format": "uint32", "type": "integer"}, "errors": {"$ref": "Errors", "description": "A list of errors defined if, and only if, the request failed."}, "inventory": {"$ref": "PosInventory", "description": "The updated inventory information."}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"`content#posCustomBatchResponseEntry`\"", "type": "string"}, "sale": {"$ref": "PosSale", "description": "The updated sale information."}, "store": {"$ref": "PosStore", "description": "The retrieved or updated store information."}}, "type": "object"}, "PosDataProviders": {"id": "PosDataProviders", "properties": {"country": {"description": "Country code.", "type": "string"}, "posDataProviders": {"description": "A list of POS data providers.", "items": {"$ref": "PosDataProvidersPosDataProvider"}, "type": "array"}}, "type": "object"}, "PosDataProvidersPosDataProvider": {"id": "PosDataProvidersPosDataProvider", "properties": {"displayName": {"description": "The display name of Pos data Provider.", "type": "string"}, "fullName": {"description": "The full name of this POS data Provider.", "type": "string"}, "providerId": {"description": "The ID of the account.", "format": "uint64", "type": "string"}}, "type": "object"}, "PosInventory": {"description": "The absolute quantity of an item available at the given store.", "id": "PosInventory", "properties": {"contentLanguage": {"description": "Required. The two-letter ISO 639-1 language code for the item.", "type": "string"}, "gtin": {"description": "Global Trade Item Number.", "type": "string"}, "itemId": {"description": "Required. A unique identifier for the item.", "type": "string"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"`content#posInventory`\"", "type": "string"}, "price": {"$ref": "Price", "description": "Required. The current price of the item."}, "quantity": {"description": "Required. The available quantity of the item.", "format": "int64", "type": "string"}, "storeCode": {"description": "Required. The identifier of the merchant's store. Either a `storeCode` inserted via the API or the code of the store in Google My Business.", "type": "string"}, "targetCountry": {"description": "Required. The CLDR territory code for the item.", "type": "string"}, "timestamp": {"description": "Required. The inventory timestamp, in ISO 8601 format.", "type": "string"}}, "type": "object"}, "PosInventoryRequest": {"id": "PosInventoryRequest", "properties": {"contentLanguage": {"description": "Required. The two-letter ISO 639-1 language code for the item.", "type": "string"}, "gtin": {"description": "Global Trade Item Number.", "type": "string"}, "itemId": {"description": "Required. A unique identifier for the item.", "type": "string"}, "price": {"$ref": "Price", "description": "Required. The current price of the item."}, "quantity": {"description": "Required. The available quantity of the item.", "format": "int64", "type": "string"}, "storeCode": {"description": "Required. The identifier of the merchant's store. Either a `storeCode` inserted via the API or the code of the store in Google My Business.", "type": "string"}, "targetCountry": {"description": "Required. The CLDR territory code for the item.", "type": "string"}, "timestamp": {"description": "Required. The inventory timestamp, in ISO 8601 format.", "type": "string"}}, "type": "object"}, "PosInventoryResponse": {"id": "PosInventoryResponse", "properties": {"contentLanguage": {"description": "Required. The two-letter ISO 639-1 language code for the item.", "type": "string"}, "gtin": {"description": "Global Trade Item Number.", "type": "string"}, "itemId": {"description": "Required. A unique identifier for the item.", "type": "string"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#posInventoryResponse\".", "type": "string"}, "price": {"$ref": "Price", "description": "Required. The current price of the item."}, "quantity": {"description": "Required. The available quantity of the item.", "format": "int64", "type": "string"}, "storeCode": {"description": "Required. The identifier of the merchant's store. Either a `storeCode` inserted via the API or the code of the store in Google My Business.", "type": "string"}, "targetCountry": {"description": "Required. The CLDR territory code for the item.", "type": "string"}, "timestamp": {"description": "Required. The inventory timestamp, in ISO 8601 format.", "type": "string"}}, "type": "object"}, "PosListResponse": {"id": "PosListResponse", "properties": {"kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#posListResponse\".", "type": "string"}, "resources": {"items": {"$ref": "PosStore"}, "type": "array"}}, "type": "object"}, "PosSale": {"description": "The change of the available quantity of an item at the given store.", "id": "PosSale", "properties": {"contentLanguage": {"description": "Required. The two-letter ISO 639-1 language code for the item.", "type": "string"}, "gtin": {"description": "Global Trade Item Number.", "type": "string"}, "itemId": {"description": "Required. A unique identifier for the item.", "type": "string"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"`content#posSale`\"", "type": "string"}, "price": {"$ref": "Price", "description": "Required. The price of the item."}, "quantity": {"description": "Required. The relative change of the available quantity. Negative for items returned.", "format": "int64", "type": "string"}, "saleId": {"description": "A unique ID to group items from the same sale event.", "type": "string"}, "storeCode": {"description": "Required. The identifier of the merchant's store. Either a `storeCode` inserted via the API or the code of the store in Google My Business.", "type": "string"}, "targetCountry": {"description": "Required. The CLDR territory code for the item.", "type": "string"}, "timestamp": {"description": "Required. The inventory timestamp, in ISO 8601 format.", "type": "string"}}, "type": "object"}, "PosSaleRequest": {"id": "PosSaleRequest", "properties": {"contentLanguage": {"description": "Required. The two-letter ISO 639-1 language code for the item.", "type": "string"}, "gtin": {"description": "Global Trade Item Number.", "type": "string"}, "itemId": {"description": "Required. A unique identifier for the item.", "type": "string"}, "price": {"$ref": "Price", "description": "Required. The price of the item."}, "quantity": {"description": "Required. The relative change of the available quantity. Negative for items returned.", "format": "int64", "type": "string"}, "saleId": {"description": "A unique ID to group items from the same sale event.", "type": "string"}, "storeCode": {"description": "Required. The identifier of the merchant's store. Either a `storeCode` inserted via the API or the code of the store in Google My Business.", "type": "string"}, "targetCountry": {"description": "Required. The CLDR territory code for the item.", "type": "string"}, "timestamp": {"description": "Required. The inventory timestamp, in ISO 8601 format.", "type": "string"}}, "type": "object"}, "PosSaleResponse": {"id": "PosSaleResponse", "properties": {"contentLanguage": {"description": "Required. The two-letter ISO 639-1 language code for the item.", "type": "string"}, "gtin": {"description": "Global Trade Item Number.", "type": "string"}, "itemId": {"description": "Required. A unique identifier for the item.", "type": "string"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#posSaleResponse\".", "type": "string"}, "price": {"$ref": "Price", "description": "Required. The price of the item."}, "quantity": {"description": "Required. The relative change of the available quantity. Negative for items returned.", "format": "int64", "type": "string"}, "saleId": {"description": "A unique ID to group items from the same sale event.", "type": "string"}, "storeCode": {"description": "Required. The identifier of the merchant's store. Either a `storeCode` inserted via the API or the code of the store in Google My Business.", "type": "string"}, "targetCountry": {"description": "Required. The CLDR territory code for the item.", "type": "string"}, "timestamp": {"description": "Required. The inventory timestamp, in ISO 8601 format.", "type": "string"}}, "type": "object"}, "PosStore": {"description": "Store resource.", "id": "PosStore", "properties": {"gcidCategory": {"description": "The business type of the store.", "items": {"type": "string"}, "type": "array"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"`content#posStore`\"", "type": "string"}, "phoneNumber": {"description": "The store phone number.", "type": "string"}, "placeId": {"description": "The Google Place Id of the store location.", "type": "string"}, "storeAddress": {"description": "Required. The street address of the store.", "type": "string"}, "storeCode": {"description": "Required. A store identifier that is unique for the given merchant.", "type": "string"}, "storeName": {"description": "The merchant or store name.", "type": "string"}, "websiteUrl": {"description": "The website url for the store or merchant.", "type": "string"}}, "type": "object"}, "PostalCodeGroup": {"id": "PostalCodeGroup", "properties": {"country": {"description": "The CLDR territory code of the country the postal code group applies to. Required.", "type": "string"}, "name": {"description": "The name of the postal code group, referred to in headers. Required.", "type": "string"}, "postalCodeRanges": {"description": "A range of postal codes. Required.", "items": {"$ref": "PostalCodeRange"}, "type": "array"}}, "type": "object"}, "PostalCodeRange": {"id": "PostalCodeRange", "properties": {"postalCodeRangeBegin": {"description": "A postal code or a pattern of the form `prefix*` denoting the inclusive lower bound of the range defining the area. Examples values: `\"94108\"`, `\"9410*\"`, `\"9*\"`. Required.", "type": "string"}, "postalCodeRangeEnd": {"description": "A postal code or a pattern of the form `prefix*` denoting the inclusive upper bound of the range defining the area. It must have the same length as `postalCodeRangeBegin`: if `postalCodeRangeBegin` is a postal code then `postalCodeRangeEnd` must be a postal code too; if `postalCodeRangeBegin` is a pattern then `postalCodeRangeEnd` must be a pattern with the same prefix length. Optional: if not set, then the area is defined as being all the postal codes matching `postalCodeRangeBegin`.", "type": "string"}}, "type": "object"}, "Price": {"id": "Price", "properties": {"currency": {"description": "The currency of the price.", "type": "string"}, "value": {"description": "The price represented as a number.", "type": "string"}}, "type": "object"}, "Product": {"description": " Required product attributes are primarily defined by the products data specification. See the Products Data Specification Help Center article for information. Product data. After inserting, updating, or deleting a product, it may take several minutes before changes take effect.", "id": "Product", "properties": {"additionalImageLinks": {"description": "Additional URLs of images of the item.", "items": {"type": "string"}, "type": "array"}, "additionalProductTypes": {"description": "Additional categories of the item (formatted as in products data specification).", "items": {"type": "string"}, "type": "array"}, "adult": {"description": "Should be set to true if the item is targeted towards adults.", "type": "boolean"}, "adwordsGrouping": {"description": "Used to group items in an arbitrary way. Only for CPA%, discouraged otherwise.", "type": "string"}, "adwordsLabels": {"description": "Similar to adwords_grouping, but only works on CPC.", "items": {"type": "string"}, "type": "array"}, "adwordsRedirect": {"description": "Allows advertisers to override the item URL when the product is shown within the context of Product Ads.", "type": "string"}, "ageGroup": {"description": "Target age group of the item. Acceptable values are: - \"`adult`\" - \"`infant`\" - \"`kids`\" - \"`newborn`\" - \"`toddler`\" - \"`youngAdult`\" ", "type": "string"}, "aspects": {"description": "Deprecated. Do not use.", "items": {"$ref": "ProductAspect"}, "type": "array"}, "availability": {"description": "Availability status of the item. Acceptable values are: - \"`in stock`\" - \"`out of stock`\" - \"`preorder`\" ", "type": "string"}, "availabilityDate": {"description": "The day a pre-ordered product becomes available for delivery, in ISO 8601 format.", "type": "string"}, "brand": {"description": "Brand of the item.", "type": "string"}, "canonicalLink": {"description": "URL for the canonical version of your item's landing page.", "type": "string"}, "channel": {"description": "Required. The item's channel (online or local). Acceptable values are: - \"`local`\" - \"`online`\" ", "type": "string"}, "color": {"description": "Color of the item.", "type": "string"}, "condition": {"description": "Condition or state of the item. Acceptable values are: - \"`new`\" - \"`refurbished`\" - \"`used`\" ", "type": "string"}, "contentLanguage": {"description": "Required. The two-letter ISO 639-1 language code for the item.", "type": "string"}, "costOfGoodsSold": {"$ref": "Price", "description": "Cost of goods sold. Used for gross profit reporting."}, "customAttributes": {"description": "A list of custom (merchant-provided) attributes. It can also be used for submitting any attribute of the feed specification in its generic form (e.g., `{ \"name\": \"size type\", \"value\": \"regular\" }`). This is useful for submitting attributes not explicitly exposed by the API, such as additional attributes used for Buy on Google (formerly known as Shopping Actions).", "items": {"$ref": "CustomAttribute"}, "type": "array"}, "customGroups": {"description": "A list of custom (merchant-provided) custom attribute groups.", "items": {"$ref": "CustomGroup"}, "type": "array"}, "customLabel0": {"description": "Custom label 0 for custom grouping of items in a Shopping campaign.", "type": "string"}, "customLabel1": {"description": "Custom label 1 for custom grouping of items in a Shopping campaign.", "type": "string"}, "customLabel2": {"description": "Custom label 2 for custom grouping of items in a Shopping campaign.", "type": "string"}, "customLabel3": {"description": "Custom label 3 for custom grouping of items in a Shopping campaign.", "type": "string"}, "customLabel4": {"description": "Custom label 4 for custom grouping of items in a Shopping campaign.", "type": "string"}, "description": {"description": "Description of the item.", "type": "string"}, "destinations": {"description": "Specifies the intended destinations for the product.", "items": {"$ref": "ProductDestination"}, "type": "array"}, "displayAdsId": {"description": "An identifier for an item for dynamic remarketing campaigns.", "type": "string"}, "displayAdsLink": {"description": "URL directly to your item's landing page for dynamic remarketing campaigns.", "type": "string"}, "displayAdsSimilarIds": {"description": "Advertiser-specified recommendations.", "items": {"type": "string"}, "type": "array"}, "displayAdsTitle": {"description": "Title of an item for dynamic remarketing campaigns.", "type": "string"}, "displayAdsValue": {"description": "Offer margin for dynamic remarketing campaigns.", "format": "double", "type": "number"}, "energyEfficiencyClass": {"description": "The energy efficiency class as defined in EU directive 2010/30/EU. Acceptable values are: - \"`A`\" - \"`A+`\" - \"`A++`\" - \"`A+++`\" - \"`B`\" - \"`C`\" - \"`D`\" - \"`E`\" - \"`F`\" - \"`G`\" ", "type": "string"}, "expirationDate": {"description": "Date on which the item should expire, as specified upon insertion, in ISO 8601 format. The actual expiration date in Google Shopping is exposed in `productstatuses` as `googleExpirationDate` and might be earlier if `expirationDate` is too far in the future.", "type": "string"}, "gender": {"description": "Target gender of the item. Acceptable values are: - \"`female`\" - \"`male`\" - \"`unisex`\" ", "type": "string"}, "googleProductCategory": {"description": "Google's category of the item (see [Google product taxonomy](https://support.google.com/merchants/answer/1705911)). When querying products, this field will contain the user provided value. There is currently no way to get back the auto assigned google product categories through the API.", "type": "string"}, "gtin": {"description": "Global Trade Item Number (GTIN) of the item.", "type": "string"}, "id": {"description": "The REST ID of the product. Content API methods that operate on products take this as their `productId` parameter. The REST ID for a product is of the form channel:contentLanguage: targetCountry: offerId.", "type": "string"}, "identifierExists": {"description": "False when the item does not have unique product identifiers appropriate to its category, such as GTIN, MPN, and brand. Required according to the Unique Product Identifier Rules for all target countries except for Canada.", "type": "boolean"}, "imageLink": {"description": "URL of an image of the item.", "type": "string"}, "installment": {"$ref": "Installment", "description": "Number and amount of installments to pay for an item."}, "isBundle": {"description": "Whether the item is a merchant-defined bundle. A bundle is a custom grouping of different products sold by a merchant for a single price.", "type": "boolean"}, "itemGroupId": {"description": "Shared identifier for all variants of the same product.", "type": "string"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"`content#product`\"", "type": "string"}, "link": {"description": "URL directly linking to your item's page on your website.", "type": "string"}, "loyaltyPoints": {"$ref": "LoyaltyPoints", "description": "Loyalty points that users receive after purchasing the item. Japan only."}, "material": {"description": "The material of which the item is made.", "type": "string"}, "maxEnergyEfficiencyClass": {"description": "The energy efficiency class as defined in EU directive 2010/30/EU. Acceptable values are: - \"`A`\" - \"`A+`\" - \"`A++`\" - \"`A+++`\" - \"`B`\" - \"`C`\" - \"`D`\" - \"`E`\" - \"`F`\" - \"`G`\" ", "type": "string"}, "maxHandlingTime": {"description": "Maximal product handling time (in business days).", "format": "int64", "type": "string"}, "minEnergyEfficiencyClass": {"description": "The energy efficiency class as defined in EU directive 2010/30/EU. Acceptable values are: - \"`A`\" - \"`A+`\" - \"`A++`\" - \"`A+++`\" - \"`B`\" - \"`C`\" - \"`D`\" - \"`E`\" - \"`F`\" - \"`G`\" ", "type": "string"}, "minHandlingTime": {"description": "Minimal product handling time (in business days).", "format": "int64", "type": "string"}, "mobileLink": {"description": "URL for the mobile-optimized version of your item's landing page.", "type": "string"}, "mpn": {"description": "Manufacturer Part Number (MPN) of the item.", "type": "string"}, "multipack": {"description": "The number of identical products in a merchant-defined multipack.", "format": "int64", "type": "string"}, "offerId": {"description": "Required. A unique identifier for the item. Leading and trailing whitespaces are stripped and multiple whitespaces are replaced by a single whitespace upon submission. Only valid unicode characters are accepted. See the products feed specification for details. *Note:* Content API methods that operate on products take the REST ID of the product, *not* this identifier.", "type": "string"}, "onlineOnly": {"description": "Deprecated.", "type": "boolean"}, "pattern": {"description": "The item's pattern (e.g. polka dots).", "type": "string"}, "price": {"$ref": "Price", "description": "Price of the item."}, "productType": {"description": "Your category of the item (formatted as in products data specification).", "type": "string"}, "promotionIds": {"description": "The unique ID of a promotion.", "items": {"type": "string"}, "type": "array"}, "salePrice": {"$ref": "Price", "description": "Advertised sale price of the item."}, "salePriceEffectiveDate": {"description": "Date range during which the item is on sale (see products data specification ).", "type": "string"}, "sellOnGoogleQuantity": {"description": "The quantity of the product that is available for selling on Google. Supported only for online products.", "format": "int64", "type": "string"}, "shipping": {"description": "Shipping rules.", "items": {"$ref": "ProductShipping"}, "type": "array"}, "shippingHeight": {"$ref": "ProductShippingDimension", "description": "Height of the item for shipping."}, "shippingLabel": {"description": "The shipping label of the product, used to group product in account-level shipping rules.", "type": "string"}, "shippingLength": {"$ref": "ProductShippingDimension", "description": "Length of the item for shipping."}, "shippingWeight": {"$ref": "ProductShippingWeight", "description": "Weight of the item for shipping."}, "shippingWidth": {"$ref": "ProductShippingDimension", "description": "Width of the item for shipping."}, "sizeSystem": {"description": "System in which the size is specified. Recommended for apparel items. Acceptable values are: - \"`AU`\" - \"`BR`\" - \"`CN`\" - \"`DE`\" - \"`EU`\" - \"`FR`\" - \"`IT`\" - \"`JP`\" - \"`MEX`\" - \"`UK`\" - \"`US`\" ", "type": "string"}, "sizeType": {"description": "The cut of the item. Recommended for apparel items. Acceptable values are: - \"`big and tall`\" - \"`maternity`\" - \"`oversize`\" - \"`petite`\" - \"`plus`\" - \"`regular`\" ", "type": "string"}, "sizes": {"description": "Size of the item. Only one value is allowed. For variants with different sizes, insert a separate product for each size with the same `itemGroupId` value (see size definition).", "items": {"type": "string"}, "type": "array"}, "source": {"description": "The source of the offer, i.e., how the offer was created. Acceptable values are: - \"`api`\" - \"`crawl`\" - \"`feed`\" ", "type": "string"}, "targetCountry": {"description": "Required. The CLDR territory code for the item.", "type": "string"}, "taxes": {"description": "Tax information.", "items": {"$ref": "ProductTax"}, "type": "array"}, "title": {"description": "Title of the item.", "type": "string"}, "unitPricingBaseMeasure": {"$ref": "ProductUnitPricingBaseMeasure", "description": "The preference of the denominator of the unit price."}, "unitPricingMeasure": {"$ref": "ProductUnitPricingMeasure", "description": "The measure and dimension of an item."}, "validatedDestinations": {"description": "Deprecated. The read-only list of intended destinations which passed validation.", "items": {"type": "string"}, "type": "array"}, "warnings": {"description": "Read-only warnings.", "items": {"$ref": "Error"}, "type": "array"}}, "type": "object"}, "ProductAmount": {"id": "ProductAmount", "properties": {"priceAmount": {"$ref": "Price", "description": "The pre-tax or post-tax price depending on the location of the order."}, "remittedTaxAmount": {"$ref": "Price", "description": "Remitted tax value."}, "taxAmount": {"$ref": "Price", "description": "Tax value."}}, "type": "object"}, "ProductAspect": {"id": "ProductAspect", "properties": {"aspectName": {"description": "Deprecated.", "type": "string"}, "destinationName": {"description": "Deprecated.", "type": "string"}, "intention": {"description": "Deprecated.", "type": "string"}}, "type": "object"}, "ProductDestination": {"id": "ProductDestination", "properties": {"destinationName": {"description": "The name of the destination.", "type": "string"}, "intention": {"description": "Whether the destination is required, excluded or should be validated. Acceptable values are: - \"`default`\" - \"`excluded`\" - \"`optional`\" - \"`required`\" ", "type": "string"}}, "type": "object"}, "ProductShipping": {"id": "ProductShipping", "properties": {"country": {"description": "The CLDR territory code of the country to which an item will ship.", "type": "string"}, "locationGroupName": {"description": "The location where the shipping is applicable, represented by a location group name.", "type": "string"}, "locationId": {"description": "The numeric ID of a location that the shipping rate applies to as defined in the AdWords API.", "format": "int64", "type": "string"}, "postalCode": {"description": "The postal code range that the shipping rate applies to, represented by a postal code, a postal code prefix followed by a * wildcard, a range between two postal codes or two postal code prefixes of equal length.", "type": "string"}, "price": {"$ref": "Price", "description": "Fixed shipping price, represented as a number."}, "region": {"description": "The geographic region to which a shipping rate applies.", "type": "string"}, "service": {"description": "A free-form description of the service class or delivery speed.", "type": "string"}}, "type": "object"}, "ProductShippingDimension": {"id": "ProductShippingDimension", "properties": {"unit": {"description": "The unit of value.", "type": "string"}, "value": {"description": "The dimension of the product used to calculate the shipping cost of the item.", "format": "double", "type": "number"}}, "type": "object"}, "ProductShippingWeight": {"id": "ProductShippingWeight", "properties": {"unit": {"description": "The unit of value.", "type": "string"}, "value": {"description": "The weight of the product used to calculate the shipping cost of the item.", "format": "double", "type": "number"}}, "type": "object"}, "ProductStatus": {"description": "The status of a product, i.e., information about a product computed asynchronously.", "id": "ProductStatus", "properties": {"creationDate": {"description": "Date on which the item has been created, in ISO 8601 format.", "type": "string"}, "dataQualityIssues": {"description": "DEPRECATED - never populated", "items": {"$ref": "ProductStatusDataQualityIssue"}, "type": "array"}, "destinationStatuses": {"description": "The intended destinations for the product.", "items": {"$ref": "ProductStatusDestinationStatus"}, "type": "array"}, "googleExpirationDate": {"description": "Date on which the item expires in Google Shopping, in ISO 8601 format.", "type": "string"}, "itemLevelIssues": {"description": "A list of all issues associated with the product.", "items": {"$ref": "ProductStatusItemLevelIssue"}, "type": "array"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"`content#productStatus`\"", "type": "string"}, "lastUpdateDate": {"description": "Date on which the item has been last updated, in ISO 8601 format.", "type": "string"}, "link": {"description": "The link to the product.", "type": "string"}, "product": {"$ref": "Product", "description": "Product data after applying all the join inputs."}, "productId": {"description": "The ID of the product for which status is reported.", "type": "string"}, "title": {"description": "The title of the product.", "type": "string"}}, "type": "object"}, "ProductStatusDataQualityIssue": {"id": "ProductStatusDataQualityIssue", "properties": {"destination": {"type": "string"}, "detail": {"type": "string"}, "fetchStatus": {"type": "string"}, "id": {"type": "string"}, "location": {"type": "string"}, "severity": {"type": "string"}, "timestamp": {"type": "string"}, "valueOnLandingPage": {"type": "string"}, "valueProvided": {"type": "string"}}, "type": "object"}, "ProductStatusDestinationStatus": {"id": "ProductStatusDestinationStatus", "properties": {"approvalPending": {"description": "Whether the approval status might change due to further processing.", "type": "boolean"}, "approvalStatus": {"description": "The destination's approval status. Acceptable values are: - \"`approved`\" - \"`disapproved`\" ", "type": "string"}, "destination": {"description": "The name of the destination", "type": "string"}, "intention": {"description": "Provided for backward compatibility only. Always set to \"required\". Acceptable values are: - \"`default`\" - \"`excluded`\" - \"`optional`\" - \"`required`\" ", "type": "string"}}, "type": "object"}, "ProductStatusItemLevelIssue": {"id": "ProductStatusItemLevelIssue", "properties": {"attributeName": {"description": "The attribute's name, if the issue is caused by a single attribute.", "type": "string"}, "code": {"description": "The error code of the issue.", "type": "string"}, "description": {"description": "A short issue description in English.", "type": "string"}, "destination": {"description": "The destination the issue applies to.", "type": "string"}, "detail": {"description": "A detailed issue description in English.", "type": "string"}, "documentation": {"description": "The URL of a web page to help with resolving this issue.", "type": "string"}, "resolution": {"description": "Whether the issue can be resolved by the merchant.", "type": "string"}, "servability": {"description": "How this issue affects serving of the offer.", "type": "string"}}, "type": "object"}, "ProductTax": {"id": "ProductTax", "properties": {"country": {"description": "The country within which the item is taxed, specified as a CLDR territory code.", "type": "string"}, "locationId": {"description": "The numeric ID of a location that the tax rate applies to as defined in the AdWords API.", "format": "int64", "type": "string"}, "postalCode": {"description": "The postal code range that the tax rate applies to, represented by a ZIP code, a ZIP code prefix using * wildcard, a range between two ZIP codes or two ZIP code prefixes of equal length. Examples: 94114, 94*, 94002-95460, 94*-95*.", "type": "string"}, "rate": {"description": "The percentage of tax rate that applies to the item price.", "format": "double", "type": "number"}, "region": {"description": "The geographic region to which the tax rate applies.", "type": "string"}, "taxShip": {"description": "Should be set to true if tax is charged on shipping.", "type": "boolean"}}, "type": "object"}, "ProductUnitPricingBaseMeasure": {"id": "ProductUnitPricingBaseMeasure", "properties": {"unit": {"description": "The unit of the denominator.", "type": "string"}, "value": {"description": "The denominator of the unit price.", "format": "int64", "type": "string"}}, "type": "object"}, "ProductUnitPricingMeasure": {"id": "ProductUnitPricingMeasure", "properties": {"unit": {"description": "The unit of the measure.", "type": "string"}, "value": {"description": "The measure of an item.", "format": "double", "type": "number"}}, "type": "object"}, "ProductsCustomBatchRequest": {"id": "ProductsCustomBatchRequest", "properties": {"entries": {"description": "The request entries to be processed in the batch.", "items": {"$ref": "ProductsCustomBatchRequestEntry"}, "type": "array"}}, "type": "object"}, "ProductsCustomBatchRequestEntry": {"description": "A batch entry encoding a single non-batch products request.", "id": "ProductsCustomBatchRequestEntry", "properties": {"batchId": {"description": "An entry ID, unique within the batch request.", "format": "uint32", "type": "integer"}, "merchantId": {"description": "The ID of the managing account.", "format": "uint64", "type": "string"}, "method": {"description": "The method of the batch entry. Acceptable values are: - \"`delete`\" - \"`get`\" - \"`insert`\" ", "type": "string"}, "product": {"$ref": "Product", "description": "The product to insert. Only required if the method is `insert`."}, "productId": {"description": "The ID of the product to get or delete. Only defined if the method is `get` or `delete`.", "type": "string"}}, "type": "object"}, "ProductsCustomBatchResponse": {"id": "ProductsCustomBatchResponse", "properties": {"entries": {"description": "The result of the execution of the batch requests.", "items": {"$ref": "ProductsCustomBatchResponseEntry"}, "type": "array"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#productsCustomBatchResponse\".", "type": "string"}}, "type": "object"}, "ProductsCustomBatchResponseEntry": {"description": "A batch entry encoding a single non-batch products response.", "id": "ProductsCustomBatchResponseEntry", "properties": {"batchId": {"description": "The ID of the request entry this entry responds to.", "format": "uint32", "type": "integer"}, "errors": {"$ref": "Errors", "description": "A list of errors defined if and only if the request failed."}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"`content#productsCustomBatchResponseEntry`\"", "type": "string"}, "product": {"$ref": "Product", "description": "The inserted product. Only defined if the method is `insert` and if the request was successful."}}, "type": "object"}, "ProductsListResponse": {"id": "ProductsListResponse", "properties": {"kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#productsListResponse\".", "type": "string"}, "nextPageToken": {"description": "The token for the retrieval of the next page of products.", "type": "string"}, "resources": {"items": {"$ref": "Product"}, "type": "array"}}, "type": "object"}, "ProductstatusesCustomBatchRequest": {"id": "ProductstatusesCustomBatchRequest", "properties": {"entries": {"description": "The request entries to be processed in the batch.", "items": {"$ref": "ProductstatusesCustomBatchRequestEntry"}, "type": "array"}}, "type": "object"}, "ProductstatusesCustomBatchRequestEntry": {"description": "A batch entry encoding a single non-batch productstatuses request.", "id": "ProductstatusesCustomBatchRequestEntry", "properties": {"batchId": {"description": "An entry ID, unique within the batch request.", "format": "uint32", "type": "integer"}, "destinations": {"description": "If set, only issues for the specified destinations are returned, otherwise only issues for the Shopping destination.", "items": {"type": "string"}, "type": "array"}, "includeAttributes": {"type": "boolean"}, "merchantId": {"description": "The ID of the managing account.", "format": "uint64", "type": "string"}, "method": {"description": "The method of the batch entry. Acceptable values are: - \"`get`\" ", "type": "string"}, "productId": {"description": "The ID of the product whose status to get.", "type": "string"}}, "type": "object"}, "ProductstatusesCustomBatchResponse": {"id": "ProductstatusesCustomBatchResponse", "properties": {"entries": {"description": "The result of the execution of the batch requests.", "items": {"$ref": "ProductstatusesCustomBatchResponseEntry"}, "type": "array"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#productstatusesCustomBatchResponse\".", "type": "string"}}, "type": "object"}, "ProductstatusesCustomBatchResponseEntry": {"description": "A batch entry encoding a single non-batch productstatuses response.", "id": "ProductstatusesCustomBatchResponseEntry", "properties": {"batchId": {"description": "The ID of the request entry this entry responds to.", "format": "uint32", "type": "integer"}, "errors": {"$ref": "Errors", "description": "A list of errors, if the request failed."}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"`content#productstatusesCustomBatchResponseEntry`\"", "type": "string"}, "productStatus": {"$ref": "ProductStatus", "description": "The requested product status. Only defined if the request was successful."}}, "type": "object"}, "ProductstatusesListResponse": {"id": "ProductstatusesListResponse", "properties": {"kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#productstatusesListResponse\".", "type": "string"}, "nextPageToken": {"description": "The token for the retrieval of the next page of products statuses.", "type": "string"}, "resources": {"items": {"$ref": "ProductStatus"}, "type": "array"}}, "type": "object"}, "Promotion": {"id": "Promotion", "properties": {"promotionAmount": {"$ref": "Amount", "description": "[required] Amount of the promotion. The values here are the promotion applied to the unit price pretax and to the total of the tax amounts."}, "promotionId": {"description": "[required] ID of the promotion.", "type": "string"}}, "type": "object"}, "RateGroup": {"id": "RateGroup", "properties": {"applicableShippingLabels": {"description": "A list of shipping labels defining the products to which this rate group applies to. This is a disjunction: only one of the labels has to match for the rate group to apply. May only be empty for the last rate group of a service. Required.", "items": {"type": "string"}, "type": "array"}, "carrierRates": {"description": "A list of carrier rates that can be referred to by `mainTable` or `singleValue`.", "items": {"$ref": "CarrierRate"}, "type": "array"}, "mainTable": {"$ref": "Table", "description": "A table defining the rate group, when `singleValue` is not expressive enough. Can only be set if `singleValue` is not set."}, "name": {"description": "Name of the rate group. Optional. If set has to be unique within shipping service.", "type": "string"}, "singleValue": {"$ref": "Value", "description": "The value of the rate group (e.g. flat rate $10). Can only be set if `mainTable` and `subtables` are not set."}, "subtables": {"description": "A list of subtables referred to by `mainTable`. Can only be set if `mainTable` is set.", "items": {"$ref": "Table"}, "type": "array"}}, "type": "object"}, "RefundReason": {"id": "RefundReason", "properties": {"description": {"description": "Description of the reason.", "type": "string"}, "reasonCode": {"description": "Code of the refund reason. Acceptable values are: - \"`adjustment`\" - \"`autoPostInternal`\" - \"`autoPostInvalidBillingAddress`\" - \"`autoPostNoInventory`\" - \"`autoPostPriceError`\" - \"`autoPostUndeliverableShippingAddress`\" - \"`couponAbuse`\" - \"`courtesyAdjustment`\" - \"`customerCanceled`\" - \"`customerDiscretionaryReturn`\" - \"`customerInitiatedMerchantCancel`\" - \"`customerSupportRequested`\" - \"`deliveredLateByCarrier`\" - \"`deliveredTooLate`\" - \"`expiredItem`\" - \"`failToPushOrderGoogleError`\" - \"`failToPushOrderMerchantError`\" - \"`failToPushOrderMerchantFulfillmentError`\" - \"`failToPushOrderToMerchant`\" - \"`failToPushOrderToMerchantOutOfStock`\" - \"`feeAdjustment`\" - \"`invalidCoupon`\" - \"`lateShipmentCredit`\" - \"`malformedShippingAddress`\" - \"`merchantDidNotShipOnTime`\" - \"`noInventory`\" - \"`orderTimeout`\" - \"`other`\" - \"`paymentAbuse`\" - \"`paymentDeclined`\" - \"`priceAdjustment`\" - \"`priceError`\" - \"`productArrivedDamaged`\" - \"`productNotAsDescribed`\" - \"`promoReallocation`\" - \"`qualityNotAsExpected`\" - \"`returnRefundAbuse`\" - \"`shippingCostAdjustment`\" - \"`shippingPriceError`\" - \"`taxAdjustment`\" - \"`taxError`\" - \"`undeliverableShippingAddress`\" - \"`unsupportedPoBoxAddress`\" - \"`wrongProductShipped`\" ", "type": "string"}}, "type": "object"}, "ReturnShipment": {"id": "ReturnShipment", "properties": {"creationDate": {"description": "The date of creation of the shipment, in ISO 8601 format.", "type": "string"}, "deliveryDate": {"description": "The date of delivery of the shipment, in ISO 8601 format.", "type": "string"}, "returnMethodType": {"description": "Type of the return method. Acceptable values are: - \"`byMail`\" - \"`contactCustomerSupport`\" - \"`returnless`\" ", "type": "string"}, "shipmentId": {"description": "Shipment ID generated by Google.", "type": "string"}, "shipmentTrackingInfos": {"description": "Tracking information of the shipment. One return shipment might be handled by several shipping carriers sequentially.", "items": {"$ref": "ShipmentTrackingInfo"}, "type": "array"}, "shippingDate": {"description": "The date of shipping of the shipment, in ISO 8601 format.", "type": "string"}, "state": {"description": "State of the shipment. Acceptable values are: - \"`completed`\" - \"`new`\" - \"`shipped`\" - \"`undeliverable`\" - \"`pending`\" ", "type": "string"}}, "type": "object"}, "Row": {"id": "Row", "properties": {"cells": {"description": "The list of cells that constitute the row. Must have the same length as `columnHeaders` for two-dimensional tables, a length of 1 for one-dimensional tables. Required.", "items": {"$ref": "Value"}, "type": "array"}}, "type": "object"}, "Service": {"id": "Service", "properties": {"active": {"description": "A boolean exposing the active status of the shipping service. Required.", "type": "boolean"}, "currency": {"description": "The CLDR code of the currency to which this service applies. Must match that of the prices in rate groups.", "type": "string"}, "deliveryCountry": {"description": "The CLDR territory code of the country to which the service applies. Required.", "type": "string"}, "deliveryTime": {"$ref": "DeliveryTime", "description": "Time spent in various aspects from order to the delivery of the product. Required."}, "eligibility": {"description": "Eligibility for this service. Acceptable values are: - \"`All scenarios`\" - \"`All scenarios except Shopping Actions`\" - \"`Shopping Actions`\" ", "type": "string"}, "minimumOrderValue": {"$ref": "Price", "description": "Minimum order value for this service. If set, indicates that customers will have to spend at least this amount. All prices within a service must have the same currency. Cannot be set together with minimum_order_value_table."}, "minimumOrderValueTable": {"$ref": "MinimumOrderValueTable", "description": "Table of per store minimum order values for the pickup fulfillment type. Cannot be set together with minimum_order_value."}, "name": {"description": "Free-form name of the service. Must be unique within target account. Required.", "type": "string"}, "pickupService": {"$ref": "PickupCarrierService", "description": "The carrier-service pair delivering items to collection points. The list of supported pickup services can be retrieved via the `getSupportedPickupServices` method. Required if and only if the service delivery type is `pickup`."}, "rateGroups": {"description": "Shipping rate group definitions. Only the last one is allowed to have an empty `applicableShippingLabels`, which means \"everything else\". The other `applicableShippingLabels` must not overlap.", "items": {"$ref": "RateGroup"}, "type": "array"}, "shipmentType": {"description": "Type of locations this service ships orders to. Acceptable values are: - \"`delivery`\" - \"`pickup`\" ", "type": "string"}}, "type": "object"}, "ShipmentInvoice": {"id": "ShipmentInvoice", "properties": {"invoiceSummary": {"$ref": "InvoiceSummary", "description": "[required] Invoice summary."}, "lineItemInvoices": {"description": "[required] Invoice details per line item.", "items": {"$ref": "ShipmentInvoiceLineItemInvoice"}, "type": "array"}, "shipmentGroupId": {"description": "[required] ID of the shipment group. It is assigned by the merchant in the `shipLineItems` method and is used to group multiple line items that have the same kind of shipping charges.", "type": "string"}}, "type": "object"}, "ShipmentInvoiceLineItemInvoice": {"id": "ShipmentInvoiceLineItemInvoice", "properties": {"lineItemId": {"description": "ID of the line item. Either lineItemId or productId must be set.", "type": "string"}, "productId": {"description": "ID of the product. This is the REST ID used in the products service. Either lineItemId or productId must be set.", "type": "string"}, "shipmentUnitIds": {"description": "[required] The shipment unit ID is assigned by the merchant and defines individual quantities within a line item. The same ID can be assigned to units that are the same while units that differ must be assigned a different ID (for example: free or promotional units).", "items": {"type": "string"}, "type": "array"}, "unitInvoice": {"$ref": "UnitInvoice", "description": "[required] Invoice details for a single unit."}}, "type": "object"}, "ShipmentTrackingInfo": {"id": "ShipmentTrackingInfo", "properties": {"carrier": {"description": "The shipping carrier that handles the package. Acceptable values are: - \"`boxtal`\" - \"`bpost`\" - \"`chronopost`\" - \"`colisPrive`\" - \"`colissimo`\" - \"`cxt`\" - \"`deliv`\" - \"`dhl`\" - \"`dpd`\" - \"`dynamex`\" - \"`eCourier`\" - \"`easypost`\" - \"`efw`\" - \"`fedex`\" - \"`fedexSmartpost`\" - \"`geodis`\" - \"`gls`\" - \"`googleCourier`\" - \"`gsx`\" - \"`jdLogistics`\" - \"`laPoste`\" - \"`lasership`\" - \"`manual`\" - \"`mpx`\" - \"`onTrac`\" - \"`other`\" - \"`tnt`\" - \"`uds`\" - \"`ups`\" - \"`usps`\" ", "type": "string"}, "trackingNumber": {"description": "The tracking number for the package.", "type": "string"}}, "type": "object"}, "ShippingSettings": {"description": "The merchant account's shipping settings. All methods except getsupportedcarriers and getsupportedholidays require the admin role.", "id": "ShippingSettings", "properties": {"accountId": {"description": "The ID of the account to which these account shipping settings belong. Ignored upon update, always present in get request responses.", "format": "uint64", "type": "string"}, "postalCodeGroups": {"description": "A list of postal code groups that can be referred to in `services`. Optional.", "items": {"$ref": "PostalCodeGroup"}, "type": "array"}, "services": {"description": "The target account's list of services. Optional.", "items": {"$ref": "Service"}, "type": "array"}, "warehouses": {"description": "Optional. A list of warehouses which can be referred to in `services`.", "items": {"$ref": "Warehouse"}, "type": "array"}}, "type": "object"}, "ShippingsettingsCustomBatchRequest": {"id": "ShippingsettingsCustomBatchRequest", "properties": {"entries": {"description": "The request entries to be processed in the batch.", "items": {"$ref": "ShippingsettingsCustomBatchRequestEntry"}, "type": "array"}}, "type": "object"}, "ShippingsettingsCustomBatchRequestEntry": {"description": "A batch entry encoding a single non-batch shippingsettings request.", "id": "ShippingsettingsCustomBatchRequestEntry", "properties": {"accountId": {"description": "The ID of the account for which to get/update account shipping settings.", "format": "uint64", "type": "string"}, "batchId": {"description": "An entry ID, unique within the batch request.", "format": "uint32", "type": "integer"}, "merchantId": {"description": "The ID of the managing account.", "format": "uint64", "type": "string"}, "method": {"description": "The method of the batch entry. Acceptable values are: - \"`get`\" - \"`update`\" ", "type": "string"}, "shippingSettings": {"$ref": "ShippingSettings", "description": "The account shipping settings to update. Only defined if the method is `update`."}}, "type": "object"}, "ShippingsettingsCustomBatchResponse": {"id": "ShippingsettingsCustomBatchResponse", "properties": {"entries": {"description": "The result of the execution of the batch requests.", "items": {"$ref": "ShippingsettingsCustomBatchResponseEntry"}, "type": "array"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#shippingsettingsCustomBatchResponse\".", "type": "string"}}, "type": "object"}, "ShippingsettingsCustomBatchResponseEntry": {"description": "A batch entry encoding a single non-batch shipping settings response.", "id": "ShippingsettingsCustomBatchResponseEntry", "properties": {"batchId": {"description": "The ID of the request entry to which this entry responds.", "format": "uint32", "type": "integer"}, "errors": {"$ref": "Errors", "description": "A list of errors defined if, and only if, the request failed."}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"`content#shippingsettingsCustomBatchResponseEntry`\"", "type": "string"}, "shippingSettings": {"$ref": "ShippingSettings", "description": "The retrieved or updated account shipping settings."}}, "type": "object"}, "ShippingsettingsGetSupportedCarriersResponse": {"id": "ShippingsettingsGetSupportedCarriersResponse", "properties": {"carriers": {"description": "A list of supported carriers. May be empty.", "items": {"$ref": "CarriersCarrier"}, "type": "array"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#shippingsettingsGetSupportedCarriersResponse\".", "type": "string"}}, "type": "object"}, "ShippingsettingsGetSupportedHolidaysResponse": {"id": "ShippingsettingsGetSupportedHolidaysResponse", "properties": {"holidays": {"description": "A list of holidays applicable for delivery guarantees. May be empty.", "items": {"$ref": "HolidaysHoliday"}, "type": "array"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#shippingsettingsGetSupportedHolidaysResponse\".", "type": "string"}}, "type": "object"}, "ShippingsettingsGetSupportedPickupServicesResponse": {"id": "ShippingsettingsGetSupportedPickupServicesResponse", "properties": {"kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#shippingsettingsGetSupportedPickupServicesResponse\".", "type": "string"}, "pickupServices": {"description": "A list of supported pickup services. May be empty.", "items": {"$ref": "PickupServicesPickupService"}, "type": "array"}}, "type": "object"}, "ShippingsettingsListResponse": {"id": "ShippingsettingsListResponse", "properties": {"kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"content#shippingsettingsListResponse\".", "type": "string"}, "nextPageToken": {"description": "The token for the retrieval of the next page of shipping settings.", "type": "string"}, "resources": {"items": {"$ref": "ShippingSettings"}, "type": "array"}}, "type": "object"}, "Table": {"id": "Table", "properties": {"columnHeaders": {"$ref": "Headers", "description": "Headers of the table's columns. Optional: if not set then the table has only one dimension."}, "name": {"description": "Name of the table. Required for subtables, ignored for the main table.", "type": "string"}, "rowHeaders": {"$ref": "Headers", "description": "Headers of the table's rows. Required."}, "rows": {"description": "The list of rows that constitute the table. Must have the same length as `rowHeaders`. Required.", "items": {"$ref": "Row"}, "type": "array"}}, "type": "object"}, "TestOrder": {"id": "TestOrder", "properties": {"customer": {"$ref": "TestOrderCustomer", "description": "Required. The details of the customer who placed the order."}, "enableOrderinvoices": {"description": "Whether the orderinvoices service should support this order.", "type": "boolean"}, "kind": {"description": "Identifies what kind of resource this is. Value: the fixed string \"`content#testOrder`\"", "type": "string"}, "lineItems": {"description": "Required. Line items that are ordered. At least one line item must be provided.", "items": {"$ref": "TestOrderLineItem"}, "type": "array"}, "notificationMode": {"description": "Restricted. Do not use.", "type": "string"}, "paymentMethod": {"$ref": "TestOrderPaymentMethod", "description": "The details of the payment method."}, "predefinedDeliveryAddress": {"description": "Required. Identifier of one of the predefined delivery addresses for the delivery. Acceptable values are: - \"`dwight`\" - \"`jim`\" - \"`pam`\" ", "type": "string"}, "predefinedPickupDetails": {"description": "Identifier of one of the predefined pickup details. Required for orders containing line items with shipping type `pickup`. Acceptable values are: - \"`dwight`\" - \"`jim`\" - \"`pam`\" ", "type": "string"}, "promotions": {"description": "Deprecated. Ignored if provided.", "items": {"$ref": "OrderLegacyPromotion"}, "type": "array"}, "shippingCost": {"$ref": "Price", "description": "Required. The price of shipping for all items. Shipping tax is automatically calculated for orders where marketplace facilitator tax laws are applicable. Otherwise, tax settings from Merchant Center are applied. Note that shipping is not taxed in certain states."}, "shippingCostTax": {"$ref": "Price", "description": "Deprecated. Ignored if provided."}, "shippingOption": {"description": "Required. The requested shipping option. Acceptable values are: - \"`economy`\" - \"`expedited`\" - \"`oneDay`\" - \"`sameDay`\" - \"`standard`\" - \"`twoDay`\" ", "type": "string"}}, "type": "object"}, "TestOrderCustomer": {"id": "TestOrderCustomer", "properties": {"email": {"description": "Required. Email address of the customer. Acceptable values are: - \"`<EMAIL>`\" - \"`<EMAIL>`\" - \"`<EMAIL>`\" ", "type": "string"}, "explicitMarketingPreference": {"description": "Deprecated. Please use marketingRightsInfo instead.", "type": "boolean"}, "fullName": {"description": "Full name of the customer.", "type": "string"}, "marketingRightsInfo": {"$ref": "TestOrderCustomerMarketingRightsInfo", "description": "Customer's marketing preferences."}}, "type": "object"}, "TestOrderCustomerMarketingRightsInfo": {"id": "TestOrderCustomerMarketingRightsInfo", "properties": {"explicitMarketingPreference": {"description": "Last know user use selection regards marketing preferences. In certain cases selection might not be known, so this field would be empty. Acceptable values are: - \"`denied`\" - \"`granted`\" ", "type": "string"}, "lastUpdatedTimestamp": {"description": "Timestamp when last time marketing preference was updated. Could be empty, if user wasn't offered a selection yet.", "type": "string"}}, "type": "object"}, "TestOrderLineItem": {"id": "TestOrderLineItem", "properties": {"product": {"$ref": "TestOrderLineItemProduct", "description": "Required. Product data from the time of the order placement."}, "quantityOrdered": {"description": "Required. Number of items ordered.", "format": "uint32", "type": "integer"}, "returnInfo": {"$ref": "OrderLineItemReturnInfo", "description": "Required. Details of the return policy for the line item."}, "shippingDetails": {"$ref": "OrderLineItemShippingDetails", "description": "Required. Details of the requested shipping for the line item."}, "unitTax": {"$ref": "Price", "description": "Deprecated. Ignored if provided."}}, "type": "object"}, "TestOrderLineItemProduct": {"id": "TestOrderLineItemProduct", "properties": {"brand": {"description": "Required. Brand of the item.", "type": "string"}, "channel": {"description": "Deprecated. Acceptable values are: - \"`online`\" ", "type": "string"}, "condition": {"description": "Required. Condition or state of the item. Acceptable values are: - \"`new`\" ", "type": "string"}, "contentLanguage": {"description": "Required. The two-letter ISO 639-1 language code for the item. Acceptable values are: - \"`en`\" - \"`fr`\" ", "type": "string"}, "fees": {"description": "Fees for the item. Optional.", "items": {"$ref": "OrderLineItemProductFee"}, "type": "array"}, "gtin": {"description": "Global Trade Item Number (GTIN) of the item. Optional.", "type": "string"}, "imageLink": {"description": "Required. URL of an image of the item.", "type": "string"}, "itemGroupId": {"description": "Shared identifier for all variants of the same product. Optional.", "type": "string"}, "mpn": {"description": "Manufacturer Part Number (MPN) of the item. Optional.", "type": "string"}, "offerId": {"description": "Required. An identifier of the item.", "type": "string"}, "price": {"$ref": "Price", "description": "Required. The price for the product. Tax is automatically calculated for orders where marketplace facilitator tax laws are applicable. Otherwise, tax settings from Merchant Center are applied."}, "targetCountry": {"description": "Required. The CLDR territory // code of the target country of the product.", "type": "string"}, "title": {"description": "Required. The title of the product.", "type": "string"}, "variantAttributes": {"description": "Variant attributes for the item. Optional.", "items": {"$ref": "OrderLineItemProductVariantAttribute"}, "type": "array"}}, "type": "object"}, "TestOrderPaymentMethod": {"id": "TestOrderPaymentMethod", "properties": {"expirationMonth": {"description": "The card expiration month (January = 1, February = 2 etc.).", "format": "int32", "type": "integer"}, "expirationYear": {"description": "The card expiration year (4-digit, e.g. 2015).", "format": "int32", "type": "integer"}, "lastFourDigits": {"description": "The last four digits of the card number.", "type": "string"}, "predefinedBillingAddress": {"description": "The billing address. Acceptable values are: - \"`dwight`\" - \"`jim`\" - \"`pam`\" ", "type": "string"}, "type": {"description": "The type of instrument. Note that real orders might have different values than the four values accepted by `createTestOrder`. Acceptable values are: - \"`AMEX`\" - \"`DISCOVER`\" - \"`MASTERCARD`\" - \"`VISA`\" ", "type": "string"}}, "type": "object"}, "TransitTable": {"id": "TransitTable", "properties": {"postalCodeGroupNames": {"description": "A list of postal group names. The last value can be `\"all other locations\"`. Example: `[\"zone 1\", \"zone 2\", \"all other locations\"]`. The referred postal code groups must match the delivery country of the service.", "items": {"type": "string"}, "type": "array"}, "rows": {"items": {"$ref": "TransitTableTransitTimeRow"}, "type": "array"}, "transitTimeLabels": {"description": "A list of transit time labels. The last value can be `\"all other labels\"`. Example: `[\"food\", \"electronics\", \"all other labels\"]`.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TransitTableTransitTimeRow": {"id": "TransitTableTransitTimeRow", "properties": {"values": {"items": {"$ref": "TransitTableTransitTimeRowTransitTimeValue"}, "type": "array"}}, "type": "object"}, "TransitTableTransitTimeRowTransitTimeValue": {"id": "TransitTableTransitTimeRowTransitTimeValue", "properties": {"maxTransitTimeInDays": {"description": "Must be greater than or equal to `minTransitTimeInDays`.", "format": "uint32", "type": "integer"}, "minTransitTimeInDays": {"description": "Transit time range (min-max) in business days. 0 means same day delivery, 1 means next day delivery.", "format": "uint32", "type": "integer"}}, "type": "object"}, "UnitInvoice": {"id": "UnitInvoice", "properties": {"additionalCharges": {"description": "Additional charges for a unit, e.g. shipping costs.", "items": {"$ref": "UnitInvoiceAdditionalCharge"}, "type": "array"}, "promotions": {"description": "Deprecated.", "items": {"$ref": "Promotion"}, "type": "array"}, "unitPricePretax": {"$ref": "Price", "description": "[required] Price of the unit, before applying taxes."}, "unitPriceTaxes": {"description": "Tax amounts to apply to the unit price.", "items": {"$ref": "UnitInvoiceTaxLine"}, "type": "array"}}, "type": "object"}, "UnitInvoiceAdditionalCharge": {"id": "UnitInvoiceAdditionalCharge", "properties": {"additionalChargeAmount": {"$ref": "Amount", "description": "[required] Amount of the additional charge."}, "additionalChargePromotions": {"description": "Deprecated.", "items": {"$ref": "Promotion"}, "type": "array"}, "type": {"description": "[required] Type of the additional charge. Acceptable values are: - \"`shipping`\" ", "type": "string"}}, "type": "object"}, "UnitInvoiceTaxLine": {"id": "UnitInvoiceTaxLine", "properties": {"taxAmount": {"$ref": "Price", "description": "[required] Tax amount for the tax type."}, "taxName": {"description": "Optional name of the tax type. This should only be provided if `taxType` is `otherFeeTax`.", "type": "string"}, "taxType": {"description": "[required] Type of the tax. Acceptable values are: - \"`otherFee`\" - \"`otherFeeTax`\" - \"`sales`\" ", "type": "string"}}, "type": "object"}, "Value": {"description": "The single value of a rate group or the value of a rate group table's cell. Exactly one of `noShipping`, `flatRate`, `pricePercentage`, `carrierRateName`, `subtableName` must be set.", "id": "Value", "properties": {"carrierRateName": {"description": "The name of a carrier rate referring to a carrier rate defined in the same rate group. Can only be set if all other fields are not set.", "type": "string"}, "flatRate": {"$ref": "Price", "description": "A flat rate. Can only be set if all other fields are not set."}, "noShipping": {"description": "If true, then the product can't ship. Must be true when set, can only be set if all other fields are not set.", "type": "boolean"}, "pricePercentage": {"description": "A percentage of the price represented as a number in decimal notation (e.g., `\"5.4\"`). Can only be set if all other fields are not set.", "type": "string"}, "subtableName": {"description": "The name of a subtable. Can only be set in table cells (i.e., not for single values), and only if all other fields are not set.", "type": "string"}}, "type": "object"}, "Warehouse": {"description": "A fulfillment warehouse, which stores and handles inventory.", "id": "Warehouse", "properties": {"businessDayConfig": {"$ref": "BusinessDayConfig", "description": "Business days of the warehouse. If not set, will be Monday to Friday by default."}, "cutoffTime": {"$ref": "WarehouseCutoffTime", "description": "Required. The latest time of day that an order can be accepted and begin processing. Later orders will be processed in the next day. The time is based on the warehouse postal code."}, "handlingDays": {"description": "Required. The number of days it takes for this warehouse to pack up and ship an item. This is on the warehouse level, but can be overridden on the offer level based on the attributes of an item.", "format": "int64", "type": "string"}, "name": {"description": "Required. The name of the warehouse. Must be unique within account.", "type": "string"}, "shippingAddress": {"$ref": "Address", "description": "Required. Shipping address of the warehouse."}}, "type": "object"}, "WarehouseBasedDeliveryTime": {"id": "WarehouseBasedDeliveryTime", "properties": {"carrier": {"description": "Required. Carrier, such as `\"UPS\"` or `\"Fedex\"`. The list of supported carriers can be retrieved via the `listSupportedCarriers` method.", "type": "string"}, "carrierService": {"description": "Required. Carrier service, such as `\"ground\"` or `\"2 days\"`. The list of supported services for a carrier can be retrieved via the `listSupportedCarriers` method. The name of the service must be in the eddSupportedServices list.", "type": "string"}, "originAdministrativeArea": {"description": "Shipping origin's state.", "type": "string"}, "originCity": {"description": "Shipping origin's city.", "type": "string"}, "originCountry": {"description": "Shipping origin's country represented as a [CLDR territory code](http://www.unicode.org/repos/cldr/tags/latest/common/main/en.xml).", "type": "string"}, "originPostalCode": {"description": "Shipping origin.", "type": "string"}, "originStreetAddress": {"description": "Shipping origin's street address", "type": "string"}, "warehouseName": {"description": "The name of the warehouse. Warehouse name need to be matched with name. If warehouseName is set, the below fields will be ignored. The warehouse info will be read from warehouse.", "type": "string"}}, "type": "object"}, "WarehouseCutoffTime": {"id": "WarehouseCutoffTime", "properties": {"hour": {"description": "Required. Hour (24-hour clock) of the cutoff time until which an order has to be placed to be processed in the same day by the warehouse. Hour is based on the timezone of warehouse.", "format": "int32", "type": "integer"}, "minute": {"description": "Required. Min<PERSON> of the cutoff time until which an order has to be placed to be processed in the same day by the warehouse. Minute is based on the timezone of warehouse.", "format": "int32", "type": "integer"}}, "type": "object"}, "Weight": {"id": "Weight", "properties": {"unit": {"description": "Required. The weight unit. Acceptable values are: - \"`kg`\" - \"`lb`\" ", "type": "string"}, "value": {"description": "Required. The weight represented as a number. The weight can have a maximum precision of four decimal places.", "type": "string"}}, "type": "object"}}, "servicePath": "content/v2/", "title": "Content API for Shopping", "version": "v2"}