import os
import json
import sys
import logging
import asyncio
import uuid
import requests
from typing import Optional, List, Dict, Any
from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks, Response, Body
from fastapi.responses import FileResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from datetime import datetime, timedelta
from urllib.parse import urlparse
import pandas as pd
import uvicorn
from bs4 import BeautifulSoup

# Import the main script functionality
from main import (
    detect_wp_api,
    crawl_site,
    get_gsc_data_by_month,
    get_ga_data_by_month,
    build_internal_links_sheet,
    html_to_markdown,
    discover_urls_from_homepage,
    is_valid_date_format,
    get_js_rendered_html,
    extract_internal_links
)
from google.oauth2 import service_account

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import Supabase client if available
try:
    from supabase import create_client
    import hashlib
    SUPABASE_AVAILABLE = True
except ImportError:
    logger.warning("Supabase client not available. Install with: pip install supabase")
    SUPABASE_AVAILABLE = False

app = FastAPI(
    title="SEO Data Analysis API",
    description="API for crawling websites, fetching GSC/GA data, and generating SEO reports with Supabase integration.",
    version="2.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Adjust in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files (for serving the HTML dashboard)
app.mount("/static", StaticFiles(directory="public"), name="static")

# Serve the main HTML file at the root
@app.get("/")
async def serve_dashboard():
    """Serve the main dashboard HTML file"""
    return FileResponse("public/index.html")

# Pydantic models
class ConfigSchema(BaseModel):
    domain_property: str = Field(..., description="Domain property in GSC (e.g., https://example.com/)")
    ga_property_id: str = Field(..., description="Google Analytics property ID")
    service_account_file: str = Field(..., description="Path to Google service account JSON file")
    homepage: Optional[str] = Field(None, description="Homepage URL (defaults to domain_property)")
    start_date: Optional[str] = Field(None, description="Start date for data (YYYY-MM-DD)")
    end_date: Optional[str] = Field(None, description="End date for data (YYYY-MM-DD)")
    website_urls: Optional[List[str]] = Field(None, description="Specific URLs to crawl (optional)")
    wp_api_key: Optional[str] = Field(None, description="WordPress API key if available")
    supabase_url: Optional[str] = Field(None, description="Supabase URL")
    supabase_key: Optional[str] = Field(None, description="Supabase API key")
    generate_excel: Optional[bool] = Field(True, description="Whether to generate Excel report")

class ExcelRequestSchema(BaseModel):
    domain: str = Field(..., description="Domain to generate report for")
    date: Optional[str] = Field(None, description="Specific snapshot date (YYYY-MM-DD)")
    supabase_url: str = Field(..., description="Supabase URL")
    supabase_key: str = Field(..., description="Supabase API key")

class SupabaseClient:
    def __init__(self, url, key, domain):
        self.client = create_client(url, key)
        self.domain = domain
        self.site_id = self._get_or_create_site_id()
        logger.info(f"Initialized Supabase client for domain: {domain} (site_id: {self.site_id})")
        
    def _get_or_create_site_id(self):
        """Get or create a site ID for the domain"""
        # Check if site exists
        response = self.client.table('sites').select('id').eq('domain', self.domain).execute()
        
        if response.data and len(response.data) > 0:
            return response.data[0]['id']
        
        # Create new site
        response = self.client.table('sites').insert({
            'domain': self.domain,
            'created_at': datetime.now().isoformat()
        }).execute()
        
        if response.data and len(response.data) > 0:
            return response.data[0]['id']
        
        raise Exception(f"Failed to get or create site ID for {self.domain}")
    
    def save_pages_data(self, df):
        """Save pages data to Supabase"""
        if df.empty:
            return
            
        # Add site_id and snapshot_date
        df = df.copy()
        df['site_id'] = self.site_id
        df['snapshot_date'] = datetime.now().date().isoformat()
        
        # Convert to records
        records = df.to_dict(orient='records')
        
        # Insert data
        response = self.client.table('pages').upsert(
            records, 
            on_conflict='site_id,URL,snapshot_date'
        ).execute()
        
        logger.info(f"Saved {len(records)} pages to Supabase")
        return response.data
    
    def save_internal_links(self, df):
        """Save internal links to Supabase with change tracking"""
        if df.empty:
            return
            
        # Add site_id, snapshot_date, and link_hash
        df = df.copy()
        df['site_id'] = self.site_id
        df['snapshot_date'] = datetime.now().date().isoformat()
        df['link_hash'] = df.apply(
            lambda row: hashlib.md5(
                f"{row['URL']}|{row['Target Hyperlink']}|{row['Anchor Text']}".encode()
            ).hexdigest(), 
            axis=1
        )
        
        # Convert to records
        records = df.to_dict(orient='records')
        
        # Insert data
        response = self.client.table('internal_links').upsert(
            records,
            on_conflict='site_id,link_hash,snapshot_date'
        ).execute()
        
        logger.info(f"Saved {len(records)} internal links to Supabase")
        return response.data
    
    def save_gsc_keywords(self, df):
        """Save GSC keywords to Supabase"""
        if df.empty:
            return
            
        # Add site_id
        df = df.copy()
        df['site_id'] = self.site_id
        
        # Convert to records
        records = df.to_dict(orient='records')
        
        # Insert data
        response = self.client.table('gsc_keywords').upsert(
            records,
            on_conflict='site_id,URL,Keyword,Month'
        ).execute()
        
        logger.info(f"Saved {len(records)} GSC keywords to Supabase")
        return response.data
    
    def save_gsc_traffic(self, df):
        """Save GSC traffic to Supabase"""
        if df.empty:
            return
            
        # Add site_id
        df = df.copy()
        df['site_id'] = self.site_id
        
        # Convert to records
        records = df.to_dict(orient='records')
        
        # Insert data
        response = self.client.table('gsc_traffic').upsert(
            records,
            on_conflict='site_id,URL,Month'
        ).execute()
        
        logger.info(f"Saved {len(records)} GSC traffic records to Supabase")
        return response.data
    
    def save_ga_data(self, df):
        """Save GA data to Supabase"""
        if df.empty:
            return

        # Add site_id
        df = df.copy()
        df['site_id'] = self.site_id

        # Filter to only include columns that exist in the database schema
        # Database schema only has: URL, Google Analytics Page Views, Active Users, Month
        valid_columns = ['URL', 'Google Analytics Page Views', 'Active Users', 'Month', 'site_id']
        df = df[[col for col in valid_columns if col in df.columns]]

        # Replace NaN values with None
        df = df.replace({float('nan'): None})

        # Convert to records
        records = df.to_dict(orient='records')

        # Insert data
        response = self.client.table('ga_data').upsert(
            records,
            on_conflict='site_id,URL,Month'
        ).execute()

        logger.info(f"Saved {len(records)} GA records to Supabase")
        return response.data
    
    def generate_excel_report(self, output_dir, date_filter=None):
        """Generate Excel report from Supabase data"""
        logger.info(f"Generating Excel report from Supabase data for {self.domain}")
        
        # Build queries
        pages_query = self.client.table('pages').select('*').eq('site_id', self.site_id)
        keywords_query = self.client.table('gsc_keywords').select('*').eq('site_id', self.site_id)
        traffic_query = self.client.table('gsc_traffic').select('*').eq('site_id', self.site_id)
        links_query = self.client.table('internal_links').select('*').eq('site_id', self.site_id)
        
        # Apply date filter if provided
        if date_filter:
            pages_query = pages_query.eq('snapshot_date', date_filter)
            links_query = links_query.eq('snapshot_date', date_filter)
        
        # Execute queries
        pages_data = pages_query.execute()
        keywords_data = keywords_query.execute()
        traffic_data = traffic_query.execute()
        links_data = links_query.execute()
        
        # Convert to DataFrames
        data_df = pd.DataFrame(pages_data.data) if pages_data.data else pd.DataFrame()
        keywords_df = pd.DataFrame(keywords_data.data) if keywords_data.data else pd.DataFrame()
        hist_traffic_df = pd.DataFrame(traffic_data.data) if traffic_data.data else pd.DataFrame()
        internal_links_df = pd.DataFrame(links_data.data) if links_data.data else pd.DataFrame()
        
        # Remove site_id column for Excel export
        for df in [data_df, keywords_df, hist_traffic_df, internal_links_df]:
            if not df.empty and 'site_id' in df.columns:
                df.drop('site_id', axis=1, inplace=True)
        
        # Generate filename with date filter if provided
        date_str = f"_{date_filter}" if date_filter else f"_{datetime.now().strftime('%Y%m%d')}"
        excel_path = os.path.join(output_dir, f'report_{self.domain.replace(".", "_")}{date_str}.xlsx')
        os.makedirs(output_dir, exist_ok=True)
        
        # Write to Excel
        with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
            data_df.to_excel(writer, sheet_name='Data', index=False)
            keywords_df.to_excel(writer, sheet_name='Keywords', index=False)
            hist_traffic_df.to_excel(writer, sheet_name='Historical-Traffic', index=False)
            internal_links_df.to_excel(writer, sheet_name='Internal-Links', index=False)
        
        logger.info(f"Excel report generated: {excel_path}")
        return excel_path

# In-memory storage for task progress
task_progress = {}

async def run_seo_analysis(config_dict: dict, task_id: str):
    """Run SEO analysis in the background and update progress"""
    try:
        # Initialize progress
        task_progress[task_id] = {
            "status": "running",
            "progress": 0,
            "message": "Starting analysis...",
            "result": None,
            "error": None
        }
        
        # Extract config values
        domain_property = config_dict['domain_property']
        ga_property_id = config_dict['ga_property_id']
        service_account_file = config_dict['service_account_file']
        
        # Optional values
        homepage = config_dict.get('homepage') or ("https://" + urlparse(domain_property).netloc)
        date_from = config_dict.get('start_date')
        date_to = config_dict.get('end_date')
        
        if date_from is None:
            date_from = (datetime.today() - timedelta(days=365)).strftime('%Y-%m-%d')
        if date_to is None:
            date_to = datetime.today().strftime('%Y-%m-%d')

        if not is_valid_date_format(date_from) or not is_valid_date_format(date_to):
            raise ValueError("Invalid date format. Dates must be in the form YYYY-MM-DD")

        # Update progress
        task_progress[task_id]["progress"] = 5
        task_progress[task_id]["message"] = "Checking for WordPress API..."
        await asyncio.sleep(0.1)  # Allow progress to be sent

        # --- Step 1: Check for WordPress API or fallback to crawling ---
        crawl_results = []
        if 'website_urls' in config_dict and config_dict['website_urls']:
            website_urls = config_dict['website_urls']
            task_progress[task_id]["message"] = f"Using {len(website_urls)} URLs provided in configuration"
        else:
            # Try to detect and use WordPress API
            wp_data = detect_wp_api(homepage)
            
            if wp_data and 'publish_seo_data' in wp_data and len(wp_data['publish_seo_data']) > 1:
                task_progress[task_id]["message"] = f"Using WordPress API data. Found {len(wp_data['publish_seo_data'])} pages."
                crawl_results = wp_data['publish_seo_data']
                website_urls = [item['url'] for item in crawl_results]
            else:
                task_progress[task_id]["message"] = "WordPress API not available. Discovering URLs..."
                
                # Discover URLs by crawling
                website_urls = await discover_urls_from_homepage_async(homepage)
                
                # If we still only have the homepage, try one more approach
                if len(website_urls) <= 1:
                    task_progress[task_id]["message"] = "Limited URLs discovered. Trying alternative method..."
                    try:
                        import requests
                        response = requests.get(homepage, timeout=15)
                        html = response.text
                        more_links = extract_internal_links(homepage, html)
                        website_urls = list(set(website_urls + more_links))
                        task_progress[task_id]["message"] = f"Alternative method found {len(website_urls)} URLs"
                    except Exception as e:
                        task_progress[task_id]["message"] = f"Alternative discovery failed: {e}"

        # Update progress
        task_progress[task_id]["progress"] = 10
        task_progress[task_id]["message"] = "Setting up output directory..."
        await asyncio.sleep(0.1)

        credentials = service_account.Credentials.from_service_account_file(service_account_file)
        output_dir = f"reports_{urlparse(domain_property).netloc.replace('.', '_')}"
        os.makedirs(output_dir, exist_ok=True)

        # Update progress
        task_progress[task_id]["progress"] = 15
        task_progress[task_id]["message"] = "Preparing to crawl website..."
        await asyncio.sleep(0.1)

        # Only crawl if we don't have WP API data or if it's limited
        if not crawl_results or len(crawl_results) <= 1:
            task_progress[task_id]["message"] = f"Crawling {len(website_urls)} URLs..."
            crawl_results = await crawl_site_async(website_urls, output_dir)
        
        # Process crawl results
        task_progress[task_id]["progress"] = 40
        task_progress[task_id]["message"] = "Processing crawl results..."
        await asyncio.sleep(0.1)
        
        for page in crawl_results:
            if 'text' in page and page['text']:
                page['text'] = html_to_markdown(page['text'])
        
        crawl_df = pd.DataFrame(crawl_results)

        # Check if DataFrame is empty or missing expected columns
        if crawl_df.empty:
            # Create empty DataFrame with expected columns
            crawl_df = pd.DataFrame(columns=[
                'url', 'title', 'description', 'h1', 'text'
            ])
        
        # Rename columns - do this regardless of whether data exists
        crawl_df = crawl_df.rename(columns={
            'url': 'URL',
            'title': 'SEO Title',
            'description': 'Meta Description',
            'h1': 'H1',
            'text': 'Page Content'
        })
        
        # Now safely calculate title length
        crawl_df['Title Length'] = crawl_df['SEO Title'].apply(
            lambda t: len(t) if isinstance(t, str) and pd.notna(t) else 0
        )
        
        # Placeholder columns
        crawl_df['Focus Keyword'] = ''
        crawl_df['Page Type'] = ''
        crawl_df['Topic'] = ''

        # Update progress
        task_progress[task_id]["progress"] = 50
        task_progress[task_id]["message"] = "Fetching Google Search Console data..."
        await asyncio.sleep(0.1)

        # --- Google Search Console (Monthly) ---
        gsc_df = get_gsc_data_by_month(domain_property, date_from, date_to, credentials)
        expected_gsc_cols = ['URL', 'Keyword', 'Clicks', 'Impressions', 'CTR', 'Position', 'Month']
        for col in expected_gsc_cols:
            if col not in gsc_df.columns:
                gsc_df[col] = []

        keywords_df = gsc_df.copy()  # for "Keywords" sheet

        # Update progress
        task_progress[task_id]["progress"] = 60
        task_progress[task_id]["message"] = "Processing GSC data..."
        await asyncio.sleep(0.1)

        # Build "Historical-Traffic" sheet (aggregate GSC by URL, Month)
        if not gsc_df.empty:
            hist_traffic_df = (
                gsc_df.groupby(['URL', 'Month'], as_index=False)
                     .agg({'Clicks': 'sum', 'Impressions': 'sum'})
            )
            hist_traffic_df['CTR'] = hist_traffic_df.apply(
                lambda row: (row['Clicks'] / row['Impressions']) if row['Impressions'] else 0,
                axis=1
            )
            pos_weight = (
                gsc_df.assign(weighted_pos=lambda df: df['Position'] * df['Impressions'])
                     .groupby(['URL', 'Month'], as_index=False)
                     .agg({'weighted_pos': 'sum', 'Impressions': 'sum'})
            )
            pos_weight = pos_weight.rename(columns={'Impressions': 'IMP_sum'})
            hist_traffic_df = hist_traffic_df.merge(
                pos_weight[['URL', 'Month', 'weighted_pos', 'IMP_sum']],
                on=['URL', 'Month'],
                how='left'
            ).fillna({'weighted_pos': 0, 'IMP_sum': 1})
            hist_traffic_df['Position'] = hist_traffic_df.apply(
                lambda row: (row['weighted_pos'] / row['IMP_sum']) if row['IMP_sum'] else 0,
                axis=1
            )
            hist_traffic_df = hist_traffic_df.drop(columns=['weighted_pos', 'IMP_sum'])
        else:
            hist_traffic_df = pd.DataFrame(columns=['URL', 'Month', 'Clicks', 'Impressions', 'CTR', 'Position'])

        # Build total GSC per URL for "Data" sheet
        if not gsc_df.empty:
            gsc_total_df = (
                gsc_df.groupby('URL', as_index=False)
                      .agg({'Clicks': 'sum', 'Impressions': 'sum'})
            )
            gsc_total_df['CTR'] = gsc_total_df.apply(
                lambda row: (row['Clicks'] / row['Impressions']) if row['Impressions'] else 0,
                axis=1
            )

            pos_weight_all = (
                gsc_df.assign(weighted_pos=lambda df: df['Position'] * df['Impressions'])
                      .groupby('URL', as_index=False)
                      .agg({'weighted_pos': 'sum', 'Impressions': 'sum'})
            )
            pos_weight_all = pos_weight_all.rename(columns={'Impressions': 'IMP_sum'})
            gsc_total_df = gsc_total_df.merge(
                pos_weight_all[['URL', 'weighted_pos', 'IMP_sum']],
                on='URL',
                how='left'
            ).fillna({'weighted_pos': 0, 'IMP_sum': 1})
            gsc_total_df['Position'] = gsc_total_df.apply(
                lambda row: (row['weighted_pos'] / row['IMP_sum']) if row['IMP_sum'] else 0,
                axis=1
            )
            gsc_total_df = gsc_total_df.drop(columns=['weighted_pos', 'IMP_sum'])
            gsc_total_df = gsc_total_df.rename(columns={
                'Clicks': 'GSC Clicks',
                'Impressions': 'GSC Impressions'
            })
        else:
            gsc_total_df = pd.DataFrame(columns=[
                'URL', 'GSC Clicks', 'GSC Impressions', 'CTR', 'Position'
            ])

        # Update progress
        task_progress[task_id]["progress"] = 70
        task_progress[task_id]["message"] = "Fetching Google Analytics data..."
        await asyncio.sleep(0.1)

        # --- Google Analytics (Monthly) ---
        ga_df = get_ga_data_by_month(ga_property_id, date_from, date_to, credentials)
        expected_ga_cols = [
            "landingPagePlusQueryString", "date", "country", "region",
            "pageTitle", "pageReferrer", "newVsReturning",
            "activeUsers", "screenPageViews", "Month"
        ]
        for col in expected_ga_cols:
            if col not in ga_df.columns:
                ga_df[col] = ""

        # Process GA data
        if not ga_df.empty:
            ga_df = ga_df[ga_df['landingPagePlusQueryString'].str.startswith('/')].copy()
            ga_df['screenPageViews'] = ga_df['screenPageViews'].astype(int)
            ga_df['activeUsers'] = ga_df['activeUsers'].astype(int)
            base = homepage.rstrip('/')
            ga_df['URL'] = base + ga_df['landingPagePlusQueryString']

            # Create aggregated data for the Data sheet
            ga_total_df = (
                ga_df.groupby('URL', as_index=False)
                     .agg({'screenPageViews': 'sum', 'activeUsers': 'sum'})
                     .rename(columns={
                         'screenPageViews': 'Google Analytics Page Views',
                         'activeUsers': 'Active Users'
                     })
            )

            # Create properly formatted GA data for Supabase (with Month dimension)
            ga_df_for_supabase = ga_df.rename(columns={
                'screenPageViews': 'Google Analytics Page Views',
                'activeUsers': 'Active Users'
            })
            # Keep only the columns that exist in the database schema
            ga_df_for_supabase = ga_df_for_supabase[['URL', 'Google Analytics Page Views', 'Active Users', 'Month']]
        else:
            ga_total_df = pd.DataFrame(columns=[
                'URL', 'Google Analytics Page Views', 'Active Users'
            ])
            ga_df_for_supabase = pd.DataFrame(columns=[
                'URL', 'Google Analytics Page Views', 'Active Users', 'Month'
            ])

        # Update progress
        task_progress[task_id]["progress"] = 80
        task_progress[task_id]["message"] = "Building final data sheets..."
        await asyncio.sleep(0.1)

        # --- Merge into "Data" sheet DataFrame ---
        data_df = crawl_df.merge(gsc_total_df, on='URL', how='left')
        data_df = data_df.merge(ga_total_df, on='URL', how='left')

        # Fill NaNs with 0 for numeric metrics
        for col in ['GSC Clicks', 'GSC Impressions', 'Google Analytics Page Views', 'Active Users']:
            if col in data_df.columns:
                data_df[col] = data_df[col].fillna(0)
                data_df[col] = data_df[col].astype(int)

        # Reorder columns
        data_columns = [
            'URL',
            'GSC Clicks',
            'GSC Impressions',
            'Google Analytics Page Views',
            'Focus Keyword',
            'Page Type',
            'Topic',
            'Page Content',
            'SEO Title',
            'Title Length',
            'Meta Description',
            'H1'
        ]
        data_df = data_df[[col for col in data_columns if col in data_df.columns]]

        # --- Build "Internal-Links" sheet ---
        task_progress[task_id]["progress"] = 85
        task_progress[task_id]["message"] = "Building internal links data..."
        await asyncio.sleep(0.1)
        
        internal_links_df = build_internal_links_sheet(crawl_results, data_df)

        # --- Save to Supabase if configured ---
        if SUPABASE_AVAILABLE and config_dict.get('supabase_url') and config_dict.get('supabase_key'):
            task_progress[task_id]["progress"] = 90
            task_progress[task_id]["message"] = "Saving data to Supabase..."
            await asyncio.sleep(0.1)
            
            try:
                domain = urlparse(domain_property).netloc
                supabase_client = SupabaseClient(
                    url=config_dict['supabase_url'],
                    key=config_dict['supabase_key'],
                    domain=domain
                )
                
                # Save all data to Supabase
                supabase_client.save_pages_data(data_df)
                supabase_client.save_gsc_keywords(keywords_df)
                supabase_client.save_gsc_traffic(hist_traffic_df)
                supabase_client.save_internal_links(internal_links_df)
                
                # Also save GA data if available (use properly formatted data)
                if not ga_df.empty:
                    supabase_client.save_ga_data(ga_df_for_supabase)
                
                task_progress[task_id]["message"] = "Successfully saved all data to Supabase"
                
                # Generate Excel report if requested
                if config_dict.get('generate_excel', True):
                    task_progress[task_id]["progress"] = 95
                    task_progress[task_id]["message"] = "Generating Excel report from Supabase data..."
                    await asyncio.sleep(0.1)
                    
                    excel_path = supabase_client.generate_excel_report(output_dir)
                    task_progress[task_id]["message"] = f"Excel report generated: {excel_path}"
                else:
                    excel_path = None
            except Exception as e:
                logger.error(f"Error with Supabase: {e}")
                # Fall back to direct Excel generation
                task_progress[task_id]["message"] = f"Supabase error: {e}. Falling back to direct Excel generation."
                excel_path = os.path.join(output_dir, 'final_report.xlsx')
                with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
                    data_df.to_excel(writer, sheet_name='Data', index=False)
                    keywords_df.to_excel(writer, sheet_name='Keywords', index=False)
                    hist_traffic_df.to_excel(writer, sheet_name='Historical-Traffic', index=False)
                    internal_links_df.to_excel(writer, sheet_name='Internal-Links', index=False)
        else:
            # --- Write to Multi-Sheet Excel if no Supabase ---
            task_progress[task_id]["progress"] = 95
            task_progress[task_id]["message"] = "Generating Excel report directly..."
            await asyncio.sleep(0.1)
            
            excel_path = os.path.join(output_dir, 'final_report.xlsx')
            with pd.ExcelWriter(excel_path, engine='xlsxwriter') as writer:
                data_df.to_excel(writer, sheet_name='Data', index=False)
                keywords_df.to_excel(writer, sheet_name='Keywords', index=False)
                hist_traffic_df.to_excel(writer, sheet_name='Historical-Traffic', index=False)
                internal_links_df.to_excel(writer, sheet_name='Internal-Links', index=False)

        # Complete the task
        task_progress[task_id]["progress"] = 100
        task_progress[task_id]["status"] = "completed"
        task_progress[task_id]["message"] = "Analysis completed successfully"
        task_progress[task_id]["result"] = {
            "output_directory": output_dir,
            "excel_report": excel_path,
            "stats": {
                "pages_analyzed": len(data_df),
                "keywords_analyzed": len(keywords_df),
                "internal_links_found": len(internal_links_df)
            }
        }
        
    except Exception as e:
        logger.exception("Error in analysis task:")
        task_progress[task_id]["status"] = "failed"
        task_progress[task_id]["message"] = f"Analysis failed: {str(e)}"
        task_progress[task_id]["error"] = str(e)

async def generate_excel_report(request_data: Dict[str, Any], task_id: str):
    """
    Generate Excel report from Supabase data
    """
    try:
        # Initialize progress
        task_progress[task_id] = {
            "status": "running",
            "progress": 0,
            "message": "Starting Excel generation...",
            "result": None,
            "error": None
        }
        
        # Extract request data
        domain = request_data['domain']
        date = request_data.get('date')
        supabase_url = request_data['supabase_url']
        supabase_key = request_data['supabase_key']
        
        # Update progress
        task_progress[task_id]["progress"] = 20
        task_progress[task_id]["message"] = f"Connecting to Supabase for domain: {domain}"
        await asyncio.sleep(0.1)
        
        # Initialize Supabase client
        supabase_client = SupabaseClient(
            url=supabase_url,
            key=supabase_key,
            domain=domain
        )
        
        # Update progress
        task_progress[task_id]["progress"] = 50
        task_progress[task_id]["message"] = "Fetching data from Supabase..."
        await asyncio.sleep(0.1)
        
        # Generate Excel report
        output_dir = f"reports_{domain.replace('.', '_')}"
        os.makedirs(output_dir, exist_ok=True)
        
        excel_path = supabase_client.generate_excel_report(output_dir, date)
        
        # Complete the task
        task_progress[task_id]["progress"] = 100
        task_progress[task_id]["status"] = "completed"
        task_progress[task_id]["message"] = "Excel report generated successfully"
        task_progress[task_id]["result"] = {
            "excel_report": excel_path
        }
        
    except Exception as e:
        logger.exception("Error in Excel generation task:")
        task_progress[task_id]["status"] = "failed"
        task_progress[task_id]["message"] = f"Excel generation failed: {str(e)}"
        task_progress[task_id]["error"] = str(e)

# API endpoint to run analysis
@app.post("/generate_report/")
async def generate_report(config_data: ConfigSchema, background_tasks: BackgroundTasks):
    """
    Runs the SEO data analysis using configuration provided in the request body and generates a report.
    """
    try:
        # Convert Pydantic model to a dictionary
        config_dict = config_data.model_dump(exclude_unset=True)
        
        # Generate a unique task ID
        task_id = str(uuid.uuid4())
        
        # Add task to background tasks
        background_tasks.add_task(run_seo_analysis, config_dict, task_id)
        
        return {
            "task_id": task_id,
            "status": "running",
            "message": "Analysis started. Check progress with /task/{task_id}"
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Configuration error: {e}")
    except Exception as e:
        logger.exception("Error running analysis:")
        raise HTTPException(status_code=500, detail=f"An internal server error occurred: {e}")

# API endpoint to get task progress
@app.get("/task/{task_id}")
async def get_task_progress(task_id: str):
    """
    Get the current progress of a running task.
    """
    if task_id in task_progress:
        return task_progress[task_id]
    else:
        return {
            "status": "not_found",
            "message": "Task not found or completed"
        }

# API endpoint to generate Excel report from Supabase
@app.post("/generate_excel_report/")
async def generate_excel_report_endpoint(excel_request: ExcelRequestSchema, background_tasks: BackgroundTasks):
    """
    Generate an Excel report from Supabase data for a specific domain and date.
    """
    try:
        # Convert Pydantic model to a dictionary
        request_data = excel_request.model_dump(exclude_unset=True)
        
        # Generate a unique task ID
        task_id = str(uuid.uuid4())
        
        # Add task to background tasks
        background_tasks.add_task(generate_excel_report, request_data, task_id)
        
        return {
            "task_id": task_id,
            "status": "running",
            "message": "Excel report generation started. Check progress with /task/{task_id}"
        }
    except Exception as e:
        logger.exception("Error generating Excel report:")
        raise HTTPException(status_code=500, detail=f"An internal server error occurred: {e}")

# Add these endpoints to your api.py file to support the HTML interface

@app.get("/download/{file_path:path}")
async def download_file(file_path: str):
    """
    Download a generated report file
    """
    # Security check to prevent directory traversal
    if ".." in file_path:
        raise HTTPException(status_code=400, detail="Invalid file path")
    
    # Normalize path
    file_path = os.path.normpath(file_path)
    
    # Check if file exists
    if not os.path.isfile(file_path):
        raise HTTPException(status_code=404, detail="File not found")
    
    return FileResponse(
        path=file_path, 
        filename=os.path.basename(file_path),
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    )

@app.options("/generate_report/")
async def options_generate_report():
    """
    Handle CORS preflight requests for the generate_report endpoint
    """
    return Response(status_code=200)

@app.post("/generate_report_with_service_account/")
async def generate_report_with_service_account(
    background_tasks: BackgroundTasks,
    config_data: dict = Body(...)
):
    """
    Runs the SEO data analysis with service account data in the request body
    """
    try:
        # Extract service account data
        service_account_data = config_data.pop("service_account_data", None)
        if not service_account_data:
            raise HTTPException(status_code=400, detail="Service account data is required")
        
        # Create a temporary file for the service account
        temp_file = f"temp_sa_{uuid.uuid4()}.json"
        with open(temp_file, "w") as f:
            json.dump(service_account_data, f)
        
        # Update the config to use the temp file
        config_data["service_account_file"] = temp_file
        
        # Generate a unique task ID
        task_id = str(uuid.uuid4())
        
        # Add task to background tasks
        background_tasks.add_task(run_seo_analysis, config_data, task_id)
        background_tasks.add_task(lambda: os.remove(temp_file) if os.path.exists(temp_file) else None)
        
        return {
            "task_id": task_id,
            "status": "running",
            "message": "Analysis started. Check progress with /task/{task_id}"
        }
    except Exception as e:
        logger.exception("Error processing request with service account data:")
        raise HTTPException(status_code=500, detail=str(e))

async def discover_urls_from_homepage_async(homepage):
    """
    Async version of discover_urls_from_homepage for use in the API
    """
    print(f"[INFO] Discovering URLs from homepage {homepage}...")
    try:
        # Use JavaScript rendering to get the full HTML
        html = await get_js_rendered_html(homepage)
        
        # Extract all internal links
        links = extract_internal_links(homepage, html)
        
        # Add the homepage itself and remove duplicates
        all_urls = [homepage] + links
        unique_urls = list(dict.fromkeys(all_urls))  # Remove duplicates while preserving order
        
        print(f"[INFO] Discovered {len(unique_urls)} URLs: {unique_urls}")
        return unique_urls
    except Exception as e:
        print(f"[ERROR] Failed to discover URLs from homepage {homepage}: {e}")
        return [homepage]

async def crawl_url_async(url):
    try:        
        response = requests.get(url, timeout=10)
        if response.status_code != 200:
            print(f"Skipping dead link: {url} (Status code: {response.status_code})")
            return None
        
        html_content = response.text
        soup = BeautifulSoup(html_content, 'html.parser')
        title = soup.title.string if soup.title else 'No title'
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        h1 = soup.find('h1')        
        
        return {
            'url': url,
            'title': title,
            'description': meta_desc['content'] if meta_desc else '',
            'h1': h1.text if h1 else '',
            'text': html_to_markdown(html_content),
            'raw_html': html_content
        }
    except Exception as e:
        print(f"Error crawling {url}: {e}")
        return None

async def crawl_site_async(urls, output_dir):
    results = []
    failed_urls = []
    for url in urls:
        data = await crawl_url_async(url)
        if not data:
            try:
                print(f"Trying JS rendering for {url}...")
                html = await get_js_rendered_html(url)
                soup = BeautifulSoup(html, 'html.parser')
                
                # Convert HTML to markdown for better LLM processing
                text = html_to_markdown(html)
                
                data = {
                    'url': url,
                    'title': soup.title.string if soup.title else 'No title',
                    'description': soup.find('meta', attrs={'name': 'description'})['content'] if soup.find('meta', attrs={'name': 'description'}) else '',
                    'h1': soup.find('h1').text if soup.find('h1') else '',
                    'text': text,
                    'raw_html': html
                }
            except Exception as e:
                print(f"JS rendering failed for {url}: {e}")
                failed_urls.append(url)
                continue
        if data:
            results.append(data)

    if failed_urls:
        with open(os.path.join(output_dir, 'failed_urls.txt'), 'w') as f:
            for url in failed_urls:
                f.write(url + '\n')

    return results

# CLI entry point
if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "serve":
        # Run as API server
        port = int(os.environ.get("PORT", 8000))
        uvicorn.run("api:app", host="0.0.0.0", port=port, reload=True)
    elif len(sys.argv) > 1:
        # Run as CLI with config file
        config_path = sys.argv[1]
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
            
            # Run the analysis
            asyncio.run(run_seo_analysis(config))
        except Exception as e:
            logger.exception(f"Error running analysis from config file {config_path}:")
            sys.exit(1)
    else:
        print("Usage:")
        print("  python api.py <config_file.json>  # Run analysis with config file")
        print("  python api.py serve               # Start API server")
        sys.exit(1)









