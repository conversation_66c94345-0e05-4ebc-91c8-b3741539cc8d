"""
Report generation service
"""
import os
import pandas as pd
from datetime import datetime
from typing import List, Optional, Dict, Any
from urllib.parse import urlparse

from src.models.schemas import CrawlResult
from src.utils.logging import get_logger

logger = get_logger(__name__)


class ReportService:
    """Service for generating various types of reports"""
    
    def generate_excel_report(self, crawl_results: List[CrawlResult], 
                            keywords_df: pd.DataFrame, traffic_df: pd.DataFrame,
                            internal_links_df: pd.DataFrame, ga_df: pd.DataFrame,
                            output_dir: str) -> str:
        """Generate comprehensive Excel report"""
        logger.info("Generating Excel report...")
        
        # Create Excel file path
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        excel_path = os.path.join(output_dir, f'seo_analysis_report_{timestamp}.xlsx')
        
        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            # Pages data sheet
            if crawl_results:
                pages_data = []
                for result in crawl_results:
                    pages_data.append({
                        'URL': result.url,
                        'Title': result.title,
                        'Description': result.description,
                        'H1': result.h1,
                        'Content Length': len(result.text) if result.text else 0,
                        'Word Count': len(result.text.split()) if result.text else 0
                    })
                
                pages_df = pd.DataFrame(pages_data)
                pages_df.to_excel(writer, sheet_name='Pages', index=False)
                logger.info(f"Added {len(pages_data)} pages to Excel report")
            
            # Keywords sheet
            if not keywords_df.empty:
                keywords_df.to_excel(writer, sheet_name='Keywords', index=False)
                logger.info(f"Added {len(keywords_df)} keywords to Excel report")
            
            # Traffic sheet
            if not traffic_df.empty:
                traffic_df.to_excel(writer, sheet_name='Traffic', index=False)
                logger.info(f"Added {len(traffic_df)} traffic records to Excel report")
            
            # Internal links sheet
            if not internal_links_df.empty:
                internal_links_df.to_excel(writer, sheet_name='Internal Links', index=False)
                logger.info(f"Added {len(internal_links_df)} internal links to Excel report")
            
            # Google Analytics sheet
            if not ga_df.empty:
                ga_df.to_excel(writer, sheet_name='Analytics', index=False)
                logger.info(f"Added {len(ga_df)} GA records to Excel report")
            
            # Summary sheet
            summary_data = {
                'Metric': [
                    'Total Pages Analyzed',
                    'Total Keywords',
                    'Total Traffic Records',
                    'Total Internal Links',
                    'Total GA Records',
                    'Report Generated'
                ],
                'Value': [
                    len(crawl_results) if crawl_results else 0,
                    len(keywords_df) if not keywords_df.empty else 0,
                    len(traffic_df) if not traffic_df.empty else 0,
                    len(internal_links_df) if not internal_links_df.empty else 0,
                    len(ga_df) if not ga_df.empty else 0,
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                ]
            }
            
            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='Summary', index=False)
        
        logger.info(f"Excel report generated: {excel_path}")
        return excel_path
    
    def generate_csv_reports(self, crawl_results: List[CrawlResult], 
                           keywords_df: pd.DataFrame, traffic_df: pd.DataFrame,
                           internal_links_df: pd.DataFrame, ga_df: pd.DataFrame,
                           output_dir: str) -> List[str]:
        """Generate individual CSV reports"""
        logger.info("Generating CSV reports...")
        csv_files = []
        
        # Pages CSV
        if crawl_results:
            pages_data = []
            for result in crawl_results:
                pages_data.append({
                    'URL': result.url,
                    'Title': result.title,
                    'Description': result.description,
                    'H1': result.h1,
                    'Content Length': len(result.text) if result.text else 0,
                    'Word Count': len(result.text.split()) if result.text else 0
                })
            
            pages_df = pd.DataFrame(pages_data)
            pages_csv = os.path.join(output_dir, 'pages.csv')
            pages_df.to_csv(pages_csv, index=False)
            csv_files.append(pages_csv)
        
        # Keywords CSV
        if not keywords_df.empty:
            keywords_csv = os.path.join(output_dir, 'keywords.csv')
            keywords_df.to_csv(keywords_csv, index=False)
            csv_files.append(keywords_csv)
        
        # Traffic CSV
        if not traffic_df.empty:
            traffic_csv = os.path.join(output_dir, 'traffic.csv')
            traffic_df.to_csv(traffic_csv, index=False)
            csv_files.append(traffic_csv)
        
        # Internal links CSV
        if not internal_links_df.empty:
            links_csv = os.path.join(output_dir, 'internal_links.csv')
            internal_links_df.to_csv(links_csv, index=False)
            csv_files.append(links_csv)
        
        # Analytics CSV
        if not ga_df.empty:
            ga_csv = os.path.join(output_dir, 'analytics.csv')
            ga_df.to_csv(ga_csv, index=False)
            csv_files.append(ga_csv)
        
        logger.info(f"Generated {len(csv_files)} CSV files")
        return csv_files
    
    def generate_summary_report(self, crawl_results: List[CrawlResult], 
                              keywords_df: pd.DataFrame, traffic_df: pd.DataFrame,
                              internal_links_df: pd.DataFrame, ga_df: pd.DataFrame,
                              domain: str) -> Dict[str, Any]:
        """Generate a summary report with key metrics"""
        summary = {
            'domain': domain,
            'analysis_date': datetime.now().isoformat(),
            'metrics': {
                'total_pages': len(crawl_results) if crawl_results else 0,
                'total_keywords': len(keywords_df) if not keywords_df.empty else 0,
                'total_traffic_records': len(traffic_df) if not traffic_df.empty else 0,
                'total_internal_links': len(internal_links_df) if not internal_links_df.empty else 0,
                'total_ga_records': len(ga_df) if not ga_df.empty else 0
            }
        }
        
        # Add top performing pages if available
        if not traffic_df.empty and 'clicks' in traffic_df.columns:
            top_pages = traffic_df.nlargest(10, 'clicks')[['page', 'clicks']].to_dict('records')
            summary['top_pages_by_clicks'] = top_pages
        
        # Add top keywords if available
        if not keywords_df.empty and 'clicks' in keywords_df.columns:
            top_keywords = keywords_df.nlargest(10, 'clicks')[['query', 'clicks']].to_dict('records')
            summary['top_keywords_by_clicks'] = top_keywords
        
        return summary
