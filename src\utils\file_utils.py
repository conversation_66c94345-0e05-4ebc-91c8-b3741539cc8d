"""
File handling utilities
"""
import os
import json
import tempfile
import uuid
from typing import Dict, Any, Optional
from pathlib import Path

from src.config.settings import settings
from src.utils.logging import get_logger

logger = get_logger(__name__)


def ensure_directory(path: str) -> None:
    """Ensure a directory exists, create if it doesn't"""
    os.makedirs(path, exist_ok=True)


def create_temp_file(content: str, suffix: str = '.json') -> str:
    """Create a temporary file with the given content"""
    temp_file = os.path.join(settings.temp_dir, f"temp_{uuid.uuid4()}{suffix}")
    ensure_directory(settings.temp_dir)
    
    with open(temp_file, 'w') as f:
        f.write(content)
    
    return temp_file


def create_temp_json_file(data: Dict[str, Any]) -> str:
    """Create a temporary JSON file with the given data"""
    content = json.dumps(data, indent=2)
    return create_temp_file(content, '.json')


def cleanup_temp_file(file_path: str) -> None:
    """Safely remove a temporary file"""
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            logger.debug(f"Cleaned up temporary file: {file_path}")
    except Exception as e:
        logger.warning(f"Failed to cleanup temporary file {file_path}: {e}")


def get_output_directory(domain: str) -> str:
    """Get the output directory for a domain"""
    from urllib.parse import urlparse
    domain_clean = urlparse(domain).netloc.replace('.', '_')
    output_dir = os.path.join(settings.reports_dir, f"reports_{domain_clean}")
    ensure_directory(output_dir)
    return output_dir


def save_urls_list(urls: list, output_dir: str, filename: str = 'urls_to_crawl.txt') -> None:
    """Save a list of URLs to a text file"""
    file_path = os.path.join(output_dir, filename)
    with open(file_path, 'w') as f:
        for url in urls:
            f.write(f"{url}\n")
    logger.info(f"Saved {len(urls)} URLs to {file_path}")


def load_config_file(config_path: str) -> Optional[Dict[str, Any]]:
    """Load configuration from a JSON file"""
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        logger.info(f"Loaded configuration from {config_path}")
        return config
    except Exception as e:
        logger.error(f"Failed to load configuration from {config_path}: {e}")
        return None


def save_config_file(config: Dict[str, Any], config_path: str) -> bool:
    """Save configuration to a JSON file"""
    try:
        ensure_directory(os.path.dirname(config_path))
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        logger.info(f"Saved configuration to {config_path}")
        return True
    except Exception as e:
        logger.error(f"Failed to save configuration to {config_path}: {e}")
        return False
