{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}, "https://www.googleapis.com/auth/firebase": {"description": "View and administer all your Firebase data and settings"}}}}, "basePath": "", "baseUrl": "https://firebasehosting.googleapis.com/", "batchPath": "batch", "canonicalName": "Firebase Hosting", "description": "The Firebase Hosting REST API enables programmatic and customizable management and deployments to your Firebase-hosted sites. Use this REST API to create and manage channels and sites as well as to deploy new or updated hosting configurations and content files.", "discoveryVersion": "v1", "documentationLink": "https://firebase.google.com/docs/hosting/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "firebasehosting:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://firebasehosting.mtls.googleapis.com/", "name": "firebasehosting", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "firebasehosting.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^operations/.*$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "request": {"$ref": "CancelOperationRequest"}, "response": {"$ref": "Empty"}}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1/operations/{operationsId}", "httpMethod": "DELETE", "id": "firebasehosting.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^operations/.*$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1/operations", "httpMethod": "GET", "id": "firebasehosting.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^operations$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ListOperationsResponse"}}}}, "projects": {"resources": {"sites": {"resources": {"customDomains": {"resources": {"operations": {"methods": {"cancel": {"description": "CancelOperation is a part of the google.longrunning.Operations interface, but is not implemented for CustomDomain resources.", "flatPath": "v1/projects/{projectsId}/sites/{sitesId}/customDomains/{customDomainsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "firebasehosting.projects.sites.customDomains.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+/customDomains/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "request": {"$ref": "CancelOperationRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "delete": {"description": "DeleteOperation is a part of the google.longrunning.Operations interface, but is not implemented for CustomDomain resources.", "flatPath": "v1/projects/{projectsId}/sites/{sitesId}/customDomains/{customDomainsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "firebasehosting.projects.sites.customDomains.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+/customDomains/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}}}}}}}}}}, "revision": "20241203", "rootUrl": "https://firebasehosting.googleapis.com/", "schemas": {"CancelOperationRequest": {"description": "The request message for Operations.CancelOperation.", "id": "CancelOperationRequest", "properties": {}, "type": "object"}, "CertVerification": {"description": "A set of ACME challenges you can use to allow Hosting to create an SSL certificate for your domain name before directing traffic to Hosting servers. Use either the DNS or HTTP challenge; it's not necessary to provide both.", "id": "CertVerification", "properties": {"dns": {"$ref": "DnsUpdates", "description": "Output only. A `TXT` record to add to your DNS records that confirms your intent to let Hosting create an SSL cert for your domain name.", "readOnly": true}, "http": {"$ref": "HttpUpdate", "description": "Output only. A file to add to your existing, non-Hosting hosting service that confirms your intent to let Hosting create an SSL cert for your domain name.", "readOnly": true}}, "type": "object"}, "CustomDomainMetadata": {"description": "Metadata associated with a`CustomDomain` operation.", "id": "CustomDomainMetadata", "properties": {"certState": {"description": "The `CertState` of the domain name's SSL certificate.", "enum": ["CERT_STATE_UNSPECIFIED", "CERT_PREPARING", "CERT_VALIDATING", "CERT_PROPAGATING", "CERT_ACTIVE", "CERT_EXPIRING_SOON", "CERT_EXPIRED"], "enumDescriptions": ["The certificate's state is unspecified. The message is invalid if this is unspecified.", "The initial state of every certificate, represents Hosting's intent to create a certificate, before requests to a Certificate Authority are made.", "Hosting is validating whether a domain name's DNS records are in a state that allow certificate creation on its behalf.", "The certificate was recently created, and needs time to propagate in Hosting's CDN.", "The certificate is active, providing secure connections for the domain names it represents.", "The certificate is expiring, all domain names on it will be given new certificates.", "The certificate has expired. Hosting can no longer serve secure content on your domain name."], "type": "string"}, "hostState": {"description": "The `HostState` of the domain name this `CustomDomain` refers to.", "enum": ["HOST_STATE_UNSPECIFIED", "HOST_UNHOSTED", "HOST_UNREACHABLE", "HOST_MISMATCH", "HOST_CONFLICT", "HOST_ACTIVE"], "enumDescriptions": ["Your custom domain's host state is unspecified. The message is invalid if this is unspecified.", "Your custom domain's domain name isn't associated with any IP addresses.", "Your custom domain's domain name can't be reached. Hosting services' DNS queries to find your domain name's IP addresses resulted in errors. See your `CustomDomain` object's `issues` field for more details.", "Your custom domain's domain name has IP addresses that don't ultimately resolve to Hosting.", "Your custom domain's domain name has IP addresses that resolve to both Hosting and other services. To ensure consistent results, remove `A` and `AAAA` records related to non-Hosting services.", "All requests against your custom domain's domain name are served by Hosting. If the custom domain's `OwnershipState` is also `ACTIVE`, Hosting serves your Hosting site's content on the domain name."], "type": "string"}, "issues": {"description": "A list of issues that are currently preventing Hosting from completing the operation. These are generally DNS-related issues that Host<PERSON> encounters when querying a domain name's records or attempting to mint an SSL certificate.", "items": {"$ref": "Status"}, "type": "array"}, "liveMigrationSteps": {"description": "A set of DNS record updates and ACME challenges that allow you to transition domain names to Firebase Hosting with zero downtime. These updates allow Hosting to create an SSL certificate and establish ownership for your custom domain before Hosting begins serving traffic on it. If your domain name is already in active use with another provider, add one of the challenges and make the recommended DNS updates. After adding challenges and adjusting DNS records as necessary, wait for the `ownershipState` to be `OWNERSHIP_ACTIVE` and the `certState` to be `CERT_ACTIVE` before sending traffic to Hosting.", "items": {"$ref": "LiveMigrationStep"}, "type": "array"}, "ownershipState": {"description": "The `OwnershipState` of the domain name this `CustomDomain` refers to.", "enum": ["OWNERSHIP_STATE_UNSPECIFIED", "OWNERSHIP_MISSING", "OWNERSHIP_UNREACHABLE", "OWNERSHIP_MISMATCH", "OWNERSHIP_CONFLICT", "OWNERSHIP_PENDING", "OWNERSHIP_ACTIVE"], "enumDescriptions": ["Your custom domain's ownership state is unspecified. This should never happen.", "Your custom domain's domain name has no Hosting-related ownership records; no Firebase project has permission to act on the domain name's behalf.", "Your custom domain's domain name can't be reached. Hosting services' DNS queries to find your domain name's ownership records resulted in errors. See your `CustomDomain` object's `issues` field for more details.", "Your custom domain's domain name is owned by another Firebase project. Remove the conflicting `TXT` records and replace them with project-specific records for your current Firebase project.", "Your custom domain's domain name has conflicting `TXT` records that indicate ownership by both your current Firebase project and another project. Remove the other project's ownership records to grant the current project ownership.", "Your custom domain's DNS records are configured correctly. Hosting will transfer ownership of your domain to this `CustomDomain` within 24 hours.", "Your custom domain's domain name has `TXT` records that grant its project permission to act on its behalf."], "type": "string"}, "quickSetupUpdates": {"$ref": "DnsUpdates", "description": "A set of DNS record updates that allow Hosting to serve secure content on your domain name. The record type determines the update's purpose: - `A` and `AAAA`: Updates your domain name's IP addresses so that they direct traffic to Hosting servers. - `TXT`: Updates ownership permissions on your domain name, letting Hosting know that your custom domain's project has permission to perform actions for that domain name. - `CAA`: Updates your domain name's list of authorized Certificate Authorities (CAs). Only present if you have existing `CAA` records that prohibit Hosting's CA from minting certs for your domain name. These updates include all DNS changes you'll need to get started with Hosting, but, if made all at once, can result in a brief period of downtime for your domain name--while Hosting creates and uploads an SSL cert, for example. If you'd like to add your domain name to Hosting without downtime, complete the `liveMigrationSteps` first, before making the remaining updates in this field."}}, "type": "object"}, "DnsRecord": {"description": "DNS records are resource records that define how systems and services should behave when handling requests for a domain name. For example, when you add `A` records to your domain name's DNS records, you're informing other systems (such as your users' web browsers) to contact those IPv4 addresses to retrieve resources relevant to your domain name (such as your Hosting site files).", "id": "DnsRecord", "properties": {"domainName": {"description": "Output only. The domain name the record pertains to, e.g. `foo.bar.com.`.", "readOnly": true, "type": "string"}, "rdata": {"description": "Output only. The data of the record. The meaning of the value depends on record type: - A and AAAA: IP addresses for the domain name. - CNAME: Another domain to check for records. - TXT: Arbitrary text strings associated with the domain name. Hosting uses TXT records to determine which Firebase projects have permission to act on the domain name's behalf. - CAA: The record's flags, tag, and value, e.g. `0 issue \"pki.goog\"`.", "readOnly": true, "type": "string"}, "requiredAction": {"description": "Output only. An enum that indicates the a required action for this record.", "enum": ["NONE", "ADD", "REMOVE"], "enumDescriptions": ["No action necessary.", "Add this record to your DNS records.", "Remove this record from your DNS records."], "readOnly": true, "type": "string"}, "type": {"description": "Output only. The record's type, which determines what data the record contains.", "enum": ["TYPE_UNSPECIFIED", "A", "CNAME", "TXT", "AAAA", "CAA"], "enumDescriptions": ["The record's type is unspecified. The message is invalid if this is unspecified.", "An `A` record, as defined in [RFC 1035](https://tools.ietf.org/html/rfc1035). A records determine which IPv4 addresses a domain name directs traffic towards.", "A `CNAME` record, as defined in [RFC 1035](https://tools.ietf.org/html/rfc1035). `CNAME` or Canonical Name records map a domain name to a different, canonical domain name. If a `CNAME` record is present, it should be the only record on the domain name.", "A `TXT` record, as defined in [RFC 1035](https://tools.ietf.org/html/rfc1035). `TXT` records hold arbitrary text data on a domain name. Hosting uses `TXT` records to establish which Firebase Project has permission to act on a domain name.", "An AAAA record, as defined in [RFC 3596](https://tools.ietf.org/html/rfc3596) AAAA records determine which IPv6 addresses a domain name directs traffic towards.", "A CAA record, as defined in [RFC 6844](https://tools.ietf.org/html/rfc6844). CAA, or Certificate Authority Authorization, records determine which Certificate Authorities (SSL certificate minting organizations) are authorized to mint a certificate for the domain name. Firebase Hosting uses `pki.goog` as its primary CA. CAA records cascade. A CAA record on `foo.com` also applies to `bar.foo.com` unless `bar.foo.com` has its own set of CAA records. CAA records are optional. If a domain name and its parents have no CAA records, all CAs are authorized to mint certificates on its behalf. In general, Hosting only asks you to modify CAA records when doing so is required to unblock SSL cert creation."], "readOnly": true, "type": "string"}}, "type": "object"}, "DnsRecordSet": {"description": "A set of DNS records relevant to the setup and maintenance of a custom domain in Firebase Hosting.", "id": "DnsRecordSet", "properties": {"checkError": {"$ref": "Status", "description": "Output only. An error Hosting services encountered when querying your domain name's DNS records. Note: Hosting ignores `NXDOMAIN` errors, as those generally just mean that a domain name hasn't been set up yet.", "readOnly": true}, "domainName": {"description": "Output only. The domain name the record set pertains to.", "readOnly": true, "type": "string"}, "records": {"description": "Output only. Records on the domain.", "items": {"$ref": "DnsRecord"}, "readOnly": true, "type": "array"}}, "type": "object"}, "DnsUpdates": {"description": "A set of DNS record updates that you should make to allow Hosting to serve secure content in response to requests against your domain name. These updates present the current state of your domain name's DNS records when Hosting last queried them, and the desired set of records that Hosting needs to see before your custom domain can be fully active.", "id": "DnsUpdates", "properties": {"checkTime": {"description": "The last time Hosting checked your custom domain's DNS records.", "format": "google-datetime", "type": "string"}, "desired": {"description": "The set of DNS records Hosting needs to serve secure content on the domain.", "items": {"$ref": "DnsRecordSet"}, "type": "array"}, "discovered": {"description": "The set of DNS records Hosting discovered when inspecting a domain.", "items": {"$ref": "DnsRecordSet"}, "type": "array"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "HttpUpdate": {"description": "A file you can add to your existing, non-Hosting hosting service that confirms your intent to allow Hosting's Certificate Authorities to create an SSL certificate for your domain.", "id": "HttpUpdate", "properties": {"checkError": {"$ref": "Status", "description": "Output only. An error encountered during the last contents check. If null, the check completed successfully.", "readOnly": true}, "desired": {"description": "Output only. A text string to serve at the path.", "readOnly": true, "type": "string"}, "discovered": {"description": "Output only. Whether Host<PERSON> was able to find the required file contents on the specified path during its last check.", "readOnly": true, "type": "string"}, "lastCheckTime": {"description": "Output only. The last time Hosting systems checked for the file contents.", "format": "google-datetime", "readOnly": true, "type": "string"}, "path": {"description": "Output only. The path to the file.", "readOnly": true, "type": "string"}}, "type": "object"}, "ListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "ListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "Operation"}, "type": "array"}}, "type": "object"}, "LiveMigrationStep": {"description": "A set of updates including ACME challenges and DNS records that allow Hosting to create an SSL certificate and establish project ownership for your domain name before you direct traffic to Hosting servers. Use these updates to facilitate zero downtime migrations to Hosting from other services. After you've made the recommended updates, check your custom domain's `ownershipState` and `certState`. To avoid downtime, they should be `OWNERSHIP_ACTIVE` and `CERT_ACTIVE`, respectively, before you update your `A` and `AAAA` records.", "id": "LiveMigrationStep", "properties": {"certVerification": {"$ref": "CertVerification", "description": "Output only. A pair of ACME challenges that Hosting's Certificate Authority (CA) can use to create an SSL cert for your domain name. Use either the DNS or HTTP challenge; it's not necessary to provide both.", "readOnly": true}, "dnsUpdates": {"$ref": "DnsUpdates", "description": "Output only. DNS updates to facilitate your domain's zero-downtime migration to Hosting.", "readOnly": true}, "issues": {"description": "Output only. Issues that prevent the current step from completing.", "items": {"$ref": "Status"}, "readOnly": true, "type": "array"}, "state": {"description": "Output only. The state of the live migration step, indicates whether you should work to complete the step now, in the future, or have already completed it.", "enum": ["STATE_UNSPECIFIED", "PREPARING", "PENDING", "INCOMPLETE", "PROCESSING", "COMPLETE"], "enumDescriptions": ["The step's state is unspecified. The message is invalid if this is unspecified.", "Hosting doesn't have enough information to construct the step yet. Complete any prior steps and/or resolve this step's issue to proceed.", "The step's state is pending. Complete prior steps before working on a `PENDING` step.", "The step is incomplete. You should complete any `certVerification` or `dnsUpdates` changes to complete it.", "You've done your part to update records and present challenges as necessary. Hosting is now completing background processes to complete the step, e.g. minting an SSL cert for your domain name.", "The step is complete. You've already made the necessary changes to your domain and/or prior hosting service to advance to the next step. Once all steps are complete, Hosting is ready to serve secure content on your domain."], "readOnly": true, "type": "string"}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Firebase Hosting API", "version": "v1", "version_module": true}