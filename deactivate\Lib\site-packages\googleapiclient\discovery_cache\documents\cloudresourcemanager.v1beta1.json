{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}, "https://www.googleapis.com/auth/cloud-platform.read-only": {"description": "View your data across Google Cloud services and see the email address of your Google Account"}}}}, "basePath": "", "baseUrl": "https://cloudresourcemanager.googleapis.com/", "batchPath": "batch", "canonicalName": "Cloud Resource Manager", "description": "Creates, reads, and updates metadata for Google Cloud Platform resource containers.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/resource-manager", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "cloudresourcemanager:v1beta1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://cloudresourcemanager.mtls.googleapis.com/", "name": "cloudresourcemanager", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"organizations": {"methods": {"get": {"description": "Fetches an Organization resource identified by the specified resource name.", "flatPath": "v1beta1/organizations/{organizationsId}", "httpMethod": "GET", "id": "cloudresourcemanager.organizations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the Organization to fetch. This is the organization's relative path in the API, formatted as \"organizations/[organizationId]\". For example, \"organizations/1234\".", "location": "path", "pattern": "^organizations/[^/]+$", "required": true, "type": "string"}, "organizationId": {"deprecated": true, "description": "The id of the Organization resource to fetch. This field is deprecated and will be removed in v1. Use name instead.", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Organization"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "getIamPolicy": {"description": "Gets the access control policy for an Organization resource. May be empty if no such policy or resource exists. The `resource` field should be the organization's resource name, e.g. \"organizations/123\".", "flatPath": "v1beta1/organizations/{organizationsId}:getIamPolicy", "httpMethod": "POST", "id": "cloudresourcemanager.organizations.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^organizations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:getIamPolicy", "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "list": {"description": "Lists Organization resources that are visible to the user and satisfy the specified filter. This method returns Organizations in an unspecified order. New Organizations do not necessarily appear at the end of the list.", "flatPath": "v1beta1/organizations", "httpMethod": "GET", "id": "cloudresourcemanager.organizations.list", "parameterOrder": [], "parameters": {"filter": {"description": "An optional query string used to filter the Organizations to return in the response. Filter rules are case-insensitive. Organizations may be filtered by `owner.directoryCustomerId` or by `domain`, where the domain is a verified G Suite domain, for example: * Filter `owner.directorycustomerid:123456789` returns Organization resources with `owner.directory_customer_id` equal to `123456789`. * Filter `domain:google.com` returns Organization resources corresponding to the domain `google.com`. This field is optional.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of Organizations to return in the response. This field is optional.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A pagination token returned from a previous call to `ListOrganizations` that indicates from where listing should continue. This field is optional.", "location": "query", "type": "string"}}, "path": "v1beta1/organizations", "response": {"$ref": "ListOrganizationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "setIamPolicy": {"description": "Sets the access control policy on an Organization resource. Replaces any existing policy. The `resource` field should be the organization's resource name, e.g. \"organizations/123\".", "flatPath": "v1beta1/organizations/{organizationsId}:setIamPolicy", "httpMethod": "POST", "id": "cloudresourcemanager.organizations.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^organizations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified Organization. The `resource` field should be the organization's resource name, e.g. \"organizations/123\".", "flatPath": "v1beta1/organizations/{organizationsId}:testIamPermissions", "httpMethod": "POST", "id": "cloudresourcemanager.organizations.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^organizations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "update": {"deprecated": true, "description": "Updates an Organization resource identified by the specified resource name.", "flatPath": "v1beta1/organizations/{organizationsId}", "httpMethod": "PUT", "id": "cloudresourcemanager.organizations.update", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The resource name of the organization. This is the organization's relative path in the API. Its format is \"organizations/[organization_id]\". For example, \"organizations/1234\".", "location": "path", "pattern": "^organizations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "Organization"}, "response": {"$ref": "Organization"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "projects": {"methods": {"create": {"description": "Creates a Project resource. Initially, the Project resource is owned by its creator exclusively. The creator can later grant permission to others to read or update the Project. Several APIs are activated automatically for the Project, including Google Cloud Storage. The parent is identified by a specified ResourceId, which must include both an ID and a type, such as project, folder, or organization. This method does not associate the new project with a billing account. You can set or update the billing account associated with a project using the [`projects.updateBillingInfo`] (/billing/reference/rest/v1/projects/updateBillingInfo) method.", "flatPath": "v1beta1/projects", "httpMethod": "POST", "id": "cloudresourcemanager.projects.create", "parameterOrder": [], "parameters": {"useLegacyStack": {"description": "A now unused experiment opt-out option.", "location": "query", "type": "boolean"}}, "path": "v1beta1/projects", "request": {"$ref": "Project"}, "response": {"$ref": "Project"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Marks the Project identified by the specified `project_id` (for example, `my-project-123`) for deletion. This method will only affect the Project if it has a lifecycle state of ACTIVE. This method changes the Project's lifecycle state from ACTIVE to DELETE_REQUESTED. The deletion starts at an unspecified time, at which point the project is no longer accessible. Until the deletion completes, you can check the lifecycle state checked by retrieving the Project with GetProject, and the Project remains visible to ListProjects. However, you cannot update the project. After the deletion completes, the Project is not retrievable by the GetProject and ListProjects methods. The caller must have delete permissions for this Project.", "flatPath": "v1beta1/projects/{projectId}", "httpMethod": "DELETE", "id": "cloudresourcemanager.projects.delete", "parameterOrder": ["projectId"], "parameters": {"projectId": {"description": "The Project ID (for example, `foo-bar-123`).", "location": "path", "required": true, "type": "string"}}, "path": "v1beta1/projects/{projectId}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Retrieves the Project identified by the specified `project_id` (for example, `my-project-123`). The caller must have read permissions for this Project.", "flatPath": "v1beta1/projects/{projectId}", "httpMethod": "GET", "id": "cloudresourcemanager.projects.get", "parameterOrder": ["projectId"], "parameters": {"projectId": {"description": "Required. The Project ID (for example, `my-project-123`).", "location": "path", "required": true, "type": "string"}}, "path": "v1beta1/projects/{projectId}", "response": {"$ref": "Project"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "getAncestry": {"description": "Gets a list of ancestors in the resource hierarchy for the Project identified by the specified `project_id` (for example, `my-project-123`). The caller must have read permissions for this Project.", "flatPath": "v1beta1/projects/{projectId}:getAncestry", "httpMethod": "POST", "id": "cloudresourcemanager.projects.getAncestry", "parameterOrder": ["projectId"], "parameters": {"projectId": {"description": "Required. The Project ID (for example, `my-project-123`).", "location": "path", "required": true, "type": "string"}}, "path": "v1beta1/projects/{projectId}:getAncestry", "request": {"$ref": "GetAncestryRequest"}, "response": {"$ref": "GetAncestryResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "getIamPolicy": {"description": "Returns the IAM access control policy for the specified Project. Permission is denied if the policy or the resource does not exist. For additional information about resource structure and identification, see [Resource Names](/apis/design/resource_names).", "flatPath": "v1beta1/projects/{resource}:getIamPolicy", "httpMethod": "POST", "id": "cloudresourcemanager.projects.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "required": true, "type": "string"}}, "path": "v1beta1/projects/{resource}:getIamPolicy", "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "list": {"description": "Lists Projects that the caller has the `resourcemanager.projects.get` permission on and satisfy the specified filter. This method returns Projects in an unspecified order. This method is eventually consistent with project mutations; this means that a newly created project may not appear in the results or recent updates to an existing project may not be reflected in the results. To retrieve the latest state of a project, use the GetProject method. NOTE: If the request filter contains a `parent.type` and `parent.id` and the caller has the `resourcemanager.projects.list` permission on the parent, the results will be drawn from an alternate index which provides more consistent results. In future versions of this API, this List method will be split into List and Search to properly capture the behavioral difference.", "flatPath": "v1beta1/projects", "httpMethod": "GET", "id": "cloudresourcemanager.projects.list", "parameterOrder": [], "parameters": {"filter": {"description": "An expression for filtering the results of the request. Filter rules are case insensitive. If multiple fields are included in a filter query, the query will return results that match any of the fields. Some eligible fields for filtering are: + `name` + `id` + `labels.` (where *key* is the name of a label) + `parent.type` + `parent.id` Some examples of using labels as filters: | Filter | Description | |------------------|-----------------------------------------------------| | name:how* | The project's name starts with \"how\". | | name:Howl | The project's name is `Howl` or `howl`. | | name:HOWL | Equivalent to above. | | NAME:howl | Equivalent to above. | | labels.color:* | The project has the label `color`. | | labels.color:red | The project's label `color` has the value `red`. | | labels.color:red labels.size:big | The project's label `color` has the value `red` or its label `size` has the value `big`. | If no filter is specified, the call will return projects for which the user has the `resourcemanager.projects.get` permission. NOTE: To perform a by-parent query (eg., what projects are directly in a Folder), the caller must have the `resourcemanager.projects.list` permission on the parent and the filter must contain both a `parent.type` and a `parent.id` restriction (example: \"parent.type:folder parent.id:123\"). In this case an alternate search index is used which provides more consistent results. Optional.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of Projects to return in the response. The server can return fewer Projects than requested. If unspecified, server picks an appropriate default. Optional.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A pagination token returned from a previous call to ListProjects that indicates from where listing should continue. Optional.", "location": "query", "type": "string"}}, "path": "v1beta1/projects", "response": {"$ref": "ListProjectsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "setIamPolicy": {"description": "Sets the IAM access control policy for the specified Project. CAUTION: This method will replace the existing policy, and cannot be used to append additional IAM settings. NOTE: Removing service accounts from policies or changing their roles can render services completely inoperable. It is important to understand how the service account is being used before removing or updating its roles. The following constraints apply when using `setIamPolicy()`: + Project does not support `allUsers` and `allAuthenticatedUsers` as `members` in a `Binding` of a `Policy`. + The owner role can be granted to a `user`, `serviceAccount`, or a group that is part of an organization. For example, <EMAIL> could be added as an owner to a project in the myownpersonaldomain.com organization, but not the examplepetstore.com organization. + Service accounts can be made owners of a project directly without any restrictions. However, to be added as an owner, a user must be invited via Cloud Platform console and must accept the invitation. + A user cannot be granted the owner role using `setIamPolicy()`. The user must be granted the owner role using the Cloud Platform Console and must explicitly accept the invitation. + Invitations to grant the owner role cannot be sent using `setIamPolicy()`; they must be sent only using the Cloud Platform Console. + Membership changes that leave the project without any owners that have accepted the Terms of Service (ToS) will be rejected. + If the project is not part of an organization, there must be at least one owner who has accepted the Terms of Service (ToS) agreement in the policy. Calling `setIamPolicy()` to remove the last ToS-accepted owner from the policy will fail. This restriction also applies to legacy projects that no longer have owners who have accepted the ToS. Edits to IAM policies will be rejected until the lack of a ToS-accepting owner is rectified. Authorization requires the Google IAM permission `resourcemanager.projects.setIamPolicy` on the project", "flatPath": "v1beta1/projects/{resource}:setIamPolicy", "httpMethod": "POST", "id": "cloudresourcemanager.projects.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "required": true, "type": "string"}}, "path": "v1beta1/projects/{resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified Project.", "flatPath": "v1beta1/projects/{resource}:testIamPermissions", "httpMethod": "POST", "id": "cloudresourcemanager.projects.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "required": true, "type": "string"}}, "path": "v1beta1/projects/{resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "undelete": {"description": "Restores the Project identified by the specified `project_id` (for example, `my-project-123`). You can only use this method for a Project that has a lifecycle state of DELETE_REQUESTED. After deletion starts, the Project cannot be restored. The caller must have undelete permissions for this Project.", "flatPath": "v1beta1/projects/{projectId}:undelete", "httpMethod": "POST", "id": "cloudresourcemanager.projects.undelete", "parameterOrder": ["projectId"], "parameters": {"projectId": {"description": "Required. The project ID (for example, `foo-bar-123`).", "location": "path", "required": true, "type": "string"}}, "path": "v1beta1/projects/{projectId}:undelete", "request": {"$ref": "UndeleteProjectRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "update": {"description": "Updates the attributes of the Project identified by the specified `project_id` (for example, `my-project-123`). The caller must have modify permissions for this Project.", "flatPath": "v1beta1/projects/{projectId}", "httpMethod": "PUT", "id": "cloudresourcemanager.projects.update", "parameterOrder": ["projectId"], "parameters": {"projectId": {"description": "The project ID (for example, `my-project-123`).", "location": "path", "required": true, "type": "string"}}, "path": "v1beta1/projects/{projectId}", "request": {"$ref": "Project"}, "response": {"$ref": "Project"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}, "revision": "20250606", "rootUrl": "https://cloudresourcemanager.googleapis.com/", "schemas": {"Ancestor": {"description": "Identifying information for a single ancestor of a project.", "id": "<PERSON><PERSON><PERSON>", "properties": {"resourceId": {"$ref": "ResourceId", "description": "Resource id of the ancestor."}}, "type": "object"}, "AuditConfig": {"description": "Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { \"audit_configs\": [ { \"service\": \"allServices\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" }, { \"log_type\": \"ADMIN_READ\" } ] }, { \"service\": \"sampleservice.googleapis.com\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\" }, { \"log_type\": \"DATA_WRITE\", \"exempted_members\": [ \"user:<EMAIL>\" ] } ] } ] } For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.", "id": "AuditConfig", "properties": {"auditLogConfigs": {"description": "The configuration for logging of each type of permission.", "items": {"$ref": "AuditLogConfig"}, "type": "array"}, "service": {"description": "Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.", "type": "string"}}, "type": "object"}, "AuditLogConfig": {"description": "Provides the configuration for logging a type of permissions. Example: { \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" } ] } This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.", "id": "AuditLogConfig", "properties": {"exemptedMembers": {"description": "Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.", "items": {"type": "string"}, "type": "array"}, "logType": {"description": "The log type that this config enables.", "enum": ["LOG_TYPE_UNSPECIFIED", "ADMIN_READ", "DATA_WRITE", "DATA_READ"], "enumDescriptions": ["Default case. Should never be this.", "Admin reads. Example: CloudIAM getIamPolicy", "Data writes. Example: CloudSQL Users create", "Data reads. Example: CloudSQL Users list"], "type": "string"}}, "type": "object"}, "Binding": {"description": "Associates `members`, or principals, with a `role`.", "id": "Binding", "properties": {"condition": {"$ref": "Expr", "description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies)."}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/*`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/group/{group_id}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/*`: All identities in a workload identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).", "type": "string"}}, "type": "object"}, "CloudresourcemanagerGoogleCloudResourcemanagerV2alpha1FolderOperation": {"description": "Metadata describing a long running folder operation", "id": "CloudresourcemanagerGoogleCloudResourcemanagerV2alpha1FolderOperation", "properties": {"destinationParent": {"description": "The resource name of the folder or organization we are either creating the folder under or moving the folder to.", "type": "string"}, "displayName": {"description": "The display name of the folder.", "type": "string"}, "operationType": {"description": "The type of this operation.", "enum": ["OPERATION_TYPE_UNSPECIFIED", "CREATE", "MOVE"], "enumDescriptions": ["Operation type not specified.", "A create folder operation.", "A move folder operation."], "type": "string"}, "sourceParent": {"description": "The resource name of the folder's parent. Only applicable when the operation_type is MOVE.", "type": "string"}}, "type": "object"}, "CloudresourcemanagerGoogleCloudResourcemanagerV2beta1FolderOperation": {"description": "Metadata describing a long running folder operation", "id": "CloudresourcemanagerGoogleCloudResourcemanagerV2beta1FolderOperation", "properties": {"destinationParent": {"description": "The resource name of the folder or organization we are either creating the folder under or moving the folder to.", "type": "string"}, "displayName": {"description": "The display name of the folder.", "type": "string"}, "operationType": {"description": "The type of this operation.", "enum": ["OPERATION_TYPE_UNSPECIFIED", "CREATE", "MOVE"], "enumDescriptions": ["Operation type not specified.", "A create folder operation.", "A move folder operation."], "type": "string"}, "sourceParent": {"description": "The resource name of the folder's parent. Only applicable when the operation_type is MOVE.", "type": "string"}}, "type": "object"}, "CreateFolderMetadata": {"description": "Metadata pertaining to the Folder creation process.", "id": "CreateFolderMetadata", "properties": {"displayName": {"description": "The display name of the folder.", "type": "string"}, "parent": {"description": "The resource name of the folder or organization we are creating the folder under.", "type": "string"}}, "type": "object"}, "CreateProjectMetadata": {"description": "A status object which is used as the `metadata` field for the Operation returned by CreateProject. It provides insight for when significant phases of Project creation have completed.", "id": "CreateProjectMetadata", "properties": {"createTime": {"description": "Creation time of the project creation workflow.", "format": "google-datetime", "type": "string"}, "gettable": {"description": "True if the project can be retrieved using `GetProject`. No other operations on the project are guaranteed to work until the project creation is complete.", "type": "boolean"}, "ready": {"description": "True if the project creation process is complete.", "type": "boolean"}}, "type": "object"}, "CreateTagBindingMetadata": {"description": "Runtime operation information for creating a TagValue.", "id": "CreateTagBindingMetadata", "properties": {}, "type": "object"}, "CreateTagKeyMetadata": {"description": "Runtime operation information for creating a TagKey.", "id": "CreateTagKeyMetadata", "properties": {}, "type": "object"}, "CreateTagValueMetadata": {"description": "Runtime operation information for creating a TagValue.", "id": "CreateTagValueMetadata", "properties": {}, "type": "object"}, "DeleteFolderMetadata": {"description": "A status object which is used as the `metadata` field for the `Operation` returned by `DeleteFolder`.", "id": "DeleteFolderMetadata", "properties": {}, "type": "object"}, "DeleteOrganizationMetadata": {"description": "A status object which is used as the `metadata` field for the operation returned by DeleteOrganization.", "id": "DeleteOrganizationMetadata", "properties": {}, "type": "object"}, "DeleteProjectMetadata": {"description": "A status object which is used as the `metadata` field for the Operation returned by `DeleteProject`.", "id": "DeleteProjectMetadata", "properties": {}, "type": "object"}, "DeleteTagBindingMetadata": {"description": "Runtime operation information for deleting a TagBinding.", "id": "DeleteTagBindingMetadata", "properties": {}, "type": "object"}, "DeleteTagKeyMetadata": {"description": "Runtime operation information for deleting a TagKey.", "id": "DeleteTagKeyMetadata", "properties": {}, "type": "object"}, "DeleteTagValueMetadata": {"description": "Runtime operation information for deleting a TagValue.", "id": "DeleteTagValueMetadata", "properties": {}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "Expr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "Expr", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}}, "type": "object"}, "FolderOperation": {"description": "Metadata describing a long running folder operation", "id": "FolderOperation", "properties": {"destinationParent": {"description": "The resource name of the folder or organization we are either creating the folder under or moving the folder to.", "type": "string"}, "displayName": {"description": "The display name of the folder.", "type": "string"}, "operationType": {"description": "The type of this operation.", "enum": ["OPERATION_TYPE_UNSPECIFIED", "CREATE", "MOVE"], "enumDescriptions": ["Operation type not specified.", "A create folder operation.", "A move folder operation."], "type": "string"}, "sourceParent": {"description": "The resource name of the folder's parent. Only applicable when the operation_type is MOVE.", "type": "string"}}, "type": "object"}, "FolderOperationError": {"description": "A classification of the Folder Operation error.", "id": "FolderOperationError", "properties": {"errorMessageId": {"description": "The type of operation error experienced.", "enum": ["ERROR_TYPE_UNSPECIFIED", "ACTIVE_FOLDER_HEIGHT_VIOLATION", "MAX_CHILD_FOLDERS_VIOLATION", "FOLDER_NAME_UNIQUENESS_VIOLATION", "RESOURCE_DELETED_VIOLATION", "PARENT_DELETED_VIOLATION", "CYCLE_INTRODUCED_VIOLATION", "FOLDER_BEING_MOVED_VIOLATION", "FOLDER_TO_DELETE_NON_EMPTY_VIOLATION", "DELETED_FOLDER_HEIGHT_VIOLATION"], "enumDescriptions": ["The error type was unrecognized or unspecified.", "The attempted action would violate the max folder depth constraint.", "The attempted action would violate the max child folders constraint.", "The attempted action would violate the locally-unique folder display_name constraint.", "The resource being moved has been deleted.", "The resource a folder was being added to has been deleted.", "The attempted action would introduce cycle in resource path.", "The attempted action would move a folder that is already being moved.", "The folder the caller is trying to delete contains active resources.", "The attempted action would violate the max deleted folder depth constraint."], "type": "string"}}, "type": "object"}, "GetAncestryRequest": {"description": "The request sent to the [google.cloudresourcemanager.projects.v1beta1.DeveloperProjects.GetAncestry] method.", "id": "GetAncestryRequest", "properties": {}, "type": "object"}, "GetAncestryResponse": {"description": "Response from the projects.getAncestry method.", "id": "GetAncestryResponse", "properties": {"ancestor": {"description": "Ancestors are ordered from bottom to top of the resource hierarchy. The first ancestor is the project itself, followed by the project's parent, etc.", "items": {"$ref": "<PERSON><PERSON><PERSON>"}, "type": "array"}}, "type": "object"}, "GetIamPolicyRequest": {"description": "Request message for `GetIamPolicy` method.", "id": "GetIamPolicyRequest", "properties": {"options": {"$ref": "GetPolicyOptions", "description": "OPTIONAL: A `GetPolicyOptions` object for specifying options to `GetIamPolicy`."}}, "type": "object"}, "GetPolicyOptions": {"description": "Encapsulates settings provided to GetIamPolicy.", "id": "GetPolicyOptions", "properties": {"requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "ListOrganizationsResponse": {"description": "The response returned from the `ListOrganizations` method.", "id": "ListOrganizationsResponse", "properties": {"nextPageToken": {"description": "A pagination token to be used to retrieve the next page of results. If the result is too large to fit within the page size specified in the request, this field will be set with a token that can be used to fetch the next page of results. If this field is empty, it indicates that this response contains the last page of results.", "type": "string"}, "organizations": {"description": "The list of Organizations that matched the list query, possibly paginated.", "items": {"$ref": "Organization"}, "type": "array"}}, "type": "object"}, "ListProjectsResponse": {"description": "A page of the response received from the ListProjects method. A paginated response where more pages are available has `next_page_token` set. This token can be used in a subsequent request to retrieve the next request page.", "id": "ListProjectsResponse", "properties": {"nextPageToken": {"description": "Pagination token. If the result set is too large to fit in a single response, this token is returned. It encodes the position of the current result cursor. Feeding this value into a new list request with the `page_token` parameter gives the next page of the results. When `next_page_token` is not filled in, there is no next page and the list returned is the last page in the result set. Pagination tokens have a limited lifetime.", "type": "string"}, "projects": {"description": "The list of Projects that matched the list filter. This list can be paginated.", "items": {"$ref": "Project"}, "type": "array"}}, "type": "object"}, "MoveFolderMetadata": {"description": "Metadata pertaining to the folder move process.", "id": "MoveFolderMetadata", "properties": {"destinationParent": {"description": "The resource name of the folder or organization to move the folder to.", "type": "string"}, "displayName": {"description": "The display name of the folder.", "type": "string"}, "sourceParent": {"description": "The resource name of the folder's parent.", "type": "string"}}, "type": "object"}, "MoveProjectMetadata": {"description": "A status object which is used as the `metadata` field for the Operation returned by MoveProject.", "id": "MoveProjectMetadata", "properties": {}, "type": "object"}, "Organization": {"description": "The root node in the resource hierarchy to which a particular entity's (e.g., company) resources belong.", "id": "Organization", "properties": {"creationTime": {"description": "Timestamp when the Organization was created. Assigned by the server.", "format": "google-datetime", "type": "string"}, "displayName": {"description": "A human-readable string that refers to the Organization in the Google Cloud console. This string is set by the server and cannot be changed. The string will be set to the primary domain (for example, \"google.com\") of the G Suite customer that owns the organization.", "type": "string"}, "lifecycleState": {"description": "The organization's current lifecycle state. Assigned by the server.", "enum": ["LIFECYCLE_STATE_UNSPECIFIED", "ACTIVE", "DELETE_REQUESTED"], "enumDescriptions": ["Unspecified state. This is only useful for distinguishing unset values.", "The normal and active state.", "The organization has been marked for deletion by the user."], "type": "string"}, "name": {"description": "Output only. The resource name of the organization. This is the organization's relative path in the API. Its format is \"organizations/[organization_id]\". For example, \"organizations/1234\".", "type": "string"}, "organizationId": {"deprecated": true, "description": "An immutable id for the Organization that is assigned on creation. This should be omitted when creating a new Organization. This field is read-only.", "type": "string"}, "owner": {"$ref": "OrganizationOwner", "description": "The owner of this Organization. The owner should be specified on creation. Once set, it cannot be changed. This field is required."}}, "type": "object"}, "OrganizationOwner": {"description": "The entity that owns an Organization. The lifetime of the Organization and all of its descendants are bound to the `OrganizationOwner`. If the `OrganizationOwner` is deleted, the Organization and all its descendants will be deleted.", "id": "OrganizationOwner", "properties": {"directoryCustomerId": {"description": "The G Suite customer id used in the Directory API.", "type": "string"}}, "type": "object"}, "Policy": {"description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).", "id": "Policy", "properties": {"auditConfigs": {"description": "Specifies cloud audit logging configuration for this policy.", "items": {"$ref": "AuditConfig"}, "type": "array"}, "bindings": {"description": "Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.", "items": {"$ref": "Binding"}, "type": "array"}, "etag": {"description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.", "format": "byte", "type": "string"}, "version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "Project": {"description": "A Project is a high-level Google Cloud Platform entity. It is a container for ACLs, APIs, App Engine Apps, VMs, and other Google Cloud Platform resources.", "id": "Project", "properties": {"configuredCapabilities": {"description": "Output only. If this project is a Management Project, list of capabilities configured on the parent folder. Note, presence of any capability implies that this is a Management Project. Example: `folders/123/capabilities/app-management`. OUTPUT ONLY.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "createTime": {"description": "Creation time. Read-only.", "format": "google-datetime", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "The labels associated with this Project. Label keys must be between 1 and 63 characters long and must conform to the following regular expression: a-z{0,62}. Label values must be between 0 and 63 characters long and must conform to the regular expression [a-z0-9_-]{0,63}. A label value can be empty. No more than 256 labels can be associated with a given resource. Clients should store labels in a representation such as JSON that does not depend on specific characters being disallowed. Example: `\"environment\" : \"dev\"` Read-write.", "type": "object"}, "lifecycleState": {"description": "The Project lifecycle state. Read-only.", "enum": ["LIFECYCLE_STATE_UNSPECIFIED", "ACTIVE", "DELETE_REQUESTED", "DELETE_IN_PROGRESS"], "enumDescriptions": ["Unspecified state. This is only used/useful for distinguishing unset values.", "The normal and active state.", "The project has been marked for deletion by the user (by invoking DeleteProject) or by the system (Google Cloud Platform). This can generally be reversed by invoking UndeleteProject.", "This lifecycle state is no longer used and is not returned by the API."], "type": "string"}, "name": {"description": "The optional user-assigned display name of the Project. When present it must be between 4 to 30 characters. Allowed characters are: lowercase and uppercase letters, numbers, hyphen, single-quote, double-quote, space, and exclamation point. Example: `My Project` Read-write.", "type": "string"}, "parent": {"$ref": "ResourceId", "description": "An optional reference to a parent Resource. Supported parent types include \"organization\" and \"folder\". Once set, the parent cannot be cleared. The `parent` can be set on creation or using the `UpdateProject` method; the end user must have the `resourcemanager.projects.create` permission on the parent. Read-write."}, "projectId": {"description": "The unique, user-assigned ID of the Project. It must be 6 to 30 lowercase letters, digits, or hyphens. It must start with a letter. Trailing hyphens are prohibited. Example: `tokyo-rain-123` Read-only after creation.", "type": "string"}, "projectNumber": {"description": "The number uniquely identifying the project. Example: `415104041262` Read-only.", "format": "int64", "type": "string"}}, "type": "object"}, "ProjectCreationStatus": {"description": "A status object which is used as the `metadata` field for the Operation returned by CreateProject. It provides insight for when significant phases of Project creation have completed.", "id": "ProjectCreationStatus", "properties": {"createTime": {"description": "Creation time of the project creation workflow.", "format": "google-datetime", "type": "string"}, "gettable": {"description": "True if the project can be retrieved using GetProject. No other operations on the project are guaranteed to work until the project creation is complete.", "type": "boolean"}, "ready": {"description": "True if the project creation process is complete.", "type": "boolean"}}, "type": "object"}, "ResourceId": {"description": "A container to reference an id for any resource type. A `resource` in Google Cloud Platform is a generic term for something you (a developer) may want to interact with through one of our API's. Some examples are an App Engine app, a Compute Engine instance, a Cloud SQL database, and so on.", "id": "ResourceId", "properties": {"id": {"description": "Required field for the type-specific id. This should correspond to the id used in the type-specific API's.", "type": "string"}, "type": {"description": "Required field representing the resource type this id is for. At present, the valid types are \"project\", \"folder\", and \"organization\".", "type": "string"}}, "type": "object"}, "SetIamPolicyRequest": {"description": "Request message for `SetIamPolicy` method.", "id": "SetIamPolicyRequest", "properties": {"policy": {"$ref": "Policy", "description": "REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them."}, "updateMask": {"description": "OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: \"bindings, etag\"`", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "TestIamPermissionsRequest": {"description": "Request message for `TestIamPermissions` method.", "id": "TestIamPermissionsRequest", "properties": {"permissions": {"description": "The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TestIamPermissionsResponse": {"description": "Response message for `TestIamPermissions` method.", "id": "TestIamPermissionsResponse", "properties": {"permissions": {"description": "A subset of `TestPermissionsRequest.permissions` that the caller is allowed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "UndeleteFolderMetadata": {"description": "A status object which is used as the `metadata` field for the `Operation` returned by `UndeleteFolder`.", "id": "UndeleteFolderMetadata", "properties": {}, "type": "object"}, "UndeleteOrganizationMetadata": {"description": "A status object which is used as the `metadata` field for the Operation returned by UndeleteOrganization.", "id": "UndeleteOrganizationMetadata", "properties": {}, "type": "object"}, "UndeleteProjectMetadata": {"description": "A status object which is used as the `metadata` field for the Operation returned by `UndeleteProject`.", "id": "UndeleteProjectMetadata", "properties": {}, "type": "object"}, "UndeleteProjectRequest": {"description": "The request sent to the UndeleteProject method.", "id": "UndeleteProjectRequest", "properties": {}, "type": "object"}, "UpdateFolderMetadata": {"description": "A status object which is used as the `metadata` field for the Operation returned by UpdateFolder.", "id": "UpdateFolderMetadata", "properties": {}, "type": "object"}, "UpdateProjectMetadata": {"description": "A status object which is used as the `metadata` field for the Operation returned by UpdateProject.", "id": "UpdateProjectMetadata", "properties": {}, "type": "object"}, "UpdateTagKeyMetadata": {"description": "Runtime operation information for updating a TagKey.", "id": "UpdateTagKeyMetadata", "properties": {}, "type": "object"}, "UpdateTagValueMetadata": {"description": "Runtime operation information for updating a TagValue.", "id": "UpdateTagValueMetadata", "properties": {}, "type": "object"}}, "servicePath": "", "title": "Cloud Resource Manager API", "version": "v1beta1"}