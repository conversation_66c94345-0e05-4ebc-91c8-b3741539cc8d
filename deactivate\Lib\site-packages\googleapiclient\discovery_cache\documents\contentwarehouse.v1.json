{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://contentwarehouse.googleapis.com/", "batchPath": "batch", "canonicalName": "contentwarehouse", "description": "", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/document-warehouse", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "contentwarehouse:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://contentwarehouse.mtls.googleapis.com/", "name": "contentwarehouse", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"methods": {"fetchAcl": {"description": "Gets the access control policy for a resource. Returns NOT_FOUND error if the resource does not exist. Returns an empty policy if the resource exists but does not have a policy set.", "flatPath": "v1/projects/{projectsId}:fetchAcl", "httpMethod": "POST", "id": "contentwarehouse.projects.fetchAcl", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "Required. REQUIRED: The resource for which the policy is being requested. Format for document: projects/{project_number}/locations/{location}/documents/{document_id}. Format for collection: projects/{project_number}/locations/{location}/collections/{collection_id}. Format for project: projects/{project_number}.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:fetchAcl", "request": {"$ref": "GoogleCloudContentwarehouseV1FetchAclRequest"}, "response": {"$ref": "GoogleCloudContentwarehouseV1FetchAclResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setAcl": {"description": "Sets the access control policy for a resource. Replaces any existing policy.", "flatPath": "v1/projects/{projectsId}:setAcl", "httpMethod": "POST", "id": "contentwarehouse.projects.setAcl", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "Required. REQUIRED: The resource for which the policy is being requested. Format for document: projects/{project_number}/locations/{location}/documents/{document_id}. Format for collection: projects/{project_number}/locations/{location}/collections/{collection_id}. Format for project: projects/{project_number}.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setAcl", "request": {"$ref": "GoogleCloudContentwarehouseV1SetAclRequest"}, "response": {"$ref": "GoogleCloudContentwarehouseV1SetAclResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"locations": {"methods": {"getStatus": {"description": "Get the project status.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}:getStatus", "httpMethod": "GET", "id": "contentwarehouse.projects.locations.getStatus", "parameterOrder": ["location"], "parameters": {"location": {"description": "Required. The location to be queried Format: projects/{project_number}/locations/{location}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+location}:getStatus", "response": {"$ref": "GoogleCloudContentwarehouseV1ProjectStatus"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "initialize": {"description": "Provisions resources for given tenant project. Returns a long running operation.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}:initialize", "httpMethod": "POST", "id": "contentwarehouse.projects.locations.initialize", "parameterOrder": ["location"], "parameters": {"location": {"description": "Required. The location to be initialized Format: projects/{project_number}/locations/{location}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+location}:initialize", "request": {"$ref": "GoogleCloudContentwarehouseV1InitializeProjectRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "runPipeline": {"description": "Run a predefined pipeline.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}:runPipeline", "httpMethod": "POST", "id": "contentwarehouse.projects.locations.runPipeline", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name which owns the resources of the pipeline. Format: projects/{project_number}/locations/{location}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:runPipeline", "request": {"$ref": "GoogleCloudContentwarehouseV1RunPipelineRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"documentSchemas": {"methods": {"create": {"description": "Creates a document schema.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/documentSchemas", "httpMethod": "POST", "id": "contentwarehouse.projects.locations.documentSchemas.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent name.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/documentSchemas", "request": {"$ref": "GoogleCloudContentwarehouseV1DocumentSchema"}, "response": {"$ref": "GoogleCloudContentwarehouseV1DocumentSchema"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a document schema. Returns NOT_FOUND if the document schema does not exist. Returns BAD_REQUEST if the document schema has documents depending on it.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/documentSchemas/{documentSchemasId}", "httpMethod": "DELETE", "id": "contentwarehouse.projects.locations.documentSchemas.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the document schema to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/documentSchemas/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a document schema. Returns NOT_FOUND if the document schema does not exist.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/documentSchemas/{documentSchemasId}", "httpMethod": "GET", "id": "contentwarehouse.projects.locations.documentSchemas.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the document schema to retrieve.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/documentSchemas/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudContentwarehouseV1DocumentSchema"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists document schemas.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/documentSchemas", "httpMethod": "GET", "id": "contentwarehouse.projects.locations.documentSchemas.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of document schemas to return. The service may return fewer than this value. If unspecified, at most 50 document schemas will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListDocumentSchemas` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListDocumentSchemas` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of document schemas. Format: projects/{project_number}/locations/{location}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/documentSchemas", "response": {"$ref": "GoogleCloudContentwarehouseV1ListDocumentSchemasResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a Document Schema. Returns INVALID_ARGUMENT if the name of the Document Schema is non-empty and does not equal the existing name. Supports only appending new properties, adding new ENUM possible values, and updating the EnumTypeOptions.validation_check_disabled flag for ENUM possible values. Updating existing properties will result into INVALID_ARGUMENT.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/documentSchemas/{documentSchemasId}", "httpMethod": "PATCH", "id": "contentwarehouse.projects.locations.documentSchemas.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the document schema to update. Format: projects/{project_number}/locations/{location}/documentSchemas/{document_schema_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/documentSchemas/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleCloudContentwarehouseV1UpdateDocumentSchemaRequest"}, "response": {"$ref": "GoogleCloudContentwarehouseV1DocumentSchema"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "documents": {"methods": {"create": {"description": "Creates a document.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/documents", "httpMethod": "POST", "id": "contentwarehouse.projects.locations.documents.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent name. Format: projects/{project_number}/locations/{location}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/documents", "request": {"$ref": "GoogleCloudContentwarehouseV1CreateDocumentRequest"}, "response": {"$ref": "GoogleCloudContentwarehouseV1CreateDocumentResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a document. Returns NOT_FOUND if the document does not exist.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/documents/{documentsId}:delete", "httpMethod": "POST", "id": "contentwarehouse.projects.locations.documents.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the document to delete. Format: projects/{project_number}/locations/{location}/documents/{document_id} or projects/{project_number}/locations/{location}/documents/referenceId/{reference_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/documents/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:delete", "request": {"$ref": "GoogleCloudContentwarehouseV1DeleteDocumentRequest"}, "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "fetchAcl": {"description": "Gets the access control policy for a resource. Returns NOT_FOUND error if the resource does not exist. Returns an empty policy if the resource exists but does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/documents/{documentsId}:fetchAcl", "httpMethod": "POST", "id": "contentwarehouse.projects.locations.documents.fetchAcl", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "Required. REQUIRED: The resource for which the policy is being requested. Format for document: projects/{project_number}/locations/{location}/documents/{document_id}. Format for collection: projects/{project_number}/locations/{location}/collections/{collection_id}. Format for project: projects/{project_number}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/documents/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:fetchAcl", "request": {"$ref": "GoogleCloudContentwarehouseV1FetchAclRequest"}, "response": {"$ref": "GoogleCloudContentwarehouseV1FetchAclResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a document. Returns NOT_FOUND if the document does not exist.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/documents/{documentsId}:get", "httpMethod": "POST", "id": "contentwarehouse.projects.locations.documents.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the document to retrieve. Format: projects/{project_number}/locations/{location}/documents/{document_id} or projects/{project_number}/locations/{location}/documents/referenceId/{reference_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/documents/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:get", "request": {"$ref": "GoogleCloudContentwarehouseV1GetDocumentRequest"}, "response": {"$ref": "GoogleCloudContentwarehouseV1Document"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "linkedSources": {"description": "Return all source document-links from the document.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/documents/{documentsId}/linkedSources", "httpMethod": "POST", "id": "contentwarehouse.projects.locations.documents.linkedSources", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The name of the document, for which all source links are returned. Format: projects/{project_number}/locations/{location}/documents/{source_document_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/documents/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/linkedSources", "request": {"$ref": "GoogleCloudContentwarehouseV1ListLinkedSourcesRequest"}, "response": {"$ref": "GoogleCloudContentwarehouseV1ListLinkedSourcesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "linkedTargets": {"description": "Return all target document-links from the document.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/documents/{documentsId}/linkedTargets", "httpMethod": "POST", "id": "contentwarehouse.projects.locations.documents.linkedTargets", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The name of the document, for which all target links are returned. Format: projects/{project_number}/locations/{location}/documents/{target_document_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/documents/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/linkedTargets", "request": {"$ref": "GoogleCloudContentwarehouseV1ListLinkedTargetsRequest"}, "response": {"$ref": "GoogleCloudContentwarehouseV1ListLinkedTargetsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "lock": {"description": "Lock the document so the document cannot be updated by other users.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/documents/{documentsId}:lock", "httpMethod": "POST", "id": "contentwarehouse.projects.locations.documents.lock", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the document to lock. Format: projects/{project_number}/locations/{location}/documents/{document}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/documents/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:lock", "request": {"$ref": "GoogleCloudContentwarehouseV1LockDocumentRequest"}, "response": {"$ref": "GoogleCloudContentwarehouseV1Document"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a document. Returns INVALID_ARGUMENT if the name of the document is non-empty and does not equal the existing name.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/documents/{documentsId}", "httpMethod": "PATCH", "id": "contentwarehouse.projects.locations.documents.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the document to update. Format: projects/{project_number}/locations/{location}/documents/{document_id} or projects/{project_number}/locations/{location}/documents/referenceId/{reference_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/documents/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleCloudContentwarehouseV1UpdateDocumentRequest"}, "response": {"$ref": "GoogleCloudContentwarehouseV1UpdateDocumentResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "search": {"description": "Searches for documents using provided SearchDocumentsRequest. This call only returns documents that the caller has permission to search against.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/documents:search", "httpMethod": "POST", "id": "contentwarehouse.projects.locations.documents.search", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent, which owns this collection of documents. Format: projects/{project_number}/locations/{location}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/documents:search", "request": {"$ref": "GoogleCloudContentwarehouseV1SearchDocumentsRequest"}, "response": {"$ref": "GoogleCloudContentwarehouseV1SearchDocumentsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setAcl": {"description": "Sets the access control policy for a resource. Replaces any existing policy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/documents/{documentsId}:setAcl", "httpMethod": "POST", "id": "contentwarehouse.projects.locations.documents.setAcl", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "Required. REQUIRED: The resource for which the policy is being requested. Format for document: projects/{project_number}/locations/{location}/documents/{document_id}. Format for collection: projects/{project_number}/locations/{location}/collections/{collection_id}. Format for project: projects/{project_number}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/documents/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setAcl", "request": {"$ref": "GoogleCloudContentwarehouseV1SetAclRequest"}, "response": {"$ref": "GoogleCloudContentwarehouseV1SetAclResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"documentLinks": {"methods": {"create": {"description": "Create a link between a source document and a target document.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/documents/{documentsId}/documentLinks", "httpMethod": "POST", "id": "contentwarehouse.projects.locations.documents.documentLinks.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Parent of the document-link to be created. parent of document-link should be a document. Format: projects/{project_number}/locations/{location}/documents/{source_document_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/documents/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/documentLinks", "request": {"$ref": "GoogleCloudContentwarehouseV1CreateDocumentLinkRequest"}, "response": {"$ref": "GoogleCloudContentwarehouseV1DocumentLink"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Remove the link between the source and target documents.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/documents/{documentsId}/documentLinks/{documentLinksId}:delete", "httpMethod": "POST", "id": "contentwarehouse.projects.locations.documents.documentLinks.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the document-link to be deleted. Format: projects/{project_number}/locations/{location}/documents/{source_document_id}/documentLinks/{document_link_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/documents/[^/]+/documentLinks/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:delete", "request": {"$ref": "GoogleCloudContentwarehouseV1DeleteDocumentLinkRequest"}, "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "referenceId": {"methods": {"delete": {"description": "Deletes a document. Returns NOT_FOUND if the document does not exist.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/documents/referenceId/{referenceIdId}:delete", "httpMethod": "POST", "id": "contentwarehouse.projects.locations.documents.referenceId.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the document to delete. Format: projects/{project_number}/locations/{location}/documents/{document_id} or projects/{project_number}/locations/{location}/documents/referenceId/{reference_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/documents/referenceId/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:delete", "request": {"$ref": "GoogleCloudContentwarehouseV1DeleteDocumentRequest"}, "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a document. Returns NOT_FOUND if the document does not exist.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/documents/referenceId/{referenceIdId}:get", "httpMethod": "POST", "id": "contentwarehouse.projects.locations.documents.referenceId.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the document to retrieve. Format: projects/{project_number}/locations/{location}/documents/{document_id} or projects/{project_number}/locations/{location}/documents/referenceId/{reference_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/documents/referenceId/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:get", "request": {"$ref": "GoogleCloudContentwarehouseV1GetDocumentRequest"}, "response": {"$ref": "GoogleCloudContentwarehouseV1Document"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a document. Returns INVALID_ARGUMENT if the name of the document is non-empty and does not equal the existing name.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/documents/referenceId/{referenceIdId}", "httpMethod": "PATCH", "id": "contentwarehouse.projects.locations.documents.referenceId.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the document to update. Format: projects/{project_number}/locations/{location}/documents/{document_id} or projects/{project_number}/locations/{location}/documents/referenceId/{reference_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/documents/referenceId/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleCloudContentwarehouseV1UpdateDocumentRequest"}, "response": {"$ref": "GoogleCloudContentwarehouseV1UpdateDocumentResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "contentwarehouse.projects.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "ruleSets": {"methods": {"create": {"description": "Creates a ruleset.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/ruleSets", "httpMethod": "POST", "id": "contentwarehouse.projects.locations.ruleSets.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent name. Format: projects/{project_number}/locations/{location}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/ruleSets", "request": {"$ref": "GoogleCloudContentwarehouseV1RuleSet"}, "response": {"$ref": "GoogleCloudContentwarehouseV1RuleSet"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a ruleset. Returns NOT_FOUND if the document does not exist.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/ruleSets/{ruleSetsId}", "httpMethod": "DELETE", "id": "contentwarehouse.projects.locations.ruleSets.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the rule set to delete. Format: projects/{project_number}/locations/{location}/ruleSets/{rule_set_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/ruleSets/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a ruleset. Returns NOT_FOUND if the ruleset does not exist.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/ruleSets/{ruleSetsId}", "httpMethod": "GET", "id": "contentwarehouse.projects.locations.ruleSets.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the rule set to retrieve. Format: projects/{project_number}/locations/{location}/ruleSets/{rule_set_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/ruleSets/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudContentwarehouseV1RuleSet"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists rulesets.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/ruleSets", "httpMethod": "GET", "id": "contentwarehouse.projects.locations.ruleSets.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of rule sets to return. The service may return fewer than this value. If unspecified, at most 50 rule sets will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListRuleSets` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListRuleSets` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of document. Format: projects/{project_number}/locations/{location}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/ruleSets", "response": {"$ref": "GoogleCloudContentwarehouseV1ListRuleSetsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a ruleset. Returns INVALID_ARGUMENT if the name of the ruleset is non-empty and does not equal the existing name.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/ruleSets/{ruleSetsId}", "httpMethod": "PATCH", "id": "contentwarehouse.projects.locations.ruleSets.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the rule set to update. Format: projects/{project_number}/locations/{location}/ruleSets/{rule_set_id}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/ruleSets/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleCloudContentwarehouseV1UpdateRuleSetRequest"}, "response": {"$ref": "GoogleCloudContentwarehouseV1RuleSet"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "synonymSets": {"methods": {"create": {"description": "Creates a SynonymSet for a single context. Throws an ALREADY_EXISTS exception if a synonymset already exists for the context.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/synonymSets", "httpMethod": "POST", "id": "contentwarehouse.projects.locations.synonymSets.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent name. Format: projects/{project_number}/locations/{location}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/synonymSets", "request": {"$ref": "GoogleCloudContentwarehouseV1SynonymSet"}, "response": {"$ref": "GoogleCloudContentwarehouseV1SynonymSet"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a SynonymSet for a given context. Throws a NOT_FOUND exception if the SynonymSet is not found.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/synonymSets/{synonymSetsId}", "httpMethod": "DELETE", "id": "contentwarehouse.projects.locations.synonymSets.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the synonymSet to delete Format: projects/{project_number}/locations/{location}/synonymSets/{context}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/synonymSets/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a SynonymSet for a particular context. Throws a NOT_FOUND exception if the Synonymset does not exist", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/synonymSets/{synonymSetsId}", "httpMethod": "GET", "id": "contentwarehouse.projects.locations.synonymSets.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the synonymSet to retrieve Format: projects/{project_number}/locations/{location}/synonymSets/{context}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/synonymSets/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudContentwarehouseV1SynonymSet"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Returns all SynonymSets (for all contexts) for the specified location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/synonymSets", "httpMethod": "GET", "id": "contentwarehouse.projects.locations.synonymSets.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of synonymSets to return. The service may return fewer than this value. If unspecified, at most 50 rule sets will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListSynonymSets` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListSynonymSets` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent name. Format: projects/{project_number}/locations/{location}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/synonymSets", "response": {"$ref": "GoogleCloudContentwarehouseV1ListSynonymSetsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Remove the existing SynonymSet for the context and replaces it with a new one. Throws a NOT_FOUND exception if the SynonymSet is not found.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/synonymSets/{synonymSetsId}", "httpMethod": "PATCH", "id": "contentwarehouse.projects.locations.synonymSets.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the synonymSet to update Format: projects/{project_number}/locations/{location}/synonymSets/{context}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/synonymSets/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleCloudContentwarehouseV1SynonymSet"}, "response": {"$ref": "GoogleCloudContentwarehouseV1SynonymSet"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}, "revision": "20241204", "rootUrl": "https://contentwarehouse.googleapis.com/", "schemas": {"CloudAiPlatformTenantresourceCloudSqlInstanceConfig": {"description": "The identity to configure a CloudSQL instance provisioned via SLM Terraform.", "id": "CloudAiPlatformTenantresourceCloudSqlInstanceConfig", "properties": {"cloudSqlInstanceConnectionName": {"description": "Output only. The CloudSQL instance connection name.", "type": "string"}, "cloudSqlInstanceName": {"description": "Input/Output [Optional]. The CloudSQL instance name within SLM instance. If not set, a random UUIC will be generated as instance name.", "type": "string"}, "kmsKeyReference": {"description": "Input [Optional]. The KMS key name or the KMS grant name used for CMEK encryption. Only set this field when provisioning new CloudSQL instances. For existing CloudSQL instances, this field will be ignored because CMEK re-encryption is not supported.", "type": "string"}, "mdbRolesForCorpAccess": {"description": "Input [Optional]. MDB roles for corp access to CloudSQL instance.", "items": {"type": "string"}, "type": "array"}, "slmInstanceName": {"description": "Output only. The SLM instance's full resource name.", "type": "string"}, "slmInstanceTemplate": {"description": "Input [Required]. The SLM instance template to provision CloudSQL.", "type": "string"}, "slmInstanceType": {"description": "Input [Required]. The SLM instance type to provision CloudSQL.", "type": "string"}}, "type": "object"}, "CloudAiPlatformTenantresourceGcsBucketConfig": {"description": "The identity to configure a GCS bucket.", "id": "CloudAiPlatformTenantresourceGcsBucketConfig", "properties": {"admins": {"items": {"type": "string"}, "type": "array"}, "bucketName": {"description": "Input/Output [Optional]. The name of a GCS bucket with max length of 63 chars. If not set, a random UUID will be generated as bucket name.", "type": "string"}, "entityName": {"description": "Input/Output [Optional]. Only needed for per-entity tenant GCP resources. During Deprovision API, the on-demand deletion will only cover the tenant GCP resources with the specified entity name.", "type": "string"}, "kmsKeyReference": {"description": "Input/Output [Optional]. The KMS key name or the KMS grant name used for CMEK encryption. Only set this field when provisioning new GCS bucket. For existing GCS bucket, this field will be ignored because CMEK re-encryption is not supported.", "type": "string"}, "ttlDays": {"description": "Input/Output [Optional]. Only needed when the content in bucket need to be garbage collected within some amount of days.", "format": "int32", "type": "integer"}, "viewers": {"description": "Input/Output [Required]. IAM roles (viewer/admin) put on the bucket.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "CloudAiPlatformTenantresourceIamPolicyBinding": {"description": "The dynamic IAM bindings to be granted after tenant projects are created.", "id": "CloudAiPlatformTenantresourceIamPolicyBinding", "properties": {"members": {"description": "Input/Output [Required]. The member service accounts with the roles above. Note: placeholders are same as the resource above.", "items": {"type": "string"}, "type": "array"}, "resource": {"description": "Input/Output [Required]. The resource name that will be accessed by members, which also depends on resource_type. Note: placeholders are supported in resource names. For example, ${tpn} will be used when the tenant project number is not ready.", "type": "string"}, "resourceType": {"description": "Input/Output [Required]. Specifies the type of resource that will be accessed by members.", "enum": ["RESOURCE_TYPE_UNSPECIFIED", "PROJECT", "SERVICE_ACCOUNT", "GCS_BUCKET", "SERVICE_CONSUMER", "AR_REPO"], "enumDescriptions": ["", "The value of resource field is the ID or number of a project. Format is ", "The value of resource field is the resource name of a service account. Format is projects//serviceAccounts/", "The value of resource field is the name of a GCS bucket (not its resource name). Format is .", "The value of resource field is the resource name of a service consumer. Format is services//consumers/", "The value of the resource field is the AR Image Uri which identifies an AR REPO. Allowed formats are: /// ///: ///@"], "type": "string"}, "role": {"description": "Input/Output [Required]. The role for members below.", "type": "string"}}, "type": "object"}, "CloudAiPlatformTenantresourceInfraSpannerConfig": {"description": "The configuration for a spanner database provisioning. Next ID: 8", "id": "CloudAiPlatformTenantresourceInfraSpannerConfig", "properties": {"createDatabaseOptions": {"$ref": "CloudAiPlatformTenantresourceInfraSpannerConfigCreateDatabaseOptions", "description": "Input [Optional]. The options to create a spanner database. Note: give the right options to ensure the right KMS key access audit logging and AxT logging in expected logging category."}, "kmsKeyReference": {"description": "Input [Optional]. The KMS key name or the KMS grant name used for CMEK encryption. Only set this field when provisioning new Infra Spanner databases. For existing Infra Spanner databases, this field will be ignored because CMEK re-encryption is not supported. For example, projects//locations//keyRings//cryptoKeys/", "type": "string"}, "sdlBundlePath": {"description": "Input [Required]. The file path to the spanner SDL bundle.", "type": "string"}, "spannerBorgServiceAccount": {"description": "Input [Optional]. The spanner borg service account for delegating the kms key to. For example, <EMAIL>, for the nonprod universe.", "type": "string"}, "spannerLocalNamePrefix": {"type": "string"}, "spannerNamespace": {"type": "string"}, "spannerUniverse": {"description": "Input [Required]. Every database in Spanner can be identified by the following path name: /span//:", "type": "string"}}, "type": "object"}, "CloudAiPlatformTenantresourceInfraSpannerConfigCreateDatabaseOptions": {"description": "The options to create a spanner database. KMS key access audit logging and AxT logging will be associated with the given resource name, resource type and service name. Please ensure to give right options to enable correct audit logging and AxT logging.", "id": "CloudAiPlatformTenantresourceInfraSpannerConfigCreateDatabaseOptions", "properties": {"cmekCloudResourceName": {"description": "The cloud resource name for the CMEK encryption. For example, projects//locations/", "type": "string"}, "cmekCloudResourceType": {"description": "The cloud resource type for the CMEK encryption. For example, contentwarehouse.googleapis.com/Location", "type": "string"}, "cmekServiceName": {"description": "The service name for the CMEK encryption. For example, contentwarehouse.googleapis.com", "type": "string"}}, "type": "object"}, "CloudAiPlatformTenantresourceServiceAccountIdentity": {"description": "The identity to configure a service account.", "id": "CloudAiPlatformTenantresourceServiceAccountIdentity", "properties": {"serviceAccountEmail": {"description": "Output only. The service account email that has been created.", "type": "string"}, "tag": {"description": "Input/Output [Optional]. The tag that configures the service account, as defined in google3/configs/production/cdpush/acl-zanzibar-cloud-prod/activation_grants/activation_grants.gcl. Note: The default P4 service account has the empty tag.", "type": "string"}}, "type": "object"}, "CloudAiPlatformTenantresourceTenantProjectConfig": {"description": "The identity to configure a tenant project.", "id": "CloudAiPlatformTenantresourceTenantProjectConfig", "properties": {"billingConfig": {"$ref": "GoogleApiServiceconsumermanagementV1BillingConfig", "description": "Input/Output [Required]. The billing account properties to create the tenant project."}, "folder": {"description": "Input/Output [Required]. The folder that holds tenant projects and folder-level permissions will be automatically granted to all tenant projects under the folder. Note: the valid folder format is `folders/{folder_number}`.", "type": "string"}, "policyBindings": {"description": "Input/Output [Required]. The policy bindings that are applied to the tenant project during creation. At least one binding must have the role `roles/owner` with either `user` or `group` type.", "items": {"$ref": "GoogleApiServiceconsumermanagementV1PolicyBinding"}, "type": "array"}, "services": {"description": "Input/Output [Required]. The API services that are enabled on the tenant project during creation.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "CloudAiPlatformTenantresourceTenantProjectResource": {"description": "The tenant project and tenant resources. Next ID: 10", "id": "CloudAiPlatformTenantresourceTenantProjectResource", "properties": {"cloudSqlInstances": {"description": "The CloudSQL instances that are provisioned under the tenant project.", "items": {"$ref": "CloudAiPlatformTenantresourceCloudSqlInstanceConfig"}, "type": "array"}, "gcsBuckets": {"description": "The GCS buckets that are provisioned under the tenant project.", "items": {"$ref": "CloudAiPlatformTenantresourceGcsBucketConfig"}, "type": "array"}, "iamPolicyBindings": {"description": "The dynamic IAM bindings that are granted under the tenant project. Note: this should only add new bindings to the project if they don't exist and the existing bindings won't be affected.", "items": {"$ref": "CloudAiPlatformTenantresourceIamPolicyBinding"}, "type": "array"}, "infraSpannerConfigs": {"description": "The Infra Spanner databases that are provisioned under the tenant project. Note: this is an experimental feature.", "items": {"$ref": "CloudAiPlatformTenantresourceInfraSpannerConfig"}, "type": "array"}, "tag": {"description": "Input/Output [Required]. The tag that uniquely identifies a tenant project within a tenancy unit. Note: for the same tenant project tag, all tenant manager operations should be idempotent.", "type": "string"}, "tenantProjectConfig": {"$ref": "CloudAiPlatformTenantresourceTenantProjectConfig", "description": "The configurations of a tenant project."}, "tenantProjectId": {"description": "Output only. The tenant project ID that has been created.", "type": "string"}, "tenantProjectNumber": {"description": "Output only. The tenant project number that has been created.", "format": "int64", "type": "string"}, "tenantServiceAccounts": {"description": "The service account identities (or enabled API service's P4SA) that are expclicitly created under the tenant project (before JIT provisioning during enabled API services).", "items": {"$ref": "CloudAiPlatformTenantresourceTenantServiceAccountIdentity"}, "type": "array"}}, "type": "object"}, "CloudAiPlatformTenantresourceTenantResource": {"description": "A collection of tenant resources.", "id": "CloudAiPlatformTenantresourceTenantResource", "properties": {"p4ServiceAccounts": {"description": "A list of P4 service accounts (go/p4sa) to provision or deprovision.", "items": {"$ref": "CloudAiPlatformTenantresourceServiceAccountIdentity"}, "type": "array"}, "tenantProjectResources": {"description": "A list of tenant projects and tenant resources to provision or deprovision.", "items": {"$ref": "CloudAiPlatformTenantresourceTenantProjectResource"}, "type": "array"}}, "type": "object"}, "CloudAiPlatformTenantresourceTenantServiceAccountIdentity": {"description": "The identity of service accounts that have been explicitly created under tenant projects.", "id": "CloudAiPlatformTenantresourceTenantServiceAccountIdentity", "properties": {"serviceAccountEmail": {"description": "Output only. The email address of the generated service account.", "type": "string"}, "serviceName": {"description": "Input/Output [Required]. The service that the service account belongs to. (e.g. cloudbuild.googleapis.com for GCB service accounts)", "type": "string"}}, "type": "object"}, "GoogleApiServiceconsumermanagementV1BillingConfig": {"description": "Describes the billing configuration for a new tenant project.", "id": "GoogleApiServiceconsumermanagementV1BillingConfig", "properties": {"billingAccount": {"description": "Name of the billing account. For example `billingAccounts/012345-567890-ABCDEF`.", "type": "string"}}, "type": "object"}, "GoogleApiServiceconsumermanagementV1PolicyBinding": {"description": "Translates to IAM Policy bindings (without auditing at this level)", "id": "GoogleApiServiceconsumermanagementV1PolicyBinding", "properties": {"members": {"description": "Uses the same format as in IAM policy. `member` must include both a prefix and ID. For example, `user:{emailId}`, `serviceAccount:{emailId}`, `group:{emailId}`.", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Role. (https://cloud.google.com/iam/docs/understanding-roles) For example, `roles/viewer`, `roles/editor`, or `roles/owner`.", "type": "string"}}, "type": "object"}, "GoogleCloudContentwarehouseV1AccessControlAction": {"description": "Represents the action responsible for access control list management operations.", "id": "GoogleCloudContentwarehouseV1AccessControlAction", "properties": {"operationType": {"description": "Identifies the type of operation.", "enum": ["UNKNOWN", "ADD_POLICY_BINDING", "REMOVE_POLICY_BINDING", "REPLACE_POLICY_BINDING"], "enumDescriptions": ["The unknown operation type.", "Adds newly given policy bindings in the existing bindings list.", "Removes newly given policy bindings from the existing bindings list.", "Replaces existing policy bindings with the given policy binding list"], "type": "string"}, "policy": {"$ref": "GoogleIamV1Policy", "description": "Represents the new policy from which bindings are added, removed or replaced based on the type of the operation. the policy is limited to a few 10s of KB."}}, "type": "object"}, "GoogleCloudContentwarehouseV1Action": {"description": "Represents the action triggered by Rule Engine when the rule is true.", "id": "GoogleCloudContentwarehouseV1Action", "properties": {"accessControl": {"$ref": "GoogleCloudContentwarehouseV1AccessControlAction", "description": "Action triggering access control operations."}, "actionId": {"description": "ID of the action. Managed internally.", "type": "string"}, "addToFolder": {"$ref": "GoogleCloudContentwarehouseV1AddToFolderAction", "description": "Action triggering create document link operation."}, "dataUpdate": {"$ref": "GoogleCloudContentwarehouseV1DataUpdateAction", "description": "Action triggering data update operations."}, "dataValidation": {"$ref": "GoogleCloudContentwarehouseV1DataValidationAction", "description": "Action triggering data validation operations."}, "deleteDocumentAction": {"$ref": "GoogleCloudContentwarehouseV1DeleteDocumentAction", "description": "Action deleting the document."}, "publishToPubSub": {"$ref": "GoogleCloudContentwarehouseV1PublishAction", "description": "Action publish to Pub/Sub operation."}, "removeFromFolderAction": {"$ref": "GoogleCloudContentwarehouseV1RemoveFromFolderAction", "description": "Action removing a document from a folder."}}, "type": "object"}, "GoogleCloudContentwarehouseV1ActionExecutorOutput": {"description": "Represents the output of the Action Executor.", "id": "GoogleCloudContentwarehouseV1ActionExecutorOutput", "properties": {"ruleActionsPairs": {"description": "List of rule and corresponding actions result.", "items": {"$ref": "GoogleCloudContentwarehouseV1RuleActionsPair"}, "type": "array"}}, "type": "object"}, "GoogleCloudContentwarehouseV1ActionOutput": {"description": "Represents the result of executing an action.", "id": "GoogleCloudContentwarehouseV1ActionOutput", "properties": {"actionId": {"description": "ID of the action.", "type": "string"}, "actionState": {"description": "State of an action.", "enum": ["UNKNOWN", "ACTION_SUCCEEDED", "ACTION_FAILED", "ACTION_TIMED_OUT", "ACTION_PENDING"], "enumDescriptions": ["The unknown state.", "State indicating action executed successfully.", "State indicating action failed.", "State indicating action timed out.", "State indicating action is pending."], "type": "string"}, "outputMessage": {"description": "Action execution output message.", "type": "string"}}, "type": "object"}, "GoogleCloudContentwarehouseV1AddToFolderAction": {"description": "Represents the action responsible for adding document under a folder.", "id": "GoogleCloudContentwarehouseV1AddToFolderAction", "properties": {"folders": {"description": "Names of the folder under which new document is to be added. Format: projects/{project_number}/locations/{location}/documents/{document_id}.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudContentwarehouseV1CloudAIDocumentOption": {"description": "Request Option for processing Cloud AI Document in CW Document.", "id": "GoogleCloudContentwarehouseV1CloudAIDocumentOption", "properties": {"customizedEntitiesPropertiesConversions": {"additionalProperties": {"type": "string"}, "description": "If set, only selected entities will be converted to properties.", "type": "object"}, "enableEntitiesConversions": {"description": "Whether to convert all the entities to properties.", "type": "boolean"}}, "type": "object"}, "GoogleCloudContentwarehouseV1CreateDocumentLinkRequest": {"description": "Request message for DocumentLinkService.CreateDocumentLink.", "id": "GoogleCloudContentwarehouseV1CreateDocumentLinkRequest", "properties": {"documentLink": {"$ref": "GoogleCloudContentwarehouseV1DocumentLink", "description": "Required. Document links associated with the source documents (source_document_id)."}, "requestMetadata": {"$ref": "GoogleCloudContentwarehouseV1RequestMetadata", "description": "The meta information collected about the document creator, used to enforce access control for the service."}}, "type": "object"}, "GoogleCloudContentwarehouseV1CreateDocumentMetadata": {"description": "Metadata object for CreateDocument request (currently empty).", "id": "GoogleCloudContentwarehouseV1CreateDocumentMetadata", "properties": {}, "type": "object"}, "GoogleCloudContentwarehouseV1CreateDocumentRequest": {"description": "Request message for DocumentService.CreateDocument.", "id": "GoogleCloudContentwarehouseV1CreateDocumentRequest", "properties": {"cloudAiDocumentOption": {"$ref": "GoogleCloudContentwarehouseV1CloudAIDocumentOption", "description": "Request Option for processing Cloud AI Document in Document Warehouse. This field offers limited support for mapping entities from Cloud AI Document to Warehouse Document. Please consult with product team before using this field and other available options."}, "createMask": {"description": "Field mask for creating Document fields. If mask path is empty, it means all fields are masked. For the `FieldMask` definition, see https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask.", "format": "google-fieldmask", "type": "string"}, "document": {"$ref": "GoogleCloudContentwarehouseV1Document", "description": "Required. The document to create."}, "policy": {"$ref": "GoogleIamV1Policy", "description": "Default document policy during creation. This refers to an Identity and Access (IAM) policy, which specifies access controls for the Document. Conditions defined in the policy will be ignored."}, "requestMetadata": {"$ref": "GoogleCloudContentwarehouseV1RequestMetadata", "description": "The meta information collected about the end user, used to enforce access control for the service."}}, "type": "object"}, "GoogleCloudContentwarehouseV1CreateDocumentResponse": {"description": "Response message for DocumentService.CreateDocument.", "id": "GoogleCloudContentwarehouseV1CreateDocumentResponse", "properties": {"document": {"$ref": "GoogleCloudContentwarehouseV1Document", "description": "Document created after executing create request."}, "longRunningOperations": {"description": "post-processing LROs", "items": {"$ref": "GoogleLongrunningOperation"}, "type": "array"}, "metadata": {"$ref": "GoogleCloudContentwarehouseV1ResponseMetadata", "description": "Additional information for the API invocation, such as the request tracking id."}, "ruleEngineOutput": {"$ref": "GoogleCloudContentwarehouseV1RuleEngineOutput", "description": "Output from Rule Engine recording the rule evaluator and action executor's output. Refer format in: google/cloud/contentwarehouse/v1/rule_engine.proto"}}, "type": "object"}, "GoogleCloudContentwarehouseV1CustomWeightsMetadata": {"description": "To support the custom weighting across document schemas.", "id": "GoogleCloudContentwarehouseV1CustomWeightsMetadata", "properties": {"weightedSchemaProperties": {"description": "List of schema and property name. Allows a maximum of 10 schemas to be specified for relevance boosting.", "items": {"$ref": "GoogleCloudContentwarehouseV1WeightedSchemaProperty"}, "type": "array"}}, "type": "object"}, "GoogleCloudContentwarehouseV1DataUpdateAction": {"description": "Represents the action responsible for properties update operations.", "id": "GoogleCloudContentwarehouseV1DataUpdateAction", "properties": {"entries": {"additionalProperties": {"type": "string"}, "description": "Map of (K, V) -> (valid name of the field, new value of the field) E.g., (\"age\", \"60\") entry triggers update of field age with a value of 60. If the field is not present then new entry is added. During update action execution, value strings will be casted to appropriate types.", "type": "object"}}, "type": "object"}, "GoogleCloudContentwarehouseV1DataValidationAction": {"description": "Represents the action responsible for data validation operations.", "id": "GoogleCloudContentwarehouseV1DataValidationAction", "properties": {"conditions": {"additionalProperties": {"type": "string"}, "description": "Map of (K, V) -> (field, string condition to be evaluated on the field) E.g., (\"age\", \"age > 18 && age < 60\") entry triggers validation of field age with the given condition. Map entries will be ANDed during validation.", "type": "object"}}, "type": "object"}, "GoogleCloudContentwarehouseV1DateTimeArray": {"description": "DateTime values.", "id": "GoogleCloudContentwarehouseV1DateTimeArray", "properties": {"values": {"description": "List of datetime values. Both OffsetDateTime and ZonedDateTime are supported.", "items": {"$ref": "GoogleTypeDateTime"}, "type": "array"}}, "type": "object"}, "GoogleCloudContentwarehouseV1DateTimeTypeOptions": {"description": "Configurations for a date time property.", "id": "GoogleCloudContentwarehouseV1DateTimeTypeOptions", "properties": {}, "type": "object"}, "GoogleCloudContentwarehouseV1DeleteDocumentAction": {"description": "Represents the action responsible for deleting the document.", "id": "GoogleCloudContentwarehouseV1DeleteDocumentAction", "properties": {"enableHardDelete": {"description": "Boolean field to select between hard vs soft delete options. Set 'true' for 'hard delete' and 'false' for 'soft delete'.", "type": "boolean"}}, "type": "object"}, "GoogleCloudContentwarehouseV1DeleteDocumentLinkRequest": {"description": "Request message for DocumentLinkService.DeleteDocumentLink.", "id": "GoogleCloudContentwarehouseV1DeleteDocumentLinkRequest", "properties": {"requestMetadata": {"$ref": "GoogleCloudContentwarehouseV1RequestMetadata", "description": "The meta information collected about the document creator, used to enforce access control for the service."}}, "type": "object"}, "GoogleCloudContentwarehouseV1DeleteDocumentRequest": {"description": "Request message for DocumentService.DeleteDocument.", "id": "GoogleCloudContentwarehouseV1DeleteDocumentRequest", "properties": {"requestMetadata": {"$ref": "GoogleCloudContentwarehouseV1RequestMetadata", "description": "The meta information collected about the end user, used to enforce access control for the service."}}, "type": "object"}, "GoogleCloudContentwarehouseV1Document": {"description": "Defines the structure for content warehouse document proto.", "id": "GoogleCloudContentwarehouseV1Document", "properties": {"cloudAiDocument": {"$ref": "GoogleCloudDocumentaiV1Document", "description": "Document AI format to save the structured content, including OCR."}, "contentCategory": {"description": "Indicates the category (image, audio, video etc.) of the original content.", "enum": ["CONTENT_CATEGORY_UNSPECIFIED", "CONTENT_CATEGORY_IMAGE", "CONTENT_CATEGORY_AUDIO", "CONTENT_CATEGORY_VIDEO"], "enumDescriptions": ["No category is specified.", "Content is of image type.", "Content is of audio type.", "Content is of video type."], "type": "string"}, "createTime": {"description": "Output only. The time when the document is created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "creator": {"description": "The user who creates the document.", "type": "string"}, "displayName": {"description": "Required. Display name of the document given by the user. This name will be displayed in the UI. Customer can populate this field with the name of the document. This differs from the 'title' field as 'title' is optional and stores the top heading in the document.", "type": "string"}, "displayUri": {"description": "Uri to display the document, for example, in the UI.", "type": "string"}, "dispositionTime": {"description": "Output only. If linked to a Collection with RetentionPolicy, the date when the document becomes mutable.", "format": "google-datetime", "readOnly": true, "type": "string"}, "documentSchemaName": {"description": "The Document schema name. Format: projects/{project_number}/locations/{location}/documentSchemas/{document_schema_id}.", "type": "string"}, "inlineRawDocument": {"description": "Raw document content.", "format": "byte", "type": "string"}, "legalHold": {"description": "Output only. Indicates if the document has a legal hold on it.", "readOnly": true, "type": "boolean"}, "name": {"description": "The resource name of the document. Format: projects/{project_number}/locations/{location}/documents/{document_id}. The name is ignored when creating a document.", "type": "string"}, "plainText": {"description": "Other document format, such as PPTX, XLXS", "type": "string"}, "properties": {"description": "List of values that are user supplied metadata.", "items": {"$ref": "GoogleCloudContentwarehouseV1Property"}, "type": "array"}, "rawDocumentFileType": {"description": "This is used when DocAI was not used to load the document and parsing/ extracting is needed for the inline_raw_document. For example, if inline_raw_document is the byte representation of a PDF file, then this should be set to: RAW_DOCUMENT_FILE_TYPE_PDF.", "enum": ["RAW_DOCUMENT_FILE_TYPE_UNSPECIFIED", "RAW_DOCUMENT_FILE_TYPE_PDF", "RAW_DOCUMENT_FILE_TYPE_DOCX", "RAW_DOCUMENT_FILE_TYPE_XLSX", "RAW_DOCUMENT_FILE_TYPE_PPTX", "RAW_DOCUMENT_FILE_TYPE_TEXT", "RAW_DOCUMENT_FILE_TYPE_TIFF"], "enumDescriptions": ["No raw document specified or it is non-parsable", "Adobe PDF format", "Microsoft Word format", "Microsoft Excel format", "Microsoft Powerpoint format", "UTF-8 encoded text format", "TIFF or TIF image file format"], "type": "string"}, "rawDocumentPath": {"description": "Raw document file in Cloud Storage path.", "type": "string"}, "referenceId": {"description": "The reference ID set by customers. Must be unique per project and location.", "type": "string"}, "textExtractionDisabled": {"deprecated": true, "description": "If true, text extraction will not be performed.", "type": "boolean"}, "textExtractionEnabled": {"description": "If true, text extraction will be performed.", "type": "boolean"}, "title": {"description": "Title that describes the document. This can be the top heading or text that describes the document.", "type": "string"}, "updateTime": {"description": "Output only. The time when the document is last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}, "updater": {"description": "The user who lastly updates the document.", "type": "string"}}, "type": "object"}, "GoogleCloudContentwarehouseV1DocumentLink": {"description": "A document-link between source and target document.", "id": "GoogleCloudContentwarehouseV1DocumentLink", "properties": {"createTime": {"description": "Output only. The time when the documentLink is created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Description of this document-link.", "type": "string"}, "name": {"description": "Name of this document-link. It is required that the parent derived form the name to be consistent with the source document reference. Otherwise an exception will be thrown. Format: projects/{project_number}/locations/{location}/documents/{source_document_id}/documentLinks/{document_link_id}.", "type": "string"}, "sourceDocumentReference": {"$ref": "GoogleCloudContentwarehouseV1DocumentReference", "description": "Document references of the source document."}, "state": {"description": "The state of the documentlink. If target node has been deleted, the link is marked as invalid. Removing a source node will result in removal of all associated links.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "SOFT_DELETED"], "enumDescriptions": ["Unknown state of documentlink.", "The documentlink has both source and target documents detected.", "Target document is deleted, and mark the documentlink as soft-deleted."], "type": "string"}, "targetDocumentReference": {"$ref": "GoogleCloudContentwarehouseV1DocumentReference", "description": "Document references of the target document."}, "updateTime": {"description": "Output only. The time when the documentLink is last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudContentwarehouseV1DocumentQuery": {"id": "GoogleCloudContentwarehouseV1DocumentQuery", "properties": {"customPropertyFilter": {"deprecated": true, "description": "This filter specifies a structured syntax to match against the [PropertyDefinition].is_filterable marked as `true`. The syntax for this expression is a subset of SQL syntax. Supported operators are: `=`, `!=`, `<`, `<=`, `>`, and `>=` where the left of the operator is a property name and the right of the operator is a number or a quoted string. You must escape backslash (\\\\) and quote (\\\") characters. Supported functions are `LOWER([property_name])` to perform a case insensitive match and `EMPTY([property_name])` to filter on the existence of a key. Boolean expressions (AND/OR/NOT) are supported up to 3 levels of nesting (for example, \"((A AND B AND C) OR NOT D) AND E\"), a maximum of 100 comparisons or functions are allowed in the expression. The expression must be < 6000 bytes in length. Sample Query: `(LOWER(driving_license)=\"class \\\"a\\\"\" OR EMPTY(driving_license)) AND driving_years > 10`", "type": "string"}, "customWeightsMetadata": {"$ref": "GoogleCloudContentwarehouseV1CustomWeightsMetadata", "description": "To support the custom weighting across document schemas, customers need to provide the properties to be used to boost the ranking in the search request. For a search query with CustomWeightsMetadata specified, only the RetrievalImportance for the properties in the CustomWeightsMetadata will be honored."}, "documentCreatorFilter": {"description": "The exact creator(s) of the documents to search against. If a value isn't specified, documents within the search results are associated with any creator. If multiple values are specified, documents within the search results may be associated with any of the specified creators.", "items": {"type": "string"}, "type": "array"}, "documentNameFilter": {"description": "Search the documents in the list. Format: projects/{project_number}/locations/{location}/documents/{document_id}.", "items": {"type": "string"}, "type": "array"}, "documentSchemaNames": {"description": "This filter specifies the exact document schema Document.document_schema_name of the documents to search against. If a value isn't specified, documents within the search results are associated with any schema. If multiple values are specified, documents within the search results may be associated with any of the specified schemas. At most 20 document schema names are allowed.", "items": {"type": "string"}, "type": "array"}, "fileTypeFilter": {"$ref": "GoogleCloudContentwarehouseV1FileTypeFilter", "description": "This filter specifies the types of files to return: ALL, FOLDER, or FILE. If FOLDER or FILE is specified, then only either folders or files will be returned, respectively. If ALL is specified, both folders and files will be returned. If no value is specified, ALL files will be returned."}, "folderNameFilter": {"description": "Search all the documents under this specified folder. Format: projects/{project_number}/locations/{location}/documents/{document_id}.", "type": "string"}, "isNlQuery": {"description": "Experimental, do not use. If the query is a natural language question. False by default. If true, then the question-answering feature will be used instead of search, and `result_count` in SearchDocumentsRequest must be set. In addition, all other input fields related to search (pagination, histograms, etc.) will be ignored.", "type": "boolean"}, "propertyFilter": {"description": "This filter specifies a structured syntax to match against the PropertyDefinition.is_filterable marked as `true`. The relationship between the PropertyFilters is OR.", "items": {"$ref": "GoogleCloudContentwarehouseV1PropertyFilter"}, "type": "array"}, "query": {"description": "The query string that matches against the full text of the document and the searchable properties. The query partially supports [Google AIP style syntax](https://google.aip.dev/160). Specifically, the query supports literals, logical operators, negation operators, comparison operators, and functions. Literals: A bare literal value (examples: \"42\", \"<PERSON>\") is a value to be matched against. It searches over the full text of the document and the searchable properties. Logical operators: \"AND\", \"and\", \"OR\", and \"or\" are binary logical operators (example: \"engineer OR developer\"). Negation operators: \"NOT\" and \"!\" are negation operators (example: \"NOT software\"). Comparison operators: support the binary comparison operators =, !=, <, >, <= and >= for string, numeric, enum, boolean. Also support like operator `~~` for string. It provides semantic search functionality by parsing, stemming and doing synonyms expansion against the input query. To specify a property in the query, the left hand side expression in the comparison must be the property ID including the parent. The right hand side must be literals. For example: \"\\\"projects/123/locations/us\\\".property_a < 1\" matches results whose \"property_a\" is less than 1 in project 123 and us location. The literals and comparison expression can be connected in a single query (example: \"software engineer \\\"projects/123/locations/us\\\".salary > 100\"). Functions: supported functions are `LOWER([property_name])` to perform a case insensitive match and `EMPTY([property_name])` to filter on the existence of a key. Support nested expressions connected using parenthesis and logical operators. The default logical operators is `AND` if there is no operators between expressions. The query can be used with other filters e.g. `time_filters` and `folder_name_filter`. They are connected with `AND` operator under the hood. The maximum number of allowed characters is 255.", "type": "string"}, "queryContext": {"description": "For custom synonyms. Customers provide the synonyms based on context. One customer can provide multiple set of synonyms based on different context. The search query will be expanded based on the custom synonyms of the query context set. By default, no custom synonyms wll be applied if no query context is provided. It is not supported for CMEK compliant deployment.", "items": {"type": "string"}, "type": "array"}, "timeFilters": {"description": "Documents created/updated within a range specified by this filter are searched against.", "items": {"$ref": "GoogleCloudContentwarehouseV1TimeFilter"}, "type": "array"}}, "type": "object"}, "GoogleCloudContentwarehouseV1DocumentReference": {"description": "References to the documents.", "id": "GoogleCloudContentwarehouseV1DocumentReference", "properties": {"createTime": {"description": "Output only. The time when the document is created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "deleteTime": {"description": "Output only. The time when the document is deleted.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "display_name of the referenced document; this name does not need to be consistent to the display_name in the Document proto, depending on the ACL constraint.", "type": "string"}, "documentIsFolder": {"description": "The document type of the document being referenced.", "type": "boolean"}, "documentIsLegalHoldFolder": {"description": "Document is a folder with legal hold.", "type": "boolean"}, "documentIsRetentionFolder": {"description": "Document is a folder with retention policy.", "type": "boolean"}, "documentName": {"description": "Required. Name of the referenced document.", "type": "string"}, "snippet": {"description": "Stores the subset of the referenced document's content. This is useful to allow user peek the information of the referenced document.", "type": "string"}, "updateTime": {"description": "Output only. The time when the document is last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudContentwarehouseV1DocumentSchema": {"description": "A document schema used to define document structure.", "id": "GoogleCloudContentwarehouseV1DocumentSchema", "properties": {"createTime": {"description": "Output only. The time when the document schema is created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Schema description.", "type": "string"}, "displayName": {"description": "Required. Name of the schema given by the user. Must be unique per project.", "type": "string"}, "documentIsFolder": {"description": "Document Type, true refers the document is a folder, otherwise it is a typical document.", "type": "boolean"}, "name": {"description": "The resource name of the document schema. Format: projects/{project_number}/locations/{location}/documentSchemas/{document_schema_id}. The name is ignored when creating a document schema.", "type": "string"}, "propertyDefinitions": {"description": "Document details.", "items": {"$ref": "GoogleCloudContentwarehouseV1PropertyDefinition"}, "type": "array"}, "updateTime": {"description": "Output only. The time when the document schema is last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudContentwarehouseV1EnumArray": {"description": "Enum values.", "id": "GoogleCloudContentwarehouseV1EnumArray", "properties": {"values": {"description": "List of enum values.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudContentwarehouseV1EnumTypeOptions": {"description": "Configurations for an enum/categorical property.", "id": "GoogleCloudContentwarehouseV1EnumTypeOptions", "properties": {"possibleValues": {"description": "Required. List of possible enum values.", "items": {"type": "string"}, "type": "array"}, "validationCheckDisabled": {"description": "Make sure the Enum property value provided in the document is in the possile value list during document creation. The validation check runs by default.", "type": "boolean"}}, "type": "object"}, "GoogleCloudContentwarehouseV1EnumValue": {"description": "Represents the string value of the enum field.", "id": "GoogleCloudContentwarehouseV1EnumValue", "properties": {"value": {"description": "String value of the enum field. This must match defined set of enums in document schema using EnumTypeOptions.", "type": "string"}}, "type": "object"}, "GoogleCloudContentwarehouseV1ExportToCdwPipeline": {"description": "The configuration of exporting documents from the Document Warehouse to CDW pipeline.", "id": "GoogleCloudContentwarehouseV1ExportToCdwPipeline", "properties": {"docAiDataset": {"description": "Optional. The CDW dataset resource name. This field is optional. If not set, the documents will be exported to Cloud Storage only. Format: projects/{project}/locations/{location}/processors/{processor}/dataset", "type": "string"}, "documents": {"description": "The list of all the resource names of the documents to be processed. Format: projects/{project_number}/locations/{location}/documents/{document_id}.", "items": {"type": "string"}, "type": "array"}, "exportFolderPath": {"description": "The Cloud Storage folder path used to store the exported documents before being sent to CDW. Format: `gs:///`.", "type": "string"}, "trainingSplitRatio": {"description": "Ratio of training dataset split. When importing into Document AI Workbench, documents will be automatically split into training and test split category with the specified ratio. This field is required if doc_ai_dataset is set.", "format": "float", "type": "number"}}, "type": "object"}, "GoogleCloudContentwarehouseV1FetchAclRequest": {"description": "Request message for DocumentService.FetchAcl", "id": "GoogleCloudContentwarehouseV1FetchAclRequest", "properties": {"projectOwner": {"description": "For Get Project ACL only. Authorization check for end user will be ignored when project_owner=true.", "type": "boolean"}, "requestMetadata": {"$ref": "GoogleCloudContentwarehouseV1RequestMetadata", "description": "The meta information collected about the end user, used to enforce access control for the service."}}, "type": "object"}, "GoogleCloudContentwarehouseV1FetchAclResponse": {"description": "Response message for DocumentService.FetchAcl.", "id": "GoogleCloudContentwarehouseV1FetchAclResponse", "properties": {"metadata": {"$ref": "GoogleCloudContentwarehouseV1ResponseMetadata", "description": "Additional information for the API invocation, such as the request tracking id."}, "policy": {"$ref": "GoogleIamV1Policy", "description": "The IAM policy."}}, "type": "object"}, "GoogleCloudContentwarehouseV1FileTypeFilter": {"description": "Filter for the specific types of documents returned.", "id": "GoogleCloudContentwarehouseV1FileTypeFilter", "properties": {"fileType": {"description": "The type of files to return.", "enum": ["FILE_TYPE_UNSPECIFIED", "ALL", "FOLDER", "DOCUMENT", "ROOT_FOLDER"], "enumDescriptions": ["Default document type. If set, disables the filter.", "Returns all document types, including folders.", "Returns only folders.", "Returns only non-folder documents.", "Returns only root folders"], "type": "string"}}, "type": "object"}, "GoogleCloudContentwarehouseV1FloatArray": {"description": "Float values.", "id": "GoogleCloudContentwarehouseV1FloatArray", "properties": {"values": {"description": "List of float values.", "items": {"format": "float", "type": "number"}, "type": "array"}}, "type": "object"}, "GoogleCloudContentwarehouseV1FloatTypeOptions": {"description": "Configurations for a float property.", "id": "GoogleCloudContentwarehouseV1FloatTypeOptions", "properties": {}, "type": "object"}, "GoogleCloudContentwarehouseV1GcsIngestPipeline": {"description": "The configuration of the Cloud Storage Ingestion pipeline.", "id": "GoogleCloudContentwarehouseV1GcsIngestPipeline", "properties": {"inputPath": {"description": "The input Cloud Storage folder. All files under this folder will be imported to Document Warehouse. Format: `gs:///`.", "type": "string"}, "pipelineConfig": {"$ref": "GoogleCloudContentwarehouseV1IngestPipelineConfig", "description": "Optional. The config for the Cloud Storage Ingestion pipeline. It provides additional customization options to run the pipeline and can be skipped if it is not applicable."}, "processorType": {"description": "The Doc AI processor type name. Only used when the format of ingested files is Doc AI Document proto format.", "type": "string"}, "schemaName": {"description": "The Document Warehouse schema resource name. All documents processed by this pipeline will use this schema. Format: projects/{project_number}/locations/{location}/documentSchemas/{document_schema_id}.", "type": "string"}, "skipIngestedDocuments": {"description": "The flag whether to skip ingested documents. If it is set to true, documents in Cloud Storage contains key \"status\" with value \"status=ingested\" in custom metadata will be skipped to ingest.", "type": "boolean"}}, "type": "object"}, "GoogleCloudContentwarehouseV1GcsIngestWithDocAiProcessorsPipeline": {"description": "The configuration of the Cloud Storage Ingestion with DocAI Processors pipeline.", "id": "GoogleCloudContentwarehouseV1GcsIngestWithDocAiProcessorsPipeline", "properties": {"extractProcessorInfos": {"description": "The extract processors information. One matched extract processor will be used to process documents based on the classify processor result. If no classify processor is specified, the first extract processor will be used.", "items": {"$ref": "GoogleCloudContentwarehouseV1ProcessorInfo"}, "type": "array"}, "inputPath": {"description": "The input Cloud Storage folder. All files under this folder will be imported to Document Warehouse. Format: `gs:///`.", "type": "string"}, "pipelineConfig": {"$ref": "GoogleCloudContentwarehouseV1IngestPipelineConfig", "description": "Optional. The config for the Cloud Storage Ingestion with DocAI Processors pipeline. It provides additional customization options to run the pipeline and can be skipped if it is not applicable."}, "processorResultsFolderPath": {"description": "The Cloud Storage folder path used to store the raw results from processors. Format: `gs:///`.", "type": "string"}, "skipIngestedDocuments": {"description": "The flag whether to skip ingested documents. If it is set to true, documents in Cloud Storage contains key \"status\" with value \"status=ingested\" in custom metadata will be skipped to ingest.", "type": "boolean"}, "splitClassifyProcessorInfo": {"$ref": "GoogleCloudContentwarehouseV1ProcessorInfo", "description": "The split and classify processor information. The split and classify result will be used to find a matched extract processor."}}, "type": "object"}, "GoogleCloudContentwarehouseV1GetDocumentRequest": {"description": "Request message for DocumentService.GetDocument.", "id": "GoogleCloudContentwarehouseV1GetDocumentRequest", "properties": {"requestMetadata": {"$ref": "GoogleCloudContentwarehouseV1RequestMetadata", "description": "The meta information collected about the end user, used to enforce access control for the service."}}, "type": "object"}, "GoogleCloudContentwarehouseV1HistogramQuery": {"description": "The histogram request.", "id": "GoogleCloudContentwarehouseV1HistogramQuery", "properties": {"filters": {"$ref": "GoogleCloudContentwarehouseV1HistogramQueryPropertyNameFilter", "description": "Optional. Filter the result of histogram query by the property names. It only works with histogram query count('FilterableProperties'). It is an optional. It will perform histogram on all the property names for all the document schemas. Setting this field will have a better performance."}, "histogramQuery": {"description": "An expression specifies a histogram request against matching documents for searches. See SearchDocumentsRequest.histogram_queries for details about syntax.", "type": "string"}, "requirePreciseResultSize": {"description": "Controls if the histogram query requires the return of a precise count. Enable this flag may adversely impact performance. Defaults to true.", "type": "boolean"}}, "type": "object"}, "GoogleCloudContentwarehouseV1HistogramQueryPropertyNameFilter": {"id": "GoogleCloudContentwarehouseV1HistogramQueryPropertyNameFilter", "properties": {"documentSchemas": {"description": "This filter specifies the exact document schema(s) Document.document_schema_name to run histogram query against. It is optional. It will perform histogram for property names for all the document schemas if it is not set. At most 10 document schema names are allowed. Format: projects/{project_number}/locations/{location}/documentSchemas/{document_schema_id}.", "items": {"type": "string"}, "type": "array"}, "propertyNames": {"description": "It is optional. It will perform histogram for all the property names if it is not set. The properties need to be defined with the is_filterable flag set to true and the name of the property should be in the format: \"schemaId.propertyName\". The property needs to be defined in the schema. Example: the schema id is abc. Then the name of property for property MORTGAGE_TYPE will be \"abc.MORTGAGE_TYPE\".", "items": {"type": "string"}, "type": "array"}, "yAxis": {"description": "By default, the y_axis is HISTOGRAM_YAXIS_DOCUMENT if this field is not set.", "enum": ["HISTOGRAM_YAXIS_DOCUMENT", "HISTOGRAM_YAXIS_PROPERTY"], "enumDescriptions": ["Count the documents per property name.", "Count the properties per property name."], "type": "string"}}, "type": "object"}, "GoogleCloudContentwarehouseV1HistogramQueryResult": {"description": "Histogram result that matches HistogramQuery specified in searches.", "id": "GoogleCloudContentwarehouseV1HistogramQueryResult", "properties": {"histogram": {"additionalProperties": {"format": "int64", "type": "string"}, "description": "A map from the values of the facet associated with distinct values to the number of matching entries with corresponding value. The key format is: * (for string histogram) string values stored in the field.", "type": "object"}, "histogramQuery": {"description": "Requested histogram expression.", "type": "string"}}, "type": "object"}, "GoogleCloudContentwarehouseV1IngestPipelineConfig": {"description": "The ingestion pipeline config.", "id": "GoogleCloudContentwarehouseV1IngestPipelineConfig", "properties": {"cloudFunction": {"description": "The Cloud Function resource name. The Cloud Function needs to live inside consumer project and is accessible to Document AI Warehouse P4SA. Only Cloud Functions V2 is supported. Cloud function execution should complete within 5 minutes or this file ingestion may fail due to timeout. Format: `https://{region}-{project_id}.cloudfunctions.net/{cloud_function}` The following keys are available the request json payload. * display_name * properties * plain_text * reference_id * document_schema_name * raw_document_path * raw_document_file_type The following keys from the cloud function json response payload will be ingested to the Document AI Warehouse as part of Document proto content and/or related information. The original values will be overridden if any key is present in the response. * display_name * properties * plain_text * document_acl_policy * folder", "type": "string"}, "documentAclPolicy": {"$ref": "GoogleIamV1Policy", "description": "The document level acl policy config. This refers to an Identity and Access (IAM) policy, which specifies access controls for all documents ingested by the pipeline. The role and members under the policy needs to be specified. The following roles are supported for document level acl control: * roles/contentwarehouse.documentAdmin * roles/contentwarehouse.documentEditor * roles/contentwarehouse.documentViewer The following members are supported for document level acl control: * user:<EMAIL> * group:<EMAIL> Note that for documents searched with LLM, only single level user or group acl check is supported."}, "enableDocumentTextExtraction": {"description": "The document text extraction enabled flag. If the flag is set to true, DWH will perform text extraction on the raw document.", "type": "boolean"}, "folder": {"description": "Optional. The name of the folder to which all ingested documents will be linked during ingestion process. Format is `projects/{project}/locations/{location}/documents/{folder_id}`", "type": "string"}}, "type": "object"}, "GoogleCloudContentwarehouseV1InitializeProjectRequest": {"description": "Request message for projectService.InitializeProject", "id": "GoogleCloudContentwarehouseV1InitializeProjectRequest", "properties": {"accessControlMode": {"description": "Required. The access control mode for accessing the customer data", "enum": ["ACL_MODE_UNKNOWN", "ACL_MODE_UNIVERSAL_ACCESS", "ACL_MODE_DOCUMENT_LEVEL_ACCESS_CONTROL_BYOID", "ACL_MODE_DOCUMENT_LEVEL_ACCESS_CONTROL_GCI"], "enumDescriptions": ["This value is required by protobuf best practices", "Universal Access: No document level access control.", "Document level access control with customer own Identity Service.", "Document level access control using Google Cloud Identity."], "type": "string"}, "databaseType": {"description": "Required. The type of database used to store customer data", "enum": ["DB_UNKNOWN", "DB_INFRA_SPANNER", "DB_CLOUD_SQL_POSTGRES"], "enumDeprecated": [false, false, true], "enumDescriptions": ["This value is required by protobuf best practices", "Internal Spanner", "Cloud Sql with a Postgres Sql instance"], "type": "string"}, "documentCreatorDefaultRole": {"description": "Optional. The default role for the person who create a document.", "enum": ["DOCUMENT_CREATOR_DEFAULT_ROLE_UNSPECIFIED", "DOCUMENT_ADMIN", "DOCUMENT_EDITOR", "DOCUMENT_VIEWER"], "enumDescriptions": ["Unspecified, will be default to document admin role.", "Document Admin, same as contentwarehouse.googleapis.com/documentAdmin.", "Document Editor, same as contentwarehouse.googleapis.com/documentEditor.", "Document Viewer, same as contentwarehouse.googleapis.com/documentViewer."], "type": "string"}, "enableCalUserEmailLogging": {"description": "Optional. Whether to enable CAL user email logging.", "type": "boolean"}, "kmsKey": {"description": "Optional. The KMS key used for CMEK encryption. It is required that the kms key is in the same region as the endpoint. The same key will be used for all provisioned resources, if encryption is available. If the kms_key is left empty, no encryption will be enforced.", "type": "string"}}, "type": "object"}, "GoogleCloudContentwarehouseV1InitializeProjectResponse": {"description": "Response message for projectService.InitializeProject", "id": "GoogleCloudContentwarehouseV1InitializeProjectResponse", "properties": {"message": {"description": "The message of the project initialization process.", "type": "string"}, "state": {"description": "The state of the project initialization process.", "enum": ["STATE_UNSPECIFIED", "SUCCEEDED", "FAILED", "CANCELLED", "RUNNING"], "enumDescriptions": ["Clients should never see this.", "Finished project initialization without error.", "Finished project initialization with an error.", "Client canceled the LRO.", "Ask the customer to check the operation for results."], "type": "string"}}, "type": "object"}, "GoogleCloudContentwarehouseV1IntegerArray": {"description": "Integer values.", "id": "GoogleCloudContentwarehouseV1IntegerArray", "properties": {"values": {"description": "List of integer values.", "items": {"format": "int32", "type": "integer"}, "type": "array"}}, "type": "object"}, "GoogleCloudContentwarehouseV1IntegerTypeOptions": {"description": "Configurations for an integer property.", "id": "GoogleCloudContentwarehouseV1IntegerTypeOptions", "properties": {}, "type": "object"}, "GoogleCloudContentwarehouseV1InvalidRule": {"description": "A triggered rule that failed the validation check(s) after parsing.", "id": "GoogleCloudContentwarehouseV1InvalidRule", "properties": {"error": {"description": "Validation error on a parsed expression.", "type": "string"}, "rule": {"$ref": "GoogleCloudContentwarehouseV1Rule", "description": "Triggered rule."}}, "type": "object"}, "GoogleCloudContentwarehouseV1ListDocumentSchemasResponse": {"description": "Response message for DocumentSchemaService.ListDocumentSchemas.", "id": "GoogleCloudContentwarehouseV1ListDocumentSchemasResponse", "properties": {"documentSchemas": {"description": "The document schemas from the specified parent.", "items": {"$ref": "GoogleCloudContentwarehouseV1DocumentSchema"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleCloudContentwarehouseV1ListLinkedSourcesRequest": {"description": "Response message for DocumentLinkService.ListLinkedSources.", "id": "GoogleCloudContentwarehouseV1ListLinkedSourcesRequest", "properties": {"pageSize": {"description": "The maximum number of document-links to return. The service may return fewer than this value. If unspecified, at most 50 document-links will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListLinkedSources` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListLinkedSources` must match the call that provided the page token.", "type": "string"}, "requestMetadata": {"$ref": "GoogleCloudContentwarehouseV1RequestMetadata", "description": "The meta information collected about the document creator, used to enforce access control for the service."}}, "type": "object"}, "GoogleCloudContentwarehouseV1ListLinkedSourcesResponse": {"description": "Response message for DocumentLinkService.ListLinkedSources.", "id": "GoogleCloudContentwarehouseV1ListLinkedSourcesResponse", "properties": {"documentLinks": {"description": "Source document-links.", "items": {"$ref": "GoogleCloudContentwarehouseV1DocumentLink"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleCloudContentwarehouseV1ListLinkedTargetsRequest": {"description": "Request message for DocumentLinkService.ListLinkedTargets.", "id": "GoogleCloudContentwarehouseV1ListLinkedTargetsRequest", "properties": {"requestMetadata": {"$ref": "GoogleCloudContentwarehouseV1RequestMetadata", "description": "The meta information collected about the document creator, used to enforce access control for the service."}}, "type": "object"}, "GoogleCloudContentwarehouseV1ListLinkedTargetsResponse": {"description": "Response message for DocumentLinkService.ListLinkedTargets.", "id": "GoogleCloudContentwarehouseV1ListLinkedTargetsResponse", "properties": {"documentLinks": {"description": "Target document-links.", "items": {"$ref": "GoogleCloudContentwarehouseV1DocumentLink"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleCloudContentwarehouseV1ListRuleSetsResponse": {"description": "Response message for RuleSetService.ListRuleSets.", "id": "GoogleCloudContentwarehouseV1ListRuleSetsResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "ruleSets": {"description": "The rule sets from the specified parent.", "items": {"$ref": "GoogleCloudContentwarehouseV1RuleSet"}, "type": "array"}}, "type": "object"}, "GoogleCloudContentwarehouseV1ListSynonymSetsResponse": {"description": "Response message for SynonymSetService.ListSynonymSets.", "id": "GoogleCloudContentwarehouseV1ListSynonymSetsResponse", "properties": {"nextPageToken": {"description": "A page token, received from a previous `ListSynonymSets` call. Provide this to retrieve the subsequent page.", "type": "string"}, "synonymSets": {"description": "The synonymSets from the specified parent.", "items": {"$ref": "GoogleCloudContentwarehouseV1SynonymSet"}, "type": "array"}}, "type": "object"}, "GoogleCloudContentwarehouseV1LockDocumentRequest": {"description": "Request message for DocumentService.LockDocument.", "id": "GoogleCloudContentwarehouseV1LockDocumentRequest", "properties": {"collectionId": {"description": "The collection the document connects to.", "type": "string"}, "lockingUser": {"$ref": "GoogleCloudContentwarehouseV1UserInfo", "description": "The user information who locks the document."}}, "type": "object"}, "GoogleCloudContentwarehouseV1MapProperty": {"description": "Map property value. Represents a structured entries of key value pairs, consisting of field names which map to dynamically typed values.", "id": "GoogleCloudContentwarehouseV1MapProperty", "properties": {"fields": {"additionalProperties": {"$ref": "GoogleCloudContentwarehouseV1Value"}, "description": "Unordered map of dynamically typed values.", "type": "object"}}, "type": "object"}, "GoogleCloudContentwarehouseV1MapTypeOptions": {"description": "Configurations for a Map property.", "id": "GoogleCloudContentwarehouseV1MapTypeOptions", "properties": {}, "type": "object"}, "GoogleCloudContentwarehouseV1MergeFieldsOptions": {"description": "Options for merging updated fields.", "id": "GoogleCloudContentwarehouseV1MergeFieldsOptions", "properties": {"replaceMessageFields": {"description": "When merging message fields, the default behavior is to merge the content of two message fields together. If you instead want to use the field from the source message to replace the corresponding field in the destination message, set this flag to true. When this flag is set, specified submessage fields that are missing in source will be cleared in destination.", "type": "boolean"}, "replaceRepeatedFields": {"description": "When merging repeated fields, the default behavior is to append entries from the source repeated field to the destination repeated field. If you instead want to keep only the entries from the source repeated field, set this flag to true. If you want to replace a repeated field within a message field on the destination message, you must set both replace_repeated_fields and replace_message_fields to true, otherwise the repeated fields will be appended.", "type": "boolean"}}, "type": "object"}, "GoogleCloudContentwarehouseV1ProcessWithDocAiPipeline": {"description": "The configuration of processing documents in Document Warehouse with DocAi processors pipeline.", "id": "GoogleCloudContentwarehouseV1ProcessWithDocAiPipeline", "properties": {"documents": {"description": "The list of all the resource names of the documents to be processed. Format: projects/{project_number}/locations/{location}/documents/{document_id}.", "items": {"type": "string"}, "type": "array"}, "exportFolderPath": {"description": "The Cloud Storage folder path used to store the exported documents before being sent to CDW. Format: `gs:///`.", "type": "string"}, "processorInfo": {"$ref": "GoogleCloudContentwarehouseV1ProcessorInfo", "description": "The CDW processor information."}, "processorResultsFolderPath": {"description": "The Cloud Storage folder path used to store the raw results from processors. Format: `gs:///`.", "type": "string"}}, "type": "object"}, "GoogleCloudContentwarehouseV1ProcessorInfo": {"description": "The DocAI processor information.", "id": "GoogleCloudContentwarehouseV1ProcessorInfo", "properties": {"documentType": {"description": "The processor will process the documents with this document type.", "type": "string"}, "processorName": {"description": "The processor resource name. Format is `projects/{project}/locations/{location}/processors/{processor}`, or `projects/{project}/locations/{location}/processors/{processor}/processorVersions/{processorVersion}`", "type": "string"}, "schemaName": {"description": "The Document schema resource name. All documents processed by this processor will use this schema. Format: projects/{project_number}/locations/{location}/documentSchemas/{document_schema_id}.", "type": "string"}}, "type": "object"}, "GoogleCloudContentwarehouseV1ProjectStatus": {"description": "Status of a project, including the project state, dbType, aclMode and etc.", "id": "GoogleCloudContentwarehouseV1ProjectStatus", "properties": {"accessControlMode": {"description": "Access control mode.", "enum": ["ACL_MODE_UNKNOWN", "ACL_MODE_UNIVERSAL_ACCESS", "ACL_MODE_DOCUMENT_LEVEL_ACCESS_CONTROL_BYOID", "ACL_MODE_DOCUMENT_LEVEL_ACCESS_CONTROL_GCI"], "enumDescriptions": ["This value is required by protobuf best practices", "Universal Access: No document level access control.", "Document level access control with customer own Identity Service.", "Document level access control using Google Cloud Identity."], "type": "string"}, "databaseType": {"description": "Database type.", "enum": ["DB_UNKNOWN", "DB_INFRA_SPANNER", "DB_CLOUD_SQL_POSTGRES"], "enumDeprecated": [false, false, true], "enumDescriptions": ["This value is required by protobuf best practices", "Internal Spanner", "Cloud Sql with a Postgres Sql instance"], "type": "string"}, "documentCreatorDefaultRole": {"description": "The default role for the person who create a document.", "type": "string"}, "location": {"description": "The location of the queried project.", "type": "string"}, "qaEnabled": {"description": "If the qa is enabled on this project.", "type": "boolean"}, "state": {"description": "State of the project.", "enum": ["PROJECT_STATE_UNSPECIFIED", "PROJECT_STATE_PENDING", "PROJECT_STATE_COMPLETED", "PROJECT_STATE_FAILED", "PROJECT_STATE_DELETING", "PROJECT_STATE_DELETING_FAILED", "PROJECT_STATE_DELETED", "PROJECT_STATE_NOT_FOUND"], "enumDescriptions": ["Default status, required by protobuf best practices.", "The project is in the middle of a provision process.", "All dependencies have been provisioned.", "A provision process was previously initiated, but failed.", "The project is in the middle of a deletion process.", "A deleting process was initiated, but failed.", "The project is deleted.", "The project is not found."], "type": "string"}}, "type": "object"}, "GoogleCloudContentwarehouseV1Property": {"description": "Property of a document.", "id": "GoogleCloudContentwarehouseV1Property", "properties": {"dateTimeValues": {"$ref": "GoogleCloudContentwarehouseV1DateTimeArray", "description": "Date time property values. It is not supported by CMEK compliant deployment."}, "enumValues": {"$ref": "GoogleCloudContentwarehouseV1EnumArray", "description": "Enum property values."}, "floatValues": {"$ref": "GoogleCloudContentwarehouseV1FloatArray", "description": "Float property values."}, "integerValues": {"$ref": "GoogleCloudContentwarehouseV1IntegerArray", "description": "Integer property values."}, "mapProperty": {"$ref": "GoogleCloudContentwarehouseV1MapProperty", "description": "Map property values."}, "name": {"description": "Required. Must match the name of a PropertyDefinition in the DocumentSchema.", "type": "string"}, "propertyValues": {"$ref": "GoogleCloudContentwarehouseV1PropertyArray", "description": "Nested structured data property values."}, "textValues": {"$ref": "GoogleCloudContentwarehouseV1TextArray", "description": "String/text property values."}, "timestampValues": {"$ref": "GoogleCloudContentwarehouseV1TimestampArray", "description": "Timestamp property values. It is not supported by CMEK compliant deployment."}}, "type": "object"}, "GoogleCloudContentwarehouseV1PropertyArray": {"description": "Property values.", "id": "GoogleCloudContentwarehouseV1PropertyArray", "properties": {"properties": {"description": "List of property values.", "items": {"$ref": "GoogleCloudContentwarehouseV1Property"}, "type": "array"}}, "type": "object"}, "GoogleCloudContentwarehouseV1PropertyDefinition": {"description": "Defines the metadata for a schema property.", "id": "GoogleCloudContentwarehouseV1PropertyDefinition", "properties": {"dateTimeTypeOptions": {"$ref": "GoogleCloudContentwarehouseV1DateTimeTypeOptions", "description": "Date time property. It is not supported by CMEK compliant deployment."}, "displayName": {"description": "The display-name for the property, used for front-end.", "type": "string"}, "enumTypeOptions": {"$ref": "GoogleCloudContentwarehouseV1EnumTypeOptions", "description": "Enum/categorical property."}, "floatTypeOptions": {"$ref": "GoogleCloudContentwarehouseV1FloatTypeOptions", "description": "Float property."}, "integerTypeOptions": {"$ref": "GoogleCloudContentwarehouseV1IntegerTypeOptions", "description": "Integer property."}, "isFilterable": {"description": "Whether the property can be filtered. If this is a sub-property, all the parent properties must be marked filterable.", "type": "boolean"}, "isMetadata": {"description": "Whether the property is user supplied metadata. This out-of-the box placeholder setting can be used to tag derived properties. Its value and interpretation logic should be implemented by API user.", "type": "boolean"}, "isRepeatable": {"description": "Whether the property can have multiple values.", "type": "boolean"}, "isRequired": {"description": "Whether the property is mandatory. Default is 'false', i.e. populating property value can be skipped. If 'true' then user must populate the value for this property.", "type": "boolean"}, "isSearchable": {"description": "Indicates that the property should be included in a global search.", "type": "boolean"}, "mapTypeOptions": {"$ref": "GoogleCloudContentwarehouseV1MapTypeOptions", "description": "Map property."}, "name": {"description": "Required. The name of the metadata property. Must be unique within a document schema and is case insensitive. Names must be non-blank, start with a letter, and can contain alphanumeric characters and: /, :, -, _, and .", "type": "string"}, "propertyTypeOptions": {"$ref": "GoogleCloudContentwarehouseV1PropertyTypeOptions", "description": "Nested structured data property."}, "retrievalImportance": {"description": "The retrieval importance of the property during search.", "enum": ["RETRIEVAL_IMPORTANCE_UNSPECIFIED", "HIGHEST", "HIGHER", "HIGH", "MEDIUM", "LOW", "LOWEST"], "enumDescriptions": ["No importance specified. Default medium importance.", "Highest importance.", "Higher importance.", "High importance.", "Medium importance.", "Low importance (negative).", "Lowest importance (negative)."], "type": "string"}, "schemaSources": {"description": "The mapping information between this property to another schema source.", "items": {"$ref": "GoogleCloudContentwarehouseV1PropertyDefinitionSchemaSource"}, "type": "array"}, "textTypeOptions": {"$ref": "GoogleCloudContentwarehouseV1TextTypeOptions", "description": "Text/string property."}, "timestampTypeOptions": {"$ref": "GoogleCloudContentwarehouseV1TimestampTypeOptions", "description": "Timestamp property. It is not supported by CMEK compliant deployment."}}, "type": "object"}, "GoogleCloudContentwarehouseV1PropertyDefinitionSchemaSource": {"description": "The schema source information.", "id": "GoogleCloudContentwarehouseV1PropertyDefinitionSchemaSource", "properties": {"name": {"description": "The schema name in the source.", "type": "string"}, "processorType": {"description": "The Doc AI processor type name.", "type": "string"}}, "type": "object"}, "GoogleCloudContentwarehouseV1PropertyFilter": {"id": "GoogleCloudContentwarehouseV1PropertyFilter", "properties": {"condition": {"description": "The filter condition. The syntax for this expression is a subset of SQL syntax. Supported operators are: `=`, `!=`, `<`, `<=`, `>`, `>=`, and `~~` where the left of the operator is a property name and the right of the operator is a number or a quoted string. You must escape backslash (\\\\) and quote (\\\") characters. `~~` is the LIKE operator. The right of the operator must be a string. The only supported property data type for LIKE is text_values. It provides semantic search functionality by parsing, stemming and doing synonyms expansion against the input query. It matches if the property contains semantic similar content to the query. It is not regex matching or wildcard matching. For example, \"property.company ~~ \\\"google\\\"\" will match records whose property `property.compnay` have values like \"Google Inc.\", \"Google LLC\" or \"Google Company\". Supported functions are `LOWER([property_name])` to perform a case insensitive match and `EMPTY([property_name])` to filter on the existence of a key. Boolean expressions (AND/OR/NOT) are supported up to 3 levels of nesting (for example, \"((A AND B AND C) OR NOT D) AND E\"), a maximum of 100 comparisons or functions are allowed in the expression. The expression must be < 6000 bytes in length. Only properties that are marked filterable are allowed (PropertyDefinition.is_filterable). Property names do not need to be prefixed by the document schema id (as is the case with histograms), however property names will need to be prefixed by its parent hierarchy, if any. For example: top_property_name.sub_property_name. Sample Query: `(LOWER(driving_license)=\"class \\\"a\\\"\" OR EMPTY(driving_license)) AND driving_years > 10` CMEK compliant deployment only supports: * Operators: `=`, `<`, `<=`, `>`, and `>=`. * Boolean expressions: AND and OR.", "type": "string"}, "documentSchemaName": {"description": "The Document schema name Document.document_schema_name. Format: projects/{project_number}/locations/{location}/documentSchemas/{document_schema_id}.", "type": "string"}}, "type": "object"}, "GoogleCloudContentwarehouseV1PropertyTypeOptions": {"description": "Configurations for a nested structured data property.", "id": "GoogleCloudContentwarehouseV1PropertyTypeOptions", "properties": {"propertyDefinitions": {"description": "Required. List of property definitions.", "items": {"$ref": "GoogleCloudContentwarehouseV1PropertyDefinition"}, "type": "array"}}, "type": "object"}, "GoogleCloudContentwarehouseV1PublishAction": {"description": "Represents the action responsible for publishing messages to a Pub/Sub topic.", "id": "GoogleCloudContentwarehouseV1PublishAction", "properties": {"messages": {"description": "Messages to be published.", "items": {"type": "string"}, "type": "array"}, "topicId": {"description": "The topic id in the Pub/Sub service for which messages will be published to.", "type": "string"}}, "type": "object"}, "GoogleCloudContentwarehouseV1QAResult": {"description": "Additional result info for the question-answering feature.", "id": "GoogleCloudContentwarehouseV1QAResult", "properties": {"confidenceScore": {"description": "The calibrated confidence score for this document, in the range [0., 1.]. This represents the confidence level for whether the returned document and snippet answers the user's query.", "format": "float", "type": "number"}, "highlights": {"description": "Highlighted sections in the snippet.", "items": {"$ref": "GoogleCloudContentwarehouseV1QAResultHighlight"}, "type": "array"}}, "type": "object"}, "GoogleCloudContentwarehouseV1QAResultHighlight": {"description": "A text span in the search text snippet that represents a highlighted section (answer context, highly relevant sentence, etc.).", "id": "GoogleCloudContentwarehouseV1QAResultHighlight", "properties": {"endIndex": {"description": "End index of the highlight, exclusive.", "format": "int32", "type": "integer"}, "startIndex": {"description": "Start index of the highlight.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudContentwarehouseV1RemoveFromFolderAction": {"description": "Represents the action responsible for remove a document from a specific folder.", "id": "GoogleCloudContentwarehouseV1RemoveFromFolderAction", "properties": {"condition": {"description": "Condition of the action to be executed.", "type": "string"}, "folder": {"description": "Name of the folder under which new document is to be added. Format: projects/{project_number}/locations/{location}/documents/{document_id}.", "type": "string"}}, "type": "object"}, "GoogleCloudContentwarehouseV1RequestMetadata": {"description": "Meta information is used to improve the performance of the service.", "id": "GoogleCloudContentwarehouseV1RequestMetadata", "properties": {"userInfo": {"$ref": "GoogleCloudContentwarehouseV1UserInfo", "description": "Provides user unique identification and groups information."}}, "type": "object"}, "GoogleCloudContentwarehouseV1ResponseMetadata": {"description": "Additional information returned to client, such as debugging information.", "id": "GoogleCloudContentwarehouseV1ResponseMetadata", "properties": {"requestId": {"description": "A unique id associated with this call. This id is logged for tracking purpose.", "type": "string"}}, "type": "object"}, "GoogleCloudContentwarehouseV1Rule": {"description": "Represents the rule for a content warehouse trigger.", "id": "GoogleCloudContentwarehouseV1Rule", "properties": {"actions": {"description": "List of actions that are executed when the rule is satisfied.", "items": {"$ref": "GoogleCloudContentwarehouseV1Action"}, "type": "array"}, "condition": {"description": "Represents the conditional expression to be evaluated. Expression should evaluate to a boolean result. When the condition is true actions are executed. Example: user_role = \"hsbc_role_1\" AND doc.salary > 20000", "type": "string"}, "description": {"description": "Short description of the rule and its context.", "type": "string"}, "ruleId": {"description": "ID of the rule. It has to be unique across all the examples. This is managed internally.", "type": "string"}, "triggerType": {"description": "Identifies the trigger type for running the policy.", "enum": ["UNKNOWN", "ON_CREATE", "ON_UPDATE", "ON_CREATE_LINK", "ON_DELETE_LINK"], "enumDescriptions": ["Trigger for unknown action.", "Trigger for create document action.", "Trigger for update document action.", "Trigger for create link action.", "Trigger for delete link action."], "type": "string"}}, "type": "object"}, "GoogleCloudContentwarehouseV1RuleActionsPair": {"description": "Represents a rule and outputs of associated actions.", "id": "GoogleCloudContentwarehouseV1RuleActionsPair", "properties": {"actionOutputs": {"description": "Outputs of executing the actions associated with the above rule.", "items": {"$ref": "GoogleCloudContentwarehouseV1ActionOutput"}, "type": "array"}, "rule": {"$ref": "GoogleCloudContentwarehouseV1Rule", "description": "Represents the rule."}}, "type": "object"}, "GoogleCloudContentwarehouseV1RuleEngineOutput": {"description": "Records the output of Rule Engine including rule evaluation and actions result.", "id": "GoogleCloudContentwarehouseV1RuleEngineOutput", "properties": {"actionExecutorOutput": {"$ref": "GoogleCloudContentwarehouseV1ActionExecutorOutput", "description": "Output from Action Executor containing rule and corresponding actions execution result."}, "documentName": {"description": "Name of the document against which the rules and actions were evaluated.", "type": "string"}, "ruleEvaluatorOutput": {"$ref": "GoogleCloudContentwarehouseV1RuleEvaluatorOutput", "description": "Output from Rule Evaluator containing matched, unmatched and invalid rules."}}, "type": "object"}, "GoogleCloudContentwarehouseV1RuleEvaluatorOutput": {"description": "Represents the output of the Rule Evaluator.", "id": "GoogleCloudContentwarehouseV1RuleEvaluatorOutput", "properties": {"invalidRules": {"description": "A subset of triggered rules that failed the validation check(s) after parsing.", "items": {"$ref": "GoogleCloudContentwarehouseV1InvalidRule"}, "type": "array"}, "matchedRules": {"description": "A subset of triggered rules that are evaluated true for a given request.", "items": {"$ref": "GoogleCloudContentwarehouseV1Rule"}, "type": "array"}, "triggeredRules": {"description": "List of rules fetched from database for the given request trigger type.", "items": {"$ref": "GoogleCloudContentwarehouseV1Rule"}, "type": "array"}}, "type": "object"}, "GoogleCloudContentwarehouseV1RuleSet": {"description": "Represents a set of rules from a single customer.", "id": "GoogleCloudContentwarehouseV1RuleSet", "properties": {"description": {"description": "Short description of the rule-set.", "type": "string"}, "name": {"description": "The resource name of the rule set. Managed internally. Format: projects/{project_number}/locations/{location}/ruleSet/{rule_set_id}. The name is ignored when creating a rule set.", "type": "string"}, "rules": {"description": "List of rules given by the customer.", "items": {"$ref": "GoogleCloudContentwarehouseV1Rule"}, "type": "array"}, "source": {"description": "Source of the rules i.e., customer name.", "type": "string"}}, "type": "object"}, "GoogleCloudContentwarehouseV1RunPipelineMetadata": {"description": "Metadata message of RunPipeline method.", "id": "GoogleCloudContentwarehouseV1RunPipelineMetadata", "properties": {"exportToCdwPipelineMetadata": {"$ref": "GoogleCloudContentwarehouseV1RunPipelineMetadataExportToCdwPipelineMetadata", "description": "The pipeline metadata for Export-to-CDW pipeline."}, "failedFileCount": {"description": "Number of files that have failed at some point in the pipeline.", "format": "int32", "type": "integer"}, "gcsIngestPipelineMetadata": {"$ref": "GoogleCloudContentwarehouseV1RunPipelineMetadataGcsIngestPipelineMetadata", "description": "The pipeline metadata for GcsIngest pipeline."}, "individualDocumentStatuses": {"description": "The list of response details of each document.", "items": {"$ref": "GoogleCloudContentwarehouseV1RunPipelineMetadataIndividualDocumentStatus"}, "type": "array"}, "processWithDocAiPipelineMetadata": {"$ref": "GoogleCloudContentwarehouseV1RunPipelineMetadataProcessWithDocAiPipelineMetadata", "description": "The pipeline metadata for Process-with-DocAi pipeline."}, "totalFileCount": {"description": "Number of files that were processed by the pipeline.", "format": "int32", "type": "integer"}, "userInfo": {"$ref": "GoogleCloudContentwarehouseV1UserInfo", "description": "User unique identification and groups information."}}, "type": "object"}, "GoogleCloudContentwarehouseV1RunPipelineMetadataExportToCdwPipelineMetadata": {"description": "The metadata message for Export-to-CDW pipeline.", "id": "GoogleCloudContentwarehouseV1RunPipelineMetadataExportToCdwPipelineMetadata", "properties": {"docAiDataset": {"description": "The output CDW dataset resource name.", "type": "string"}, "documents": {"description": "The input list of all the resource names of the documents to be exported.", "items": {"type": "string"}, "type": "array"}, "outputPath": {"description": "The output Cloud Storage folder in this pipeline.", "type": "string"}}, "type": "object"}, "GoogleCloudContentwarehouseV1RunPipelineMetadataGcsIngestPipelineMetadata": {"description": "The metadata message for GcsIngest pipeline.", "id": "GoogleCloudContentwarehouseV1RunPipelineMetadataGcsIngestPipelineMetadata", "properties": {"inputPath": {"description": "The input Cloud Storage folder in this pipeline. Format: `gs:///`.", "type": "string"}}, "type": "object"}, "GoogleCloudContentwarehouseV1RunPipelineMetadataIndividualDocumentStatus": {"description": "The status of processing a document.", "id": "GoogleCloudContentwarehouseV1RunPipelineMetadataIndividualDocumentStatus", "properties": {"documentId": {"description": "Document identifier of an existing document.", "type": "string"}, "status": {"$ref": "GoogleRpcStatus", "description": "The status processing the document."}}, "type": "object"}, "GoogleCloudContentwarehouseV1RunPipelineMetadataProcessWithDocAiPipelineMetadata": {"description": "The metadata message for Process-with-DocAi pipeline.", "id": "GoogleCloudContentwarehouseV1RunPipelineMetadataProcessWithDocAiPipelineMetadata", "properties": {"documents": {"description": "The input list of all the resource names of the documents to be processed.", "items": {"type": "string"}, "type": "array"}, "processorInfo": {"$ref": "GoogleCloudContentwarehouseV1ProcessorInfo", "description": "The DocAI processor to process the documents with."}}, "type": "object"}, "GoogleCloudContentwarehouseV1RunPipelineRequest": {"description": "Request message for DocumentService.RunPipeline.", "id": "GoogleCloudContentwarehouseV1RunPipelineRequest", "properties": {"exportCdwPipeline": {"$ref": "GoogleCloudContentwarehouseV1ExportToCdwPipeline", "description": "Export docuemnts from Document Warehouse to CDW for training purpose."}, "gcsIngestPipeline": {"$ref": "GoogleCloudContentwarehouseV1GcsIngestPipeline", "description": "Cloud Storage ingestion pipeline."}, "gcsIngestWithDocAiProcessorsPipeline": {"$ref": "GoogleCloudContentwarehouseV1GcsIngestWithDocAiProcessorsPipeline", "description": "Use DocAI processors to process documents in Cloud Storage and ingest them to Document Warehouse."}, "processWithDocAiPipeline": {"$ref": "GoogleCloudContentwarehouseV1ProcessWithDocAiPipeline", "description": "Use a DocAI processor to process documents in Document Warehouse, and re-ingest the updated results into Document Warehouse."}, "requestMetadata": {"$ref": "GoogleCloudContentwarehouseV1RequestMetadata", "description": "The meta information collected about the end user, used to enforce access control for the service."}}, "type": "object"}, "GoogleCloudContentwarehouseV1SearchDocumentsRequest": {"description": "Request message for DocumentService.SearchDocuments.", "id": "GoogleCloudContentwarehouseV1SearchDocumentsRequest", "properties": {"documentQuery": {"$ref": "GoogleCloudContentwarehouseV1DocumentQuery", "description": "Query used to search against documents (keyword, filters, etc.)."}, "histogramQueries": {"description": "An expression specifying a histogram request against matching documents. Expression syntax is an aggregation function call with histogram facets and other options. The following aggregation functions are supported: * `count(string_histogram_facet)`: Count the number of matching entities for each distinct attribute value. Data types: * Histogram facet (aka filterable properties): Facet names with format <schema id>.<facet>. Facets will have the format of: `a-zA-Z`. If the facet is a child facet, then the parent hierarchy needs to be specified separated by dots in the prefix after the schema id. Thus, the format for a multi- level facet is: <schema id>.<parent facet name>. <child facet name>. Example: schema123.root_parent_facet.middle_facet.child_facet * DocumentSchemaId: (with no schema id prefix) to get histograms for each document type (returns the schema id path, e.g. projects/12345/locations/us-west/documentSchemas/abc123). Example expression: * Document type counts: count('DocumentSchemaId') * For schema id, abc123, get the counts for MORTGAGE_TYPE: count('abc123.MORTGAGE_TYPE')", "items": {"$ref": "GoogleCloudContentwarehouseV1HistogramQuery"}, "type": "array"}, "offset": {"description": "An integer that specifies the current offset (that is, starting result location, amongst the documents deemed by the API as relevant) in search results. This field is only considered if page_token is unset. The maximum allowed value is 5000. Otherwise an error is thrown. For example, 0 means to return results starting from the first matching document, and 10 means to return from the 11th document. This can be used for pagination, (for example, pageSize = 10 and offset = 10 means to return from the second page).", "format": "int32", "type": "integer"}, "orderBy": {"description": "The criteria determining how search results are sorted. For non-empty query, default is `\"relevance desc\"`. For empty query, default is `\"upload_date desc\"`. Supported options are: * `\"relevance desc\"`: By relevance descending, as determined by the API algorithms. * `\"upload_date desc\"`: By upload date descending. * `\"upload_date\"`: By upload date ascending. * `\"update_date desc\"`: By last updated date descending. * `\"update_date\"`: By last updated date ascending. * `\"retrieval_importance desc\"`: By retrieval importance of properties descending. This feature is still under development, please do not use unless otherwise instructed to do so.", "type": "string"}, "pageSize": {"description": "A limit on the number of documents returned in the search results. Increasing this value above the default value of 10 can increase search response time. The value can be between 1 and 100.", "format": "int32", "type": "integer"}, "pageToken": {"description": "The token specifying the current offset within search results. See SearchDocumentsResponse.next_page_token for an explanation of how to obtain the next set of query results.", "type": "string"}, "qaSizeLimit": {"description": "Experimental, do not use. The limit on the number of documents returned for the question-answering feature. To enable the question-answering feature, set [DocumentQuery].is_nl_query to true.", "format": "int32", "type": "integer"}, "requestMetadata": {"$ref": "GoogleCloudContentwarehouseV1RequestMetadata", "description": "The meta information collected about the end user, used to enforce access control and improve the search quality of the service."}, "requireTotalSize": {"description": "Controls if the search document request requires the return of a total size of matched documents. See SearchDocumentsResponse.total_size. Enabling this flag may adversely impact performance. Hint: If this is used with pagination, set this flag on the initial query but set this to false on subsequent page calls (keep the total count locally). Defaults to false.", "type": "boolean"}, "totalResultSize": {"description": "Controls if the search document request requires the return of a total size of matched documents. See SearchDocumentsResponse.total_size.", "enum": ["TOTAL_RESULT_SIZE_UNSPECIFIED", "ESTIMATED_SIZE", "ACTUAL_SIZE"], "enumDescriptions": ["Total number calculation will be skipped.", "Estimate total number. The total result size will be accurated up to 10,000. This option will add cost and latency to your request.", "It may adversely impact performance. The limit is 1000,000."], "type": "string"}}, "type": "object"}, "GoogleCloudContentwarehouseV1SearchDocumentsResponse": {"description": "Response message for DocumentService.SearchDocuments.", "id": "GoogleCloudContentwarehouseV1SearchDocumentsResponse", "properties": {"histogramQueryResults": {"description": "The histogram results that match with the specified SearchDocumentsRequest.histogram_queries.", "items": {"$ref": "GoogleCloudContentwarehouseV1HistogramQueryResult"}, "type": "array"}, "matchingDocuments": {"description": "The document entities that match the specified SearchDocumentsRequest.", "items": {"$ref": "GoogleCloudContentwarehouseV1SearchDocumentsResponseMatchingDocument"}, "type": "array"}, "metadata": {"$ref": "GoogleCloudContentwarehouseV1ResponseMetadata", "description": "Additional information for the API invocation, such as the request tracking id."}, "nextPageToken": {"description": "The token that specifies the starting position of the next page of results. This field is empty if there are no more results.", "type": "string"}, "questionAnswer": {"description": "Experimental. Question answer from the query against the document.", "type": "string"}, "totalSize": {"description": "The total number of matched documents which is available only if the client set SearchDocumentsRequest.require_total_size to `true` or set SearchDocumentsRequest.total_result_size to `ESTIMATED_SIZE` or `ACTUAL_SIZE`. Otherwise, the value will be `-1`. Typically a UI would handle this condition by displaying \"of many\", for example: \"Displaying 10 of many\".", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudContentwarehouseV1SearchDocumentsResponseMatchingDocument": {"description": "Document entry with metadata inside SearchDocumentsResponse", "id": "GoogleCloudContentwarehouseV1SearchDocumentsResponseMatchingDocument", "properties": {"document": {"$ref": "GoogleCloudContentwarehouseV1Document", "description": "Document that matches the specified SearchDocumentsRequest. This document only contains indexed metadata information."}, "matchedTokenPageIndices": {"description": "Return the 1-based page indices where those pages have one or more matched tokens.", "items": {"format": "int64", "type": "string"}, "type": "array"}, "qaResult": {"$ref": "GoogleCloudContentwarehouseV1QAResult", "description": "Experimental. Additional result info if the question-answering feature is enabled."}, "searchTextSnippet": {"description": "Contains snippets of text from the document full raw text that most closely match a search query's keywords, if available. All HTML tags in the original fields are stripped when returned in this field, and matching query keywords are enclosed in HTML bold tags. If the question-answering feature is enabled, this field will instead contain a snippet that answers the user's natural-language query. No HTML bold tags will be present, and highlights in the answer snippet can be found in QAResult.highlights.", "type": "string"}}, "type": "object"}, "GoogleCloudContentwarehouseV1SetAclRequest": {"description": "Request message for DocumentService.SetAcl.", "id": "GoogleCloudContentwarehouseV1SetAclRequest", "properties": {"policy": {"$ref": "GoogleIamV1Policy", "description": "Required. REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. This refers to an Identity and Access (IAM) policy, which specifies access controls for the Document. You can set ACL with condition for projects only. Supported operators are: `=`, `!=`, `<`, `<=`, `>`, and `>=` where the left of the operator is `DocumentSchemaId` or property name and the right of the operator is a number or a quoted string. You must escape backslash (\\\\) and quote (\\\") characters. Boolean expressions (AND/OR) are supported up to 3 levels of nesting (for example, \"((A AND B AND C) OR D) AND E\"), a maximum of 10 comparisons are allowed in the expression. The expression must be < 6000 bytes in length. Sample condition: `\"DocumentSchemaId = \\\"some schema id\\\" OR SchemaId.floatPropertyName >= 10\"`"}, "projectOwner": {"description": "For Set Project ACL only. Authorization check for end user will be ignored when project_owner=true.", "type": "boolean"}, "requestMetadata": {"$ref": "GoogleCloudContentwarehouseV1RequestMetadata", "description": "The meta information collected about the end user, used to enforce access control for the service."}}, "type": "object"}, "GoogleCloudContentwarehouseV1SetAclResponse": {"description": "Response message for DocumentService.SetAcl.", "id": "GoogleCloudContentwarehouseV1SetAclResponse", "properties": {"metadata": {"$ref": "GoogleCloudContentwarehouseV1ResponseMetadata", "description": "Additional information for the API invocation, such as the request tracking id."}, "policy": {"$ref": "GoogleIamV1Policy", "description": "The policy will be attached to a resource (e.g. projecct, document)."}}, "type": "object"}, "GoogleCloudContentwarehouseV1SynonymSet": {"description": "Represents a list of synonyms for a given context. For example a context \"sales\" could contain: Synonym 1: sale, invoice, bill, order Synonym 2: money, credit, finance, payment Synonym 3: shipping, freight, transport Each SynonymSets should be disjoint", "id": "GoogleCloudContentwarehouseV1SynonymSet", "properties": {"context": {"description": "This is a freeform field. Example contexts can be \"sales,\" \"engineering,\" \"real estate,\" \"accounting,\" etc. The context can be supplied during search requests.", "type": "string"}, "name": {"description": "The resource name of the SynonymSet This is mandatory for google.api.resource. Format: projects/{project_number}/locations/{location}/synonymSets/{context}.", "type": "string"}, "synonyms": {"description": "List of Synonyms for the context.", "items": {"$ref": "GoogleCloudContentwarehouseV1SynonymSetSynonym"}, "type": "array"}}, "type": "object"}, "GoogleCloudContentwarehouseV1SynonymSetSynonym": {"description": "Represents a list of words given by the customer All these words are synonyms of each other.", "id": "GoogleCloudContentwarehouseV1SynonymSetSynonym", "properties": {"words": {"description": "For example: sale, invoice, bill, order", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudContentwarehouseV1TextArray": {"description": "String/text values.", "id": "GoogleCloudContentwarehouseV1TextArray", "properties": {"values": {"description": "List of text values.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudContentwarehouseV1TextTypeOptions": {"description": "Configurations for a text property.", "id": "GoogleCloudContentwarehouseV1TextTypeOptions", "properties": {}, "type": "object"}, "GoogleCloudContentwarehouseV1TimeFilter": {"description": "Filter on create timestamp or update timestamp of documents.", "id": "GoogleCloudContentwarehouseV1TimeFilter", "properties": {"timeField": {"description": "Specifies which time field to filter documents on. Defaults to TimeField.UPLOAD_TIME.", "enum": ["TIME_FIELD_UNSPECIFIED", "CREATE_TIME", "UPDATE_TIME", "DISPOSITION_TIME"], "enumDescriptions": ["Default value.", "Earliest document create time.", "Latest document update time.", "Time when document becomes mutable again."], "type": "string"}, "timeRange": {"$ref": "GoogleTypeInterval"}}, "type": "object"}, "GoogleCloudContentwarehouseV1TimestampArray": {"description": "Timestamp values.", "id": "GoogleCloudContentwarehouseV1TimestampArray", "properties": {"values": {"description": "List of timestamp values.", "items": {"$ref": "GoogleCloudContentwarehouseV1TimestampValue"}, "type": "array"}}, "type": "object"}, "GoogleCloudContentwarehouseV1TimestampTypeOptions": {"description": "Configurations for a timestamp property.", "id": "GoogleCloudContentwarehouseV1TimestampTypeOptions", "properties": {}, "type": "object"}, "GoogleCloudContentwarehouseV1TimestampValue": {"description": "Timestamp value type.", "id": "GoogleCloudContentwarehouseV1TimestampValue", "properties": {"textValue": {"description": "The string must represent a valid instant in UTC and is parsed using java.time.format.DateTimeFormatter.ISO_INSTANT. e.g. \"2013-09-29T18:46:19Z\"", "type": "string"}, "timestampValue": {"description": "Timestamp value", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudContentwarehouseV1UpdateDocumentMetadata": {"description": "Metadata object for UpdateDocument request (currently empty).", "id": "GoogleCloudContentwarehouseV1UpdateDocumentMetadata", "properties": {}, "type": "object"}, "GoogleCloudContentwarehouseV1UpdateDocumentRequest": {"description": "Request message for DocumentService.UpdateDocument.", "id": "GoogleCloudContentwarehouseV1UpdateDocumentRequest", "properties": {"cloudAiDocumentOption": {"$ref": "GoogleCloudContentwarehouseV1CloudAIDocumentOption", "description": "Request Option for processing Cloud AI Document in Document Warehouse. This field offers limited support for mapping entities from Cloud AI Document to Warehouse Document. Please consult with product team before using this field and other available options."}, "document": {"$ref": "GoogleCloudContentwarehouseV1Document", "description": "Required. The document to update."}, "requestMetadata": {"$ref": "GoogleCloudContentwarehouseV1RequestMetadata", "description": "The meta information collected about the end user, used to enforce access control for the service."}, "updateOptions": {"$ref": "GoogleCloudContentwarehouseV1UpdateOptions", "description": "Options for the update operation."}}, "type": "object"}, "GoogleCloudContentwarehouseV1UpdateDocumentResponse": {"description": "Response message for DocumentService.UpdateDocument.", "id": "GoogleCloudContentwarehouseV1UpdateDocumentResponse", "properties": {"document": {"$ref": "GoogleCloudContentwarehouseV1Document", "description": "Updated document after executing update request."}, "metadata": {"$ref": "GoogleCloudContentwarehouseV1ResponseMetadata", "description": "Additional information for the API invocation, such as the request tracking id."}, "ruleEngineOutput": {"$ref": "GoogleCloudContentwarehouseV1RuleEngineOutput", "description": "Output from Rule Engine recording the rule evaluator and action executor's output. Refer format in: google/cloud/contentwarehouse/v1/rule_engine.proto"}}, "type": "object"}, "GoogleCloudContentwarehouseV1UpdateDocumentSchemaRequest": {"description": "Request message for DocumentSchemaService.UpdateDocumentSchema.", "id": "GoogleCloudContentwarehouseV1UpdateDocumentSchemaRequest", "properties": {"documentSchema": {"$ref": "GoogleCloudContentwarehouseV1DocumentSchema", "description": "Required. The document schema to update with."}}, "type": "object"}, "GoogleCloudContentwarehouseV1UpdateOptions": {"description": "Options for Update operations.", "id": "GoogleCloudContentwarehouseV1UpdateOptions", "properties": {"mergeFieldsOptions": {"$ref": "GoogleCloudContentwarehouseV1MergeFieldsOptions", "description": "Options for merging."}, "updateMask": {"description": "Field mask for merging Document fields. For the `FieldMask` definition, see https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask", "format": "google-fieldmask", "type": "string"}, "updateType": {"description": "Type for update.", "enum": ["UPDATE_TYPE_UNSPECIFIED", "UPDATE_TYPE_REPLACE", "UPDATE_TYPE_MERGE", "UPDATE_TYPE_INSERT_PROPERTIES_BY_NAMES", "UPDATE_TYPE_REPLACE_PROPERTIES_BY_NAMES", "UPDATE_TYPE_DELETE_PROPERTIES_BY_NAMES", "UPDATE_TYPE_MERGE_AND_REPLACE_OR_INSERT_PROPERTIES_BY_NAMES"], "enumDescriptions": ["Defaults to full replace behavior, ie. FULL_REPLACE.", "Fully replace all the fields (including previously linked raw document). Any field masks will be ignored.", "Merge the fields into the existing entities.", "Inserts the properties by names.", "Replace the properties by names.", "Delete the properties by names.", "For each of the property, replaces the property if the it exists, otherwise inserts a new property. And for the rest of the fields, merge them based on update mask and merge fields options."], "type": "string"}}, "type": "object"}, "GoogleCloudContentwarehouseV1UpdateRuleSetRequest": {"description": "Request message for RuleSetService.UpdateRuleSet.", "id": "GoogleCloudContentwarehouseV1UpdateRuleSetRequest", "properties": {"ruleSet": {"$ref": "GoogleCloudContentwarehouseV1RuleSet", "description": "Required. The rule set to update."}}, "type": "object"}, "GoogleCloudContentwarehouseV1UserInfo": {"description": "The user information.", "id": "GoogleCloudContentwarehouseV1UserInfo", "properties": {"groupIds": {"description": "The unique group identifications which the user is belong to. The format is \"group:<EMAIL>\";", "items": {"type": "string"}, "type": "array"}, "id": {"description": "A unique user identification string, as determined by the client. The maximum number of allowed characters is 255. Allowed characters include numbers 0 to 9, uppercase and lowercase letters, and restricted special symbols (:, @, +, -, _, ~) The format is \"user:<EMAIL>\";", "type": "string"}}, "type": "object"}, "GoogleCloudContentwarehouseV1Value": {"description": "`Value` represents a dynamically typed value which can be either be a float, a integer, a string, or a datetime value. A producer of value is expected to set one of these variants. Absence of any variant indicates an error.", "id": "GoogleCloudContentwarehouseV1Value", "properties": {"booleanValue": {"description": "Represents a boolean value.", "type": "boolean"}, "datetimeValue": {"$ref": "GoogleTypeDateTime", "description": "Represents a datetime value."}, "enumValue": {"$ref": "GoogleCloudContentwarehouseV1EnumValue", "description": "Represents an enum value."}, "floatValue": {"description": "Represents a float value.", "format": "float", "type": "number"}, "intValue": {"description": "Represents a integer value.", "format": "int32", "type": "integer"}, "stringValue": {"description": "Represents a string value.", "type": "string"}, "timestampValue": {"$ref": "GoogleCloudContentwarehouseV1TimestampValue", "description": "Represents a timestamp value."}}, "type": "object"}, "GoogleCloudContentwarehouseV1WeightedSchemaProperty": {"description": "Specifies the schema property name.", "id": "GoogleCloudContentwarehouseV1WeightedSchemaProperty", "properties": {"documentSchemaName": {"description": "The document schema name.", "type": "string"}, "propertyNames": {"description": "The property definition names in the schema.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudContentwarehouseV1beta1CreateDocumentMetadata": {"description": "Metadata object for CreateDocument request (currently empty).", "id": "GoogleCloudContentwarehouseV1beta1CreateDocumentMetadata", "properties": {}, "type": "object"}, "GoogleCloudContentwarehouseV1beta1InitializeProjectResponse": {"description": "Response message for projectService.InitializeProject", "id": "GoogleCloudContentwarehouseV1beta1InitializeProjectResponse", "properties": {"message": {"description": "The message of the project initialization process.", "type": "string"}, "state": {"description": "The state of the project initialization process.", "enum": ["STATE_UNSPECIFIED", "SUCCEEDED", "FAILED", "CANCELLED", "RUNNING"], "enumDescriptions": ["Clients should never see this.", "Finished project initialization without error.", "Finished project initialization with an error.", "Client canceled the LRO.", "Ask the customer to check the operation for results."], "type": "string"}}, "type": "object"}, "GoogleCloudContentwarehouseV1beta1UpdateDocumentMetadata": {"description": "Metadata object for UpdateDocument request (currently empty).", "id": "GoogleCloudContentwarehouseV1beta1UpdateDocumentMetadata", "properties": {}, "type": "object"}, "GoogleCloudDocumentaiV1Barcode": {"description": "Encodes the detailed information of a barcode.", "id": "GoogleCloudDocumentaiV1Barcode", "properties": {"format": {"description": "Format of a barcode. The supported formats are: - `CODE_128`: Code 128 type. - `CODE_39`: Code 39 type. - `CODE_93`: Code 93 type. - `CODABAR`: Codabar type. - `DATA_MATRIX`: 2D Data Matrix type. - `ITF`: ITF type. - `EAN_13`: EAN-13 type. - `EAN_8`: EAN-8 type. - `QR_CODE`: 2D QR code type. - `UPC_A`: UPC-A type. - `UPC_E`: UPC-E type. - `PDF417`: PDF417 type. - `AZTEC`: 2D Aztec code type. - `DATABAR`: GS1 DataBar code type.", "type": "string"}, "rawValue": {"description": "Raw value encoded in the barcode. For example: `'MEBKM:TITLE:Google;URL:https://www.google.com;;'`.", "type": "string"}, "valueFormat": {"description": "Value format describes the format of the value that a barcode encodes. The supported formats are: - `CONTACT_INFO`: Contact information. - `EMAIL`: Email address. - `ISBN`: ISBN identifier. - `PHONE`: Phone number. - `PRODUCT`: Product. - `SMS`: SMS message. - `TEXT`: Text string. - `URL`: URL address. - `WIFI`: Wifi information. - `GEO`: Geo-localization. - `CALENDAR_EVENT`: Calendar event. - `DRIVER_LICENSE`: Driver's license.", "type": "string"}}, "type": "object"}, "GoogleCloudDocumentaiV1BoundingPoly": {"description": "A bounding polygon for the detected image annotation.", "id": "GoogleCloudDocumentaiV1BoundingPoly", "properties": {"normalizedVertices": {"description": "The bounding polygon normalized vertices.", "items": {"$ref": "GoogleCloudDocumentaiV1NormalizedVertex"}, "type": "array"}, "vertices": {"description": "The bounding polygon vertices.", "items": {"$ref": "GoogleCloudDocumentaiV1Vertex"}, "type": "array"}}, "type": "object"}, "GoogleCloudDocumentaiV1Document": {"description": "Document represents the canonical document resource in Document AI. It is an interchange format that provides insights into documents and allows for collaboration between users and Document AI to iterate and optimize for quality.", "id": "GoogleCloudDocumentaiV1Document", "properties": {"chunkedDocument": {"$ref": "GoogleCloudDocumentaiV1DocumentChunkedDocument", "description": "Document chunked based on chunking config."}, "content": {"description": "Optional. Inline document content, represented as a stream of bytes. Note: As with all `bytes` fields, protobuffers use a pure binary representation, whereas JSON representations use base64.", "format": "byte", "type": "string"}, "documentLayout": {"$ref": "GoogleCloudDocumentaiV1DocumentDocumentLayout", "description": "Parsed layout of the document."}, "entities": {"description": "A list of entities detected on Document.text. For document shards, entities in this list may cross shard boundaries.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentEntity"}, "type": "array"}, "entityRelations": {"description": "Placeholder. Relationship among Document.entities.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentEntityRelation"}, "type": "array"}, "error": {"$ref": "GoogleRpcStatus", "description": "Any error that occurred while processing this document."}, "mimeType": {"description": "An IANA published [media type (MIME type)](https://www.iana.org/assignments/media-types/media-types.xhtml).", "type": "string"}, "pages": {"description": "Visual page layout for the Document.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentPage"}, "type": "array"}, "revisions": {"description": "Placeholder. Revision history of this document.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentRevision"}, "type": "array"}, "shardInfo": {"$ref": "GoogleCloudDocumentaiV1DocumentShardInfo", "description": "Information about the sharding if this document is sharded part of a larger document. If the document is not sharded, this message is not specified."}, "text": {"description": "Optional. UTF-8 encoded text in reading order from the document.", "type": "string"}, "textChanges": {"description": "Placeholder. A list of text corrections made to Document.text. This is usually used for annotating corrections to OCR mistakes. Text changes for a given revision may not overlap with each other.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentTextChange"}, "type": "array"}, "textStyles": {"deprecated": true, "description": "Styles for the Document.text.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentStyle"}, "type": "array"}, "uri": {"description": "Optional. Currently supports Google Cloud Storage URI of the form `gs://bucket_name/object_name`. Object versioning is not supported. For more information, refer to [Google Cloud Storage Request URIs](https://cloud.google.com/storage/docs/reference-uris).", "type": "string"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentChunkedDocument": {"description": "Represents the chunks that the document is divided into.", "id": "GoogleCloudDocumentaiV1DocumentChunkedDocument", "properties": {"chunks": {"description": "List of chunks.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentChunkedDocumentChunk"}, "type": "array"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentChunkedDocumentChunk": {"description": "Represents a chunk.", "id": "GoogleCloudDocumentaiV1DocumentChunkedDocumentChunk", "properties": {"chunkId": {"description": "ID of the chunk.", "type": "string"}, "content": {"description": "Text content of the chunk.", "type": "string"}, "pageFooters": {"description": "Page footers associated with the chunk.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentChunkedDocumentChunkChunkPageFooter"}, "type": "array"}, "pageHeaders": {"description": "Page headers associated with the chunk.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentChunkedDocumentChunkChunkPageHeader"}, "type": "array"}, "pageSpan": {"$ref": "GoogleCloudDocumentaiV1DocumentChunkedDocumentChunkChunkPageSpan", "description": "Page span of the chunk."}, "sourceBlockIds": {"description": "Unused.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentChunkedDocumentChunkChunkPageFooter": {"description": "Represents the page footer associated with the chunk.", "id": "GoogleCloudDocumentaiV1DocumentChunkedDocumentChunkChunkPageFooter", "properties": {"pageSpan": {"$ref": "GoogleCloudDocumentaiV1DocumentChunkedDocumentChunkChunkPageSpan", "description": "Page span of the footer."}, "text": {"description": "Footer in text format.", "type": "string"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentChunkedDocumentChunkChunkPageHeader": {"description": "Represents the page header associated with the chunk.", "id": "GoogleCloudDocumentaiV1DocumentChunkedDocumentChunkChunkPageHeader", "properties": {"pageSpan": {"$ref": "GoogleCloudDocumentaiV1DocumentChunkedDocumentChunkChunkPageSpan", "description": "Page span of the header."}, "text": {"description": "Header in text format.", "type": "string"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentChunkedDocumentChunkChunkPageSpan": {"description": "Represents where the chunk starts and ends in the document.", "id": "GoogleCloudDocumentaiV1DocumentChunkedDocumentChunkChunkPageSpan", "properties": {"pageEnd": {"description": "Page where chunk ends in the document.", "format": "int32", "type": "integer"}, "pageStart": {"description": "Page where chunk starts in the document.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentDocumentLayout": {"description": "Represents the parsed layout of a document as a collection of blocks that the document is divided into.", "id": "GoogleCloudDocumentaiV1DocumentDocumentLayout", "properties": {"blocks": {"description": "List of blocks in the document.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlock"}, "type": "array"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlock": {"description": "Represents a block. A block could be one of the various types (text, table, list) supported.", "id": "GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlock", "properties": {"blockId": {"description": "ID of the block.", "type": "string"}, "listBlock": {"$ref": "GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutListBlock", "description": "Block consisting of list content/structure."}, "pageSpan": {"$ref": "GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutPageSpan", "description": "Page span of the block."}, "tableBlock": {"$ref": "GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutTableBlock", "description": "Block consisting of table content/structure."}, "textBlock": {"$ref": "GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutTextBlock", "description": "Block consisting of text content."}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutListBlock": {"description": "Represents a list type block.", "id": "GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutListBlock", "properties": {"listEntries": {"description": "List entries that constitute a list block.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutListEntry"}, "type": "array"}, "type": {"description": "Type of the list_entries (if exist). Available options are `ordered` and `unordered`.", "type": "string"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutListEntry": {"description": "Represents an entry in the list.", "id": "GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutListEntry", "properties": {"blocks": {"description": "A list entry is a list of blocks. Repeated blocks support further hierarchies and nested blocks.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlock"}, "type": "array"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutPageSpan": {"description": "Represents where the block starts and ends in the document.", "id": "GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutPageSpan", "properties": {"pageEnd": {"description": "Page where block ends in the document.", "format": "int32", "type": "integer"}, "pageStart": {"description": "Page where block starts in the document.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutTableBlock": {"description": "Represents a table type block.", "id": "GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutTableBlock", "properties": {"bodyRows": {"description": "Body rows containing main table content.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutTableRow"}, "type": "array"}, "caption": {"description": "Table caption/title.", "type": "string"}, "headerRows": {"description": "Header rows at the top of the table.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutTableRow"}, "type": "array"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutTableCell": {"description": "Represents a cell in a table row.", "id": "GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutTableCell", "properties": {"blocks": {"description": "A table cell is a list of blocks. Repeated blocks support further hierarchies and nested blocks.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlock"}, "type": "array"}, "colSpan": {"description": "How many columns this cell spans.", "format": "int32", "type": "integer"}, "rowSpan": {"description": "How many rows this cell spans.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutTableRow": {"description": "Represents a row in a table.", "id": "GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutTableRow", "properties": {"cells": {"description": "A table row is a list of table cells.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutTableCell"}, "type": "array"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutTextBlock": {"description": "Represents a text type block.", "id": "GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlockLayoutTextBlock", "properties": {"blocks": {"description": "A text block could further have child blocks. Repeated blocks support further hierarchies and nested blocks.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentDocumentLayoutDocumentLayoutBlock"}, "type": "array"}, "text": {"description": "Text content stored in the block.", "type": "string"}, "type": {"description": "Type of the text in the block. Available options are: `paragraph`, `subtitle`, `heading-1`, `heading-2`, `heading-3`, `heading-4`, `heading-5`, `header`, `footer`.", "type": "string"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentEntity": {"description": "An entity that could be a phrase in the text or a property that belongs to the document. It is a known entity type, such as a person, an organization, or location.", "id": "GoogleCloudDocumentaiV1DocumentEntity", "properties": {"confidence": {"description": "Optional. Confidence of detected Schema entity. Range `[0, 1]`.", "format": "float", "type": "number"}, "id": {"description": "Optional. Canonical id. This will be a unique value in the entity list for this document.", "type": "string"}, "mentionId": {"description": "Optional. Deprecated. Use `id` field instead.", "type": "string"}, "mentionText": {"description": "Optional. Text value of the entity e.g. `1600 Amphitheatre Pkwy`.", "type": "string"}, "normalizedValue": {"$ref": "GoogleCloudDocumentaiV1DocumentEntityNormalizedValue", "description": "Optional. Normalized entity value. Absent if the extracted value could not be converted or the type (e.g. address) is not supported for certain parsers. This field is also only populated for certain supported document types."}, "pageAnchor": {"$ref": "GoogleCloudDocumentaiV1DocumentPageAnchor", "description": "Optional. Represents the provenance of this entity wrt. the location on the page where it was found."}, "properties": {"description": "Optional. Entities can be nested to form a hierarchical data structure representing the content in the document.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentEntity"}, "type": "array"}, "provenance": {"$ref": "GoogleCloudDocumentaiV1DocumentProvenance", "description": "Optional. The history of this annotation."}, "redacted": {"description": "Optional. Whether the entity will be redacted for de-identification purposes.", "type": "boolean"}, "textAnchor": {"$ref": "GoogleCloudDocumentaiV1DocumentTextAnchor", "description": "Optional. Provenance of the entity. Text anchor indexing into the Document.text."}, "type": {"description": "Required. Entity type from a schema e.g. `Address`.", "type": "string"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentEntityNormalizedValue": {"description": "Parsed and normalized entity value.", "id": "GoogleCloudDocumentaiV1DocumentEntityNormalizedValue", "properties": {"addressValue": {"$ref": "GoogleTypePostalAddress", "description": "Postal address. See also: https://github.com/googleapis/googleapis/blob/master/google/type/postal_address.proto"}, "booleanValue": {"description": "Boolean value. Can be used for entities with binary values, or for checkboxes.", "type": "boolean"}, "dateValue": {"$ref": "GoogleTypeDate", "description": "Date value. Includes year, month, day. See also: https://github.com/googleapis/googleapis/blob/master/google/type/date.proto"}, "datetimeValue": {"$ref": "GoogleTypeDateTime", "description": "DateTime value. Includes date, time, and timezone. See also: https://github.com/googleapis/googleapis/blob/master/google/type/datetime.proto"}, "floatValue": {"description": "Float value.", "format": "float", "type": "number"}, "integerValue": {"description": "Integer value.", "format": "int32", "type": "integer"}, "moneyValue": {"$ref": "GoogleTypeMoney", "description": "Money value. See also: https://github.com/googleapis/googleapis/blob/master/google/type/money.proto"}, "text": {"description": "Optional. An optional field to store a normalized string. For some entity types, one of respective `structured_value` fields may also be populated. Also not all the types of `structured_value` will be normalized. For example, some processors may not generate `float` or `integer` normalized text by default. Below are sample formats mapped to structured values. - Money/Currency type (`money_value`) is in the ISO 4217 text format. - Date type (`date_value`) is in the ISO 8601 text format. - Datetime type (`datetime_value`) is in the ISO 8601 text format.", "type": "string"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentEntityRelation": {"description": "Relationship between Entities.", "id": "GoogleCloudDocumentaiV1DocumentEntityRelation", "properties": {"objectId": {"description": "Object entity id.", "type": "string"}, "relation": {"description": "Relationship description.", "type": "string"}, "subjectId": {"description": "Subject entity id.", "type": "string"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentPage": {"description": "A page in a Document.", "id": "GoogleCloudDocumentaiV1DocumentPage", "properties": {"blocks": {"description": "A list of visually detected text blocks on the page. A block has a set of lines (collected into paragraphs) that have a common line-spacing and orientation.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentPageBlock"}, "type": "array"}, "detectedBarcodes": {"description": "A list of detected barcodes.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentPageDetectedBarcode"}, "type": "array"}, "detectedLanguages": {"description": "A list of detected languages together with confidence.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentPageDetectedLanguage"}, "type": "array"}, "dimension": {"$ref": "GoogleCloudDocumentaiV1DocumentPageDimension", "description": "Physical dimension of the page."}, "formFields": {"description": "A list of visually detected form fields on the page.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentPageFormField"}, "type": "array"}, "image": {"$ref": "GoogleCloudDocumentaiV1DocumentPageImage", "description": "Rendered image for this page. This image is preprocessed to remove any skew, rotation, and distortions such that the annotation bounding boxes can be upright and axis-aligned."}, "imageQualityScores": {"$ref": "GoogleCloudDocumentaiV1DocumentPageImageQualityScores", "description": "Image quality scores."}, "layout": {"$ref": "GoogleCloudDocumentaiV1DocumentPageLayout", "description": "Layout for the page."}, "lines": {"description": "A list of visually detected text lines on the page. A collection of tokens that a human would perceive as a line.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentPageLine"}, "type": "array"}, "pageNumber": {"description": "1-based index for current Page in a parent Document. Useful when a page is taken out of a Document for individual processing.", "format": "int32", "type": "integer"}, "paragraphs": {"description": "A list of visually detected text paragraphs on the page. A collection of lines that a human would perceive as a paragraph.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentPageParagraph"}, "type": "array"}, "provenance": {"$ref": "GoogleCloudDocumentaiV1DocumentProvenance", "deprecated": true, "description": "The history of this page."}, "symbols": {"description": "A list of visually detected symbols on the page.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentPageSymbol"}, "type": "array"}, "tables": {"description": "A list of visually detected tables on the page.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentPageTable"}, "type": "array"}, "tokens": {"description": "A list of visually detected tokens on the page.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentPageToken"}, "type": "array"}, "transforms": {"description": "Transformation matrices that were applied to the original document image to produce Page.image.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentPageMatrix"}, "type": "array"}, "visualElements": {"description": "A list of detected non-text visual elements e.g. checkbox, signature etc. on the page.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentPageVisualElement"}, "type": "array"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentPageAnchor": {"description": "Referencing the visual context of the entity in the Document.pages. Page anchors can be cross-page, consist of multiple bounding polygons and optionally reference specific layout element types.", "id": "GoogleCloudDocumentaiV1DocumentPageAnchor", "properties": {"pageRefs": {"description": "One or more references to visual page elements", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentPageAnchorPageRef"}, "type": "array"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentPageAnchorPageRef": {"description": "Represents a weak reference to a page element within a document.", "id": "GoogleCloudDocumentaiV1DocumentPageAnchorPageRef", "properties": {"boundingPoly": {"$ref": "GoogleCloudDocumentaiV1BoundingPoly", "description": "Optional. Identifies the bounding polygon of a layout element on the page. If `layout_type` is set, the bounding polygon must be exactly the same to the layout element it's referring to."}, "confidence": {"description": "Optional. Confidence of detected page element, if applicable. Range `[0, 1]`.", "format": "float", "type": "number"}, "layoutId": {"deprecated": true, "description": "Optional. Deprecated. Use PageRef.bounding_poly instead.", "type": "string"}, "layoutType": {"description": "Optional. The type of the layout element that is being referenced if any.", "enum": ["LAYOUT_TYPE_UNSPECIFIED", "BLOCK", "PARAGRAPH", "LINE", "TOKEN", "VISUAL_ELEMENT", "TABLE", "FORM_FIELD"], "enumDescriptions": ["Layout Unspecified.", "References a Page.blocks element.", "References a Page.paragraphs element.", "References a Page.lines element.", "References a Page.tokens element.", "References a Page.visual_elements element.", "Refrrences a Page.tables element.", "References a Page.form_fields element."], "type": "string"}, "page": {"description": "Required. Index into the Document.pages element, for example using `Document.pages` to locate the related page element. This field is skipped when its value is the default `0`. See https://developers.google.com/protocol-buffers/docs/proto3#json.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentPageBlock": {"description": "A block has a set of lines (collected into paragraphs) that have a common line-spacing and orientation.", "id": "GoogleCloudDocumentaiV1DocumentPageBlock", "properties": {"detectedLanguages": {"description": "A list of detected languages together with confidence.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentPageDetectedLanguage"}, "type": "array"}, "layout": {"$ref": "GoogleCloudDocumentaiV1DocumentPageLayout", "description": "Layout for Block."}, "provenance": {"$ref": "GoogleCloudDocumentaiV1DocumentProvenance", "deprecated": true, "description": "The history of this annotation."}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentPageDetectedBarcode": {"description": "A detected barcode.", "id": "GoogleCloudDocumentaiV1DocumentPageDetectedBarcode", "properties": {"barcode": {"$ref": "GoogleCloudDocumentaiV1Barcode", "description": "Detailed barcode information of the DetectedBarcode."}, "layout": {"$ref": "GoogleCloudDocumentaiV1DocumentPageLayout", "description": "Layout for DetectedBarcode."}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentPageDetectedLanguage": {"description": "Detected language for a structural component.", "id": "GoogleCloudDocumentaiV1DocumentPageDetectedLanguage", "properties": {"confidence": {"description": "Confidence of detected language. Range `[0, 1]`.", "format": "float", "type": "number"}, "languageCode": {"description": "The [BCP-47 language code](https://www.unicode.org/reports/tr35/#Unicode_locale_identifier), such as `en-US` or `sr-Latn`.", "type": "string"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentPageDimension": {"description": "Dimension for the page.", "id": "GoogleCloudDocumentaiV1DocumentPageDimension", "properties": {"height": {"description": "Page height.", "format": "float", "type": "number"}, "unit": {"description": "Dimension unit.", "type": "string"}, "width": {"description": "Page width.", "format": "float", "type": "number"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentPageFormField": {"description": "A form field detected on the page.", "id": "GoogleCloudDocumentaiV1DocumentPageFormField", "properties": {"correctedKeyText": {"description": "Created for Labeling UI to export key text. If corrections were made to the text identified by the `field_name.text_anchor`, this field will contain the correction.", "type": "string"}, "correctedValueText": {"description": "Created for Labeling UI to export value text. If corrections were made to the text identified by the `field_value.text_anchor`, this field will contain the correction.", "type": "string"}, "fieldName": {"$ref": "GoogleCloudDocumentaiV1DocumentPageLayout", "description": "Layout for the FormField name. e.g. `Address`, `Email`, `Grand total`, `Phone number`, etc."}, "fieldValue": {"$ref": "GoogleCloudDocumentaiV1DocumentPageLayout", "description": "Layout for the FormField value."}, "nameDetectedLanguages": {"description": "A list of detected languages for name together with confidence.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentPageDetectedLanguage"}, "type": "array"}, "provenance": {"$ref": "GoogleCloudDocumentaiV1DocumentProvenance", "description": "The history of this annotation."}, "valueDetectedLanguages": {"description": "A list of detected languages for value together with confidence.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentPageDetectedLanguage"}, "type": "array"}, "valueType": {"description": "If the value is non-textual, this field represents the type. Current valid values are: - blank (this indicates the `field_value` is normal text) - `unfilled_checkbox` - `filled_checkbox`", "type": "string"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentPageImage": {"description": "Rendered image contents for this page.", "id": "GoogleCloudDocumentaiV1DocumentPageImage", "properties": {"content": {"description": "Raw byte content of the image.", "format": "byte", "type": "string"}, "height": {"description": "Height of the image in pixels.", "format": "int32", "type": "integer"}, "mimeType": {"description": "Encoding [media type (MIME type)](https://www.iana.org/assignments/media-types/media-types.xhtml) for the image.", "type": "string"}, "width": {"description": "Width of the image in pixels.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentPageImageQualityScores": {"description": "Image quality scores for the page image.", "id": "GoogleCloudDocumentaiV1DocumentPageImageQualityScores", "properties": {"detectedDefects": {"description": "A list of detected defects.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentPageImageQualityScoresDetectedDefect"}, "type": "array"}, "qualityScore": {"description": "The overall quality score. Range `[0, 1]` where `1` is perfect quality.", "format": "float", "type": "number"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentPageImageQualityScoresDetectedDefect": {"description": "Image Quality Defects", "id": "GoogleCloudDocumentaiV1DocumentPageImageQualityScoresDetectedDefect", "properties": {"confidence": {"description": "Confidence of detected defect. Range `[0, 1]` where `1` indicates strong confidence that the defect exists.", "format": "float", "type": "number"}, "type": {"description": "Name of the defect type. Supported values are: - `quality/defect_blurry` - `quality/defect_noisy` - `quality/defect_dark` - `quality/defect_faint` - `quality/defect_text_too_small` - `quality/defect_document_cutoff` - `quality/defect_text_cutoff` - `quality/defect_glare`", "type": "string"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentPageLayout": {"description": "Visual element describing a layout unit on a page.", "id": "GoogleCloudDocumentaiV1DocumentPageLayout", "properties": {"boundingPoly": {"$ref": "GoogleCloudDocumentaiV1BoundingPoly", "description": "The bounding polygon for the Layout."}, "confidence": {"description": "Confidence of the current Layout within context of the object this layout is for. e.g. confidence can be for a single token, a table, a visual element, etc. depending on context. Range `[0, 1]`.", "format": "float", "type": "number"}, "orientation": {"description": "Detected orientation for the Layout.", "enum": ["ORIENTATION_UNSPECIFIED", "PAGE_UP", "PAGE_RIGHT", "PAGE_DOWN", "PAGE_LEFT"], "enumDescriptions": ["Unspecified orientation.", "Orientation is aligned with page up.", "Orientation is aligned with page right. Turn the head 90 degrees clockwise from upright to read.", "Orientation is aligned with page down. Turn the head 180 degrees from upright to read.", "Orientation is aligned with page left. Turn the head 90 degrees counterclockwise from upright to read."], "type": "string"}, "textAnchor": {"$ref": "GoogleCloudDocumentaiV1DocumentTextAnchor", "description": "Text anchor indexing into the Document.text."}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentPageLine": {"description": "A collection of tokens that a human would perceive as a line. Does not cross column boundaries, can be horizontal, vertical, etc.", "id": "GoogleCloudDocumentaiV1DocumentPageLine", "properties": {"detectedLanguages": {"description": "A list of detected languages together with confidence.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentPageDetectedLanguage"}, "type": "array"}, "layout": {"$ref": "GoogleCloudDocumentaiV1DocumentPageLayout", "description": "Layout for Line."}, "provenance": {"$ref": "GoogleCloudDocumentaiV1DocumentProvenance", "deprecated": true, "description": "The history of this annotation."}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentPageMatrix": {"description": "Representation for transformation matrix, intended to be compatible and used with OpenCV format for image manipulation.", "id": "GoogleCloudDocumentaiV1DocumentPageMatrix", "properties": {"cols": {"description": "Number of columns in the matrix.", "format": "int32", "type": "integer"}, "data": {"description": "The matrix data.", "format": "byte", "type": "string"}, "rows": {"description": "Number of rows in the matrix.", "format": "int32", "type": "integer"}, "type": {"description": "This encodes information about what data type the matrix uses. For example, 0 (CV_8U) is an unsigned 8-bit image. For the full list of OpenCV primitive data types, please refer to https://docs.opencv.org/4.3.0/d1/d1b/group__core__hal__interface.html", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentPageParagraph": {"description": "A collection of lines that a human would perceive as a paragraph.", "id": "GoogleCloudDocumentaiV1DocumentPageParagraph", "properties": {"detectedLanguages": {"description": "A list of detected languages together with confidence.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentPageDetectedLanguage"}, "type": "array"}, "layout": {"$ref": "GoogleCloudDocumentaiV1DocumentPageLayout", "description": "Layout for Paragraph."}, "provenance": {"$ref": "GoogleCloudDocumentaiV1DocumentProvenance", "deprecated": true, "description": "The history of this annotation."}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentPageSymbol": {"description": "A detected symbol.", "id": "GoogleCloudDocumentaiV1DocumentPageSymbol", "properties": {"detectedLanguages": {"description": "A list of detected languages together with confidence.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentPageDetectedLanguage"}, "type": "array"}, "layout": {"$ref": "GoogleCloudDocumentaiV1DocumentPageLayout", "description": "Layout for Symbol."}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentPageTable": {"description": "A table representation similar to HTML table structure.", "id": "GoogleCloudDocumentaiV1DocumentPageTable", "properties": {"bodyRows": {"description": "Body rows of the table.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentPageTableTableRow"}, "type": "array"}, "detectedLanguages": {"description": "A list of detected languages together with confidence.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentPageDetectedLanguage"}, "type": "array"}, "headerRows": {"description": "Header rows of the table.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentPageTableTableRow"}, "type": "array"}, "layout": {"$ref": "GoogleCloudDocumentaiV1DocumentPageLayout", "description": "Layout for Table."}, "provenance": {"$ref": "GoogleCloudDocumentaiV1DocumentProvenance", "deprecated": true, "description": "The history of this table."}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentPageTableTableCell": {"description": "A cell representation inside the table.", "id": "GoogleCloudDocumentaiV1DocumentPageTableTableCell", "properties": {"colSpan": {"description": "How many columns this cell spans.", "format": "int32", "type": "integer"}, "detectedLanguages": {"description": "A list of detected languages together with confidence.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentPageDetectedLanguage"}, "type": "array"}, "layout": {"$ref": "GoogleCloudDocumentaiV1DocumentPageLayout", "description": "Layout for TableCell."}, "rowSpan": {"description": "How many rows this cell spans.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentPageTableTableRow": {"description": "A row of table cells.", "id": "GoogleCloudDocumentaiV1DocumentPageTableTableRow", "properties": {"cells": {"description": "Cells that make up this row.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentPageTableTableCell"}, "type": "array"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentPageToken": {"description": "A detected token.", "id": "GoogleCloudDocumentaiV1DocumentPageToken", "properties": {"detectedBreak": {"$ref": "GoogleCloudDocumentaiV1DocumentPageTokenDetectedBreak", "description": "Detected break at the end of a Token."}, "detectedLanguages": {"description": "A list of detected languages together with confidence.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentPageDetectedLanguage"}, "type": "array"}, "layout": {"$ref": "GoogleCloudDocumentaiV1DocumentPageLayout", "description": "Layout for Token."}, "provenance": {"$ref": "GoogleCloudDocumentaiV1DocumentProvenance", "deprecated": true, "description": "The history of this annotation."}, "styleInfo": {"$ref": "GoogleCloudDocumentaiV1DocumentPageTokenStyleInfo", "description": "Text style attributes."}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentPageTokenDetectedBreak": {"description": "Detected break at the end of a Token.", "id": "GoogleCloudDocumentaiV1DocumentPageTokenDetectedBreak", "properties": {"type": {"description": "Detected break type.", "enum": ["TYPE_UNSPECIFIED", "SPACE", "WIDE_SPACE", "HYPHEN"], "enumDescriptions": ["Unspecified break type.", "A single whitespace.", "A wider whitespace.", "A hyphen that indicates that a token has been split across lines."], "type": "string"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentPageTokenStyleInfo": {"description": "Font and other text style attributes.", "id": "GoogleCloudDocumentaiV1DocumentPageTokenStyleInfo", "properties": {"backgroundColor": {"$ref": "GoogleTypeColor", "description": "Color of the background."}, "bold": {"description": "Whether the text is bold (equivalent to font_weight is at least `700`).", "type": "boolean"}, "fontSize": {"description": "Font size in points (`1` point is `¹⁄₇₂` inches).", "format": "int32", "type": "integer"}, "fontType": {"description": "Name or style of the font.", "type": "string"}, "fontWeight": {"description": "TrueType weight on a scale `100` (thin) to `1000` (ultra-heavy). Normal is `400`, bold is `700`.", "format": "int32", "type": "integer"}, "handwritten": {"description": "Whether the text is handwritten.", "type": "boolean"}, "italic": {"description": "Whether the text is italic.", "type": "boolean"}, "letterSpacing": {"description": "Letter spacing in points.", "format": "double", "type": "number"}, "pixelFontSize": {"description": "Font size in pixels, equal to _unrounded font_size_ * _resolution_ ÷ `72.0`.", "format": "double", "type": "number"}, "smallcaps": {"description": "Whether the text is in small caps. This feature is not supported yet.", "type": "boolean"}, "strikeout": {"description": "Whether the text is strikethrough. This feature is not supported yet.", "type": "boolean"}, "subscript": {"description": "Whether the text is a subscript. This feature is not supported yet.", "type": "boolean"}, "superscript": {"description": "Whether the text is a superscript. This feature is not supported yet.", "type": "boolean"}, "textColor": {"$ref": "GoogleTypeColor", "description": "Color of the text."}, "underlined": {"description": "Whether the text is underlined.", "type": "boolean"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentPageVisualElement": {"description": "Detected non-text visual elements e.g. checkbox, signature etc. on the page.", "id": "GoogleCloudDocumentaiV1DocumentPageVisualElement", "properties": {"detectedLanguages": {"description": "A list of detected languages together with confidence.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentPageDetectedLanguage"}, "type": "array"}, "layout": {"$ref": "GoogleCloudDocumentaiV1DocumentPageLayout", "description": "Layout for VisualElement."}, "type": {"description": "Type of the VisualElement.", "type": "string"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentProvenance": {"description": "Structure to identify provenance relationships between annotations in different revisions.", "id": "GoogleCloudDocumentaiV1DocumentProvenance", "properties": {"id": {"deprecated": true, "description": "The Id of this operation. Needs to be unique within the scope of the revision.", "format": "int32", "type": "integer"}, "parents": {"description": "References to the original elements that are replaced.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentProvenanceParent"}, "type": "array"}, "revision": {"deprecated": true, "description": "The index of the revision that produced this element.", "format": "int32", "type": "integer"}, "type": {"description": "The type of provenance operation.", "enum": ["OPERATION_TYPE_UNSPECIFIED", "ADD", "REMOVE", "UPDATE", "REPLACE", "EVAL_REQUESTED", "EVAL_APPROVED", "EVAL_SKIPPED"], "enumDeprecated": [false, false, false, false, false, true, true, true], "enumDescriptions": ["Operation type unspecified. If no operation is specified a provenance entry is simply used to match against a `parent`.", "Add an element.", "Remove an element identified by `parent`.", "Updates any fields within the given provenance scope of the message. It overwrites the fields rather than replacing them. Use this when you want to update a field value of an entity without also updating all the child properties.", "Currently unused. Replace an element identified by `parent`.", "Deprecated. Request human review for the element identified by `parent`.", "Deprecated. Element is reviewed and approved at human review, confidence will be set to 1.0.", "Deprecated. Element is skipped in the validation process."], "type": "string"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentProvenanceParent": {"description": "The parent element the current element is based on. Used for referencing/aligning, removal and replacement operations.", "id": "GoogleCloudDocumentaiV1DocumentProvenanceParent", "properties": {"id": {"deprecated": true, "description": "The id of the parent provenance.", "format": "int32", "type": "integer"}, "index": {"description": "The index of the parent item in the corresponding item list (eg. list of entities, properties within entities, etc.) in the parent revision.", "format": "int32", "type": "integer"}, "revision": {"description": "The index of the index into current revision's parent_ids list.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentRevision": {"description": "Contains past or forward revisions of this document.", "id": "GoogleCloudDocumentaiV1DocumentRevision", "properties": {"agent": {"description": "If the change was made by a person specify the name or id of that person.", "type": "string"}, "createTime": {"description": "The time that the revision was created, internally generated by doc proto storage at the time of create.", "format": "google-datetime", "type": "string"}, "humanReview": {"$ref": "GoogleCloudDocumentaiV1DocumentRevisionHumanReview", "description": "Human Review information of this revision."}, "id": {"description": "Id of the revision, internally generated by doc proto storage. Unique within the context of the document.", "type": "string"}, "parent": {"deprecated": true, "description": "The revisions that this revision is based on. This can include one or more parent (when documents are merged.) This field represents the index into the `revisions` field.", "items": {"format": "int32", "type": "integer"}, "type": "array"}, "parentIds": {"description": "The revisions that this revision is based on. Must include all the ids that have anything to do with this revision - eg. there are `provenance.parent.revision` fields that index into this field.", "items": {"type": "string"}, "type": "array"}, "processor": {"description": "If the annotation was made by processor identify the processor by its resource name.", "type": "string"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentRevisionHumanReview": {"description": "Human Review information of the document.", "id": "GoogleCloudDocumentaiV1DocumentRevisionHumanReview", "properties": {"state": {"description": "Human review state. e.g. `requested`, `succeeded`, `rejected`.", "type": "string"}, "stateMessage": {"description": "A message providing more details about the current state of processing. For example, the rejection reason when the state is `rejected`.", "type": "string"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentShardInfo": {"description": "For a large document, sharding may be performed to produce several document shards. Each document shard contains this field to detail which shard it is.", "id": "GoogleCloudDocumentaiV1DocumentShardInfo", "properties": {"shardCount": {"description": "Total number of shards.", "format": "int64", "type": "string"}, "shardIndex": {"description": "The 0-based index of this shard.", "format": "int64", "type": "string"}, "textOffset": {"description": "The index of the first character in Document.text in the overall document global text.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentStyle": {"description": "Annotation for common text style attributes. This adheres to CSS conventions as much as possible.", "id": "GoogleCloudDocumentaiV1DocumentStyle", "properties": {"backgroundColor": {"$ref": "GoogleTypeColor", "description": "Text background color."}, "color": {"$ref": "GoogleTypeColor", "description": "Text color."}, "fontFamily": {"description": "Font family such as `<PERSON><PERSON>`, `Times New Roman`. https://www.w3schools.com/cssref/pr_font_font-family.asp", "type": "string"}, "fontSize": {"$ref": "GoogleCloudDocumentaiV1DocumentStyleFontSize", "description": "Font size."}, "fontWeight": {"description": "[Font weight](https://www.w3schools.com/cssref/pr_font_weight.asp). Possible values are `normal`, `bold`, `bolder`, and `lighter`.", "type": "string"}, "textAnchor": {"$ref": "GoogleCloudDocumentaiV1DocumentTextAnchor", "description": "Text anchor indexing into the Document.text."}, "textDecoration": {"description": "[Text decoration](https://www.w3schools.com/cssref/pr_text_text-decoration.asp). Follows CSS standard. ", "type": "string"}, "textStyle": {"description": "[Text style](https://www.w3schools.com/cssref/pr_font_font-style.asp). Possible values are `normal`, `italic`, and `oblique`.", "type": "string"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentStyleFontSize": {"description": "Font size with unit.", "id": "GoogleCloudDocumentaiV1DocumentStyleFontSize", "properties": {"size": {"description": "Font size for the text.", "format": "float", "type": "number"}, "unit": {"description": "Unit for the font size. Follows CSS naming (such as `in`, `px`, and `pt`).", "type": "string"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentTextAnchor": {"description": "Text reference indexing into the Document.text.", "id": "GoogleCloudDocumentaiV1DocumentTextAnchor", "properties": {"content": {"description": "Contains the content of the text span so that users do not have to look it up in the text_segments. It is always populated for formFields.", "type": "string"}, "textSegments": {"description": "The text segments from the Document.text.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentTextAnchorTextSegment"}, "type": "array"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentTextAnchorTextSegment": {"description": "A text segment in the Document.text. The indices may be out of bounds which indicate that the text extends into another document shard for large sharded documents. See ShardInfo.text_offset", "id": "GoogleCloudDocumentaiV1DocumentTextAnchorTextSegment", "properties": {"endIndex": {"description": "TextSegment half open end UTF-8 char index in the Document.text.", "format": "int64", "type": "string"}, "startIndex": {"description": "TextSegment start UTF-8 char index in the Document.text.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudDocumentaiV1DocumentTextChange": {"description": "This message is used for text changes aka. OCR corrections.", "id": "GoogleCloudDocumentaiV1DocumentTextChange", "properties": {"changedText": {"description": "The text that replaces the text identified in the `text_anchor`.", "type": "string"}, "provenance": {"deprecated": true, "description": "The history of this annotation.", "items": {"$ref": "GoogleCloudDocumentaiV1DocumentProvenance"}, "type": "array"}, "textAnchor": {"$ref": "GoogleCloudDocumentaiV1DocumentTextAnchor", "description": "Provenance of the correction. Text anchor indexing into the Document.text. There can only be a single `TextAnchor.text_segments` element. If the start and end index of the text segment are the same, the text change is inserted before that index."}}, "type": "object"}, "GoogleCloudDocumentaiV1NormalizedVertex": {"description": "A vertex represents a 2D point in the image. NOTE: the normalized vertex coordinates are relative to the original image and range from 0 to 1.", "id": "GoogleCloudDocumentaiV1NormalizedVertex", "properties": {"x": {"description": "X coordinate.", "format": "float", "type": "number"}, "y": {"description": "Y coordinate (starts from the top of the image).", "format": "float", "type": "number"}}, "type": "object"}, "GoogleCloudDocumentaiV1Vertex": {"description": "A vertex represents a 2D point in the image. NOTE: the vertex coordinates are in the same scale as the original image.", "id": "GoogleCloudDocumentaiV1Vertex", "properties": {"x": {"description": "X coordinate.", "format": "int32", "type": "integer"}, "y": {"description": "Y coordinate (starts from the top of the image).", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleIamV1AuditConfig": {"description": "Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { \"audit_configs\": [ { \"service\": \"allServices\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" }, { \"log_type\": \"ADMIN_READ\" } ] }, { \"service\": \"sampleservice.googleapis.com\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\" }, { \"log_type\": \"DATA_WRITE\", \"exempted_members\": [ \"user:<EMAIL>\" ] } ] } ] } For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.", "id": "GoogleIamV1AuditConfig", "properties": {"auditLogConfigs": {"description": "The configuration for logging of each type of permission.", "items": {"$ref": "GoogleIamV1AuditLogConfig"}, "type": "array"}, "service": {"description": "Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.", "type": "string"}}, "type": "object"}, "GoogleIamV1AuditLogConfig": {"description": "Provides the configuration for logging a type of permissions. Example: { \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" } ] } This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.", "id": "GoogleIamV1AuditLogConfig", "properties": {"exemptedMembers": {"description": "Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.", "items": {"type": "string"}, "type": "array"}, "logType": {"description": "The log type that this config enables.", "enum": ["LOG_TYPE_UNSPECIFIED", "ADMIN_READ", "DATA_WRITE", "DATA_READ"], "enumDescriptions": ["Default case. Should never be this.", "Admin reads. Example: CloudIAM getIamPolicy", "Data writes. Example: CloudSQL Users create", "Data reads. Example: CloudSQL Users list"], "type": "string"}}, "type": "object"}, "GoogleIamV1Binding": {"description": "Associates `members`, or principals, with a `role`.", "id": "GoogleIamV1Binding", "properties": {"condition": {"$ref": "GoogleTypeExpr", "description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies)."}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/*`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/group/{group_id}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/*`: All identities in a workload identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).", "type": "string"}}, "type": "object"}, "GoogleIamV1Policy": {"description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).", "id": "GoogleIamV1Policy", "properties": {"auditConfigs": {"description": "Specifies cloud audit logging configuration for this policy.", "items": {"$ref": "GoogleIamV1AuditConfig"}, "type": "array"}, "bindings": {"description": "Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.", "items": {"$ref": "GoogleIamV1Binding"}, "type": "array"}, "etag": {"description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.", "format": "byte", "type": "string"}, "version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleLongrunningOperation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "GoogleLongrunningOperation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "GoogleRpcStatus", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "GoogleProtobufEmpty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "GoogleProtobufEmpty", "properties": {}, "type": "object"}, "GoogleRpcStatus": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "GoogleRpcStatus", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "GoogleTypeColor": {"description": "Represents a color in the RGBA color space. This representation is designed for simplicity of conversion to and from color representations in various languages over compactness. For example, the fields of this representation can be trivially provided to the constructor of `java.awt.Color` in Java; it can also be trivially provided to UIColor's `+colorWithRed:green:blue:alpha` method in iOS; and, with just a little work, it can be easily formatted into a CSS `rgba()` string in JavaScript. This reference page doesn't have information about the absolute color space that should be used to interpret the RGB value—for example, sRGB, Adobe RGB, DCI-P3, and BT.2020. By default, applications should assume the sRGB color space. When color equality needs to be decided, implementations, unless documented otherwise, treat two colors as equal if all their red, green, blue, and alpha values each differ by at most `1e-5`. Example (Java): import com.google.type.Color; // ... public static java.awt.Color fromProto(Color protocolor) { float alpha = protocolor.hasAlpha() ? protocolor.getAlpha().getValue() : 1.0; return new java.awt.Color( protocolor.getRed(), protocolor.getGreen(), protocolor.getBlue(), alpha); } public static Color toProto(java.awt.Color color) { float red = (float) color.getRed(); float green = (float) color.getGreen(); float blue = (float) color.getBlue(); float denominator = 255.0; Color.Builder resultBuilder = Color .newBuilder() .setRed(red / denominator) .setGreen(green / denominator) .setBlue(blue / denominator); int alpha = color.getAlpha(); if (alpha != 255) { result.setAlpha( FloatValue .newBuilder() .setValue(((float) alpha) / denominator) .build()); } return resultBuilder.build(); } // ... Example (iOS / Obj-C): // ... static UIColor* fromProto(Color* protocolor) { float red = [protocolor red]; float green = [protocolor green]; float blue = [protocolor blue]; FloatValue* alpha_wrapper = [protocolor alpha]; float alpha = 1.0; if (alpha_wrapper != nil) { alpha = [alpha_wrapper value]; } return [UIColor colorWithRed:red green:green blue:blue alpha:alpha]; } static Color* toProto(UIColor* color) { CGFloat red, green, blue, alpha; if (![color getRed:&red green:&green blue:&blue alpha:&alpha]) { return nil; } Color* result = [[Color alloc] init]; [result setRed:red]; [result setGreen:green]; [result setBlue:blue]; if (alpha <= 0.9999) { [result setAlpha:floatWrapperWithValue(alpha)]; } [result autorelease]; return result; } // ... Example (JavaScript): // ... var protoToCssColor = function(rgb_color) { var redFrac = rgb_color.red || 0.0; var greenFrac = rgb_color.green || 0.0; var blueFrac = rgb_color.blue || 0.0; var red = Math.floor(redFrac * 255); var green = Math.floor(greenFrac * 255); var blue = Math.floor(blueFrac * 255); if (!('alpha' in rgb_color)) { return rgbToCssColor(red, green, blue); } var alphaFrac = rgb_color.alpha.value || 0.0; var rgbParams = [red, green, blue].join(','); return ['rgba(', rgbParams, ',', alphaFrac, ')'].join(''); }; var rgbToCssColor = function(red, green, blue) { var rgbNumber = new Number((red << 16) | (green << 8) | blue); var hexString = rgbNumber.toString(16); var missingZeros = 6 - hexString.length; var resultBuilder = ['#']; for (var i = 0; i < missingZeros; i++) { resultBuilder.push('0'); } resultBuilder.push(hexString); return resultBuilder.join(''); }; // ...", "id": "GoogleTypeColor", "properties": {"alpha": {"description": "The fraction of this color that should be applied to the pixel. That is, the final pixel color is defined by the equation: `pixel color = alpha * (this color) + (1.0 - alpha) * (background color)` This means that a value of 1.0 corresponds to a solid color, whereas a value of 0.0 corresponds to a completely transparent color. This uses a wrapper message rather than a simple float scalar so that it is possible to distinguish between a default value and the value being unset. If omitted, this color object is rendered as a solid color (as if the alpha value had been explicitly given a value of 1.0).", "format": "float", "type": "number"}, "blue": {"description": "The amount of blue in the color as a value in the interval [0, 1].", "format": "float", "type": "number"}, "green": {"description": "The amount of green in the color as a value in the interval [0, 1].", "format": "float", "type": "number"}, "red": {"description": "The amount of red in the color as a value in the interval [0, 1].", "format": "float", "type": "number"}}, "type": "object"}, "GoogleTypeDate": {"description": "Represents a whole or partial calendar date, such as a birthday. The time of day and time zone are either specified elsewhere or are insignificant. The date is relative to the Gregorian Calendar. This can represent one of the following: * A full date, with non-zero year, month, and day values. * A month and day, with a zero year (for example, an anniversary). * A year on its own, with a zero month and a zero day. * A year and month, with a zero day (for example, a credit card expiration date). Related types: * google.type.TimeOfDay * google.type.DateTime * google.protobuf.Timestamp", "id": "GoogleTypeDate", "properties": {"day": {"description": "Day of a month. Must be from 1 to 31 and valid for the year and month, or 0 to specify a year by itself or a year and month where the day isn't significant.", "format": "int32", "type": "integer"}, "month": {"description": "Month of a year. Must be from 1 to 12, or 0 to specify a year without a month and day.", "format": "int32", "type": "integer"}, "year": {"description": "Year of the date. Must be from 1 to 9999, or 0 to specify a date without a year.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleTypeDateTime": {"description": "Represents civil time (or occasionally physical time). This type can represent a civil time in one of a few possible ways: * When utc_offset is set and time_zone is unset: a civil time on a calendar day with a particular offset from UTC. * When time_zone is set and utc_offset is unset: a civil time on a calendar day in a particular time zone. * When neither time_zone nor utc_offset is set: a civil time on a calendar day in local time. The date is relative to the Proleptic Gregorian Calendar. If year, month, or day are 0, the DateTime is considered not to have a specific year, month, or day respectively. This type may also be used to represent a physical time if all the date and time fields are set and either case of the `time_offset` oneof is set. Consider using `Timestamp` message for physical time instead. If your use case also would like to store the user's timezone, that can be done in another field. This type is more flexible than some applications may want. Make sure to document and validate your application's limitations.", "id": "GoogleTypeDateTime", "properties": {"day": {"description": "Optional. Day of month. Must be from 1 to 31 and valid for the year and month, or 0 if specifying a datetime without a day.", "format": "int32", "type": "integer"}, "hours": {"description": "Optional. Hours of day in 24 hour format. Should be from 0 to 23, defaults to 0 (midnight). An API may choose to allow the value \"24:00:00\" for scenarios like business closing time.", "format": "int32", "type": "integer"}, "minutes": {"description": "Optional. Minutes of hour of day. Must be from 0 to 59, defaults to 0.", "format": "int32", "type": "integer"}, "month": {"description": "Optional. Month of year. Must be from 1 to 12, or 0 if specifying a datetime without a month.", "format": "int32", "type": "integer"}, "nanos": {"description": "Optional. Fractions of seconds in nanoseconds. Must be from 0 to 999,999,999, defaults to 0.", "format": "int32", "type": "integer"}, "seconds": {"description": "Optional. Seconds of minutes of the time. Must normally be from 0 to 59, defaults to 0. An API may allow the value 60 if it allows leap-seconds.", "format": "int32", "type": "integer"}, "timeZone": {"$ref": "GoogleTypeTimeZone", "description": "Time zone."}, "utcOffset": {"description": "UTC offset. Must be whole seconds, between -18 hours and +18 hours. For example, a UTC offset of -4:00 would be represented as { seconds: -14400 }.", "format": "google-duration", "type": "string"}, "year": {"description": "Optional. Year of date. Must be from 1 to 9999, or 0 if specifying a datetime without a year.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleTypeExpr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "GoogleTypeExpr", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}}, "type": "object"}, "GoogleTypeInterval": {"description": "Represents a time interval, encoded as a Timestamp start (inclusive) and a Timestamp end (exclusive). The start must be less than or equal to the end. When the start equals the end, the interval is empty (matches no time). When both start and end are unspecified, the interval matches any time.", "id": "GoogleTypeInterval", "properties": {"endTime": {"description": "Optional. Exclusive end of the interval. If specified, a Timestamp matching this interval will have to be before the end.", "format": "google-datetime", "type": "string"}, "startTime": {"description": "Optional. Inclusive start of the interval. If specified, a Timestamp matching this interval will have to be the same or after the start.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleTypeMoney": {"description": "Represents an amount of money with its currency type.", "id": "GoogleTypeMoney", "properties": {"currencyCode": {"description": "The three-letter currency code defined in ISO 4217.", "type": "string"}, "nanos": {"description": "Number of nano (10^-9) units of the amount. The value must be between -999,999,999 and +999,999,999 inclusive. If `units` is positive, `nanos` must be positive or zero. If `units` is zero, `nanos` can be positive, zero, or negative. If `units` is negative, `nanos` must be negative or zero. For example $-1.75 is represented as `units`=-1 and `nanos`=-750,000,000.", "format": "int32", "type": "integer"}, "units": {"description": "The whole units of the amount. For example if `currencyCode` is `\"USD\"`, then 1 unit is one US dollar.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleTypePostalAddress": {"description": "Represents a postal address. For example for postal delivery or payments addresses. Given a postal address, a postal service can deliver items to a premise, P.O. Box or similar. It is not intended to model geographical locations (roads, towns, mountains). In typical usage an address would be created by user input or from importing existing data, depending on the type of process. Advice on address input / editing: - Use an internationalization-ready address widget such as https://github.com/google/libaddressinput) - Users should not be presented with UI elements for input or editing of fields outside countries where that field is used. For more guidance on how to use this schema, see: https://support.google.com/business/answer/6397478", "id": "GoogleTypePostalAddress", "properties": {"addressLines": {"description": "Unstructured address lines describing the lower levels of an address. Because values in address_lines do not have type information and may sometimes contain multiple values in a single field (For example \"Austin, TX\"), it is important that the line order is clear. The order of address lines should be \"envelope order\" for the country/region of the address. In places where this can vary (For example Japan), address_language is used to make it explicit (For example \"ja\" for large-to-small ordering and \"ja-Latn\" or \"en\" for small-to-large). This way, the most specific line of an address can be selected based on the language. The minimum permitted structural representation of an address consists of a region_code with all remaining information placed in the address_lines. It would be possible to format such an address very approximately without geocoding, but no semantic reasoning could be made about any of the address components until it was at least partially resolved. Creating an address only containing a region_code and address_lines, and then geocoding is the recommended way to handle completely unstructured addresses (as opposed to guessing which parts of the address should be localities or administrative areas).", "items": {"type": "string"}, "type": "array"}, "administrativeArea": {"description": "Optional. Highest administrative subdivision which is used for postal addresses of a country or region. For example, this can be a state, a province, an oblast, or a prefecture. Specifically, for Spain this is the province and not the autonomous community (For example \"Barcelona\" and not \"Catalonia\"). Many countries don't use an administrative area in postal addresses. For example in Switzerland this should be left unpopulated.", "type": "string"}, "languageCode": {"description": "Optional. BCP-47 language code of the contents of this address (if known). This is often the UI language of the input form or is expected to match one of the languages used in the address' country/region, or their transliterated equivalents. This can affect formatting in certain countries, but is not critical to the correctness of the data and will never affect any validation or other non-formatting related operations. If this value is not known, it should be omitted (rather than specifying a possibly incorrect default). Examples: \"zh-Hant\", \"ja\", \"ja-Latn\", \"en\".", "type": "string"}, "locality": {"description": "Optional. Generally refers to the city/town portion of the address. Examples: US city, IT comune, UK post town. In regions of the world where localities are not well defined or do not fit into this structure well, leave locality empty and use address_lines.", "type": "string"}, "organization": {"description": "Optional. The name of the organization at the address.", "type": "string"}, "postalCode": {"description": "Optional. Postal code of the address. Not all countries use or require postal codes to be present, but where they are used, they may trigger additional validation with other parts of the address (For example state/zip validation in the U.S.A.).", "type": "string"}, "recipients": {"description": "Optional. The recipient at the address. This field may, under certain circumstances, contain multiline information. For example, it might contain \"care of\" information.", "items": {"type": "string"}, "type": "array"}, "regionCode": {"description": "Required. CLDR region code of the country/region of the address. This is never inferred and it is up to the user to ensure the value is correct. See https://cldr.unicode.org/ and https://www.unicode.org/cldr/charts/30/supplemental/territory_information.html for details. Example: \"CH\" for Switzerland.", "type": "string"}, "revision": {"description": "The schema revision of the `PostalAddress`. This must be set to 0, which is the latest revision. All new revisions **must** be backward compatible with old revisions.", "format": "int32", "type": "integer"}, "sortingCode": {"description": "Optional. Additional, country-specific, sorting code. This is not used in most regions. Where it is used, the value is either a string like \"CEDEX\", optionally followed by a number (For example \"CEDEX 7\"), or just a number alone, representing the \"sector code\" (Jamaica), \"delivery area indicator\" (Malawi) or \"post office indicator\" (For example Côte d'Ivoire).", "type": "string"}, "sublocality": {"description": "Optional. Sublocality of the address. For example, this can be neighborhoods, boroughs, districts.", "type": "string"}}, "type": "object"}, "GoogleTypeTimeZone": {"description": "Represents a time zone from the [IANA Time Zone Database](https://www.iana.org/time-zones).", "id": "GoogleTypeTimeZone", "properties": {"id": {"description": "IANA Time Zone Database time zone. For example \"America/New_York\".", "type": "string"}, "version": {"description": "Optional. IANA Time Zone Database version number. For example \"2019a\".", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Document AI Warehouse API", "version": "v1", "version_module": true}