#!/usr/bin/env python3
"""
Simple test to validate the enhanced report generation functionality
"""
import pandas as pd
import os

def test_aggregation_logic():
    """Test the core aggregation logic without importing the full service"""
    print("🧪 Testing Enhanced Report Generation Logic")
    print("=" * 50)
    
    # Sample GSC keywords data
    keywords_data = [
        {"page": "https://example.com/", "query": "example site", "clicks": 150, "impressions": 2000, "ctr": 0.075, "position": 3.2, "Month": "2024-01"},
        {"page": "https://example.com/", "query": "home page", "clicks": 89, "impressions": 1200, "ctr": 0.074, "position": 4.1, "Month": "2024-01"},
        {"page": "https://example.com/about/", "query": "about company", "clicks": 45, "impressions": 800, "ctr": 0.056, "position": 5.3, "Month": "2024-01"},
        {"page": "https://example.com/services/", "query": "professional services", "clicks": 78, "impressions": 1500, "ctr": 0.052, "position": 6.1, "Month": "2024-01"},
        {"page": "https://example.com/", "query": "example site", "clicks": 165, "impressions": 2200, "ctr": 0.075, "position": 3.0, "Month": "2024-02"},
        {"page": "https://example.com/about/", "query": "about company", "clicks": 52, "impressions": 900, "ctr": 0.058, "position": 5.1, "Month": "2024-02"},
    ]
    keywords_df = pd.DataFrame(keywords_data)
    
    print("1. Testing GSC data aggregation by URL...")
    print("Original keywords data:")
    print(keywords_df[['page', 'clicks', 'impressions', 'Month']].head())
    
    # Aggregate GSC data by URL (replicate the logic from ReportService)
    df = keywords_df.copy()
    
    # Standardize column names
    column_mapping = {
        'page': 'URL',
        'query': 'Keyword', 
        'clicks': 'Clicks',
        'impressions': 'Impressions',
        'ctr': 'CTR',
        'position': 'Position'
    }
    
    for old_col, new_col in column_mapping.items():
        if old_col in df.columns and new_col not in df.columns:
            df = df.rename(columns={old_col: new_col})
    
    # Aggregate by URL
    gsc_total_df = (
        df.groupby('URL', as_index=False)
          .agg({'Clicks': 'sum', 'Impressions': 'sum'})
    )
    
    # Calculate CTR
    gsc_total_df['CTR'] = gsc_total_df.apply(
        lambda row: (row['Clicks'] / row['Impressions']) if row['Impressions'] else 0,
        axis=1
    )
    
    # Calculate weighted average position
    pos_weight_all = (
        df.assign(weighted_pos=lambda df: df['Position'] * df['Impressions'])
          .groupby('URL', as_index=False)
          .agg({'weighted_pos': 'sum', 'Impressions': 'sum'})
    )
    pos_weight_all = pos_weight_all.rename(columns={'Impressions': 'IMP_sum'})
    
    gsc_total_df = gsc_total_df.merge(
        pos_weight_all[['URL', 'weighted_pos', 'IMP_sum']],
        on='URL',
        how='left'
    ).fillna({'weighted_pos': 0, 'IMP_sum': 1})
    
    gsc_total_df['Position'] = gsc_total_df.apply(
        lambda row: (row['weighted_pos'] / row['IMP_sum']) if row['IMP_sum'] else 0,
        axis=1
    )
    
    # Clean up and rename columns to match original format
    gsc_total_df = gsc_total_df.drop(columns=['weighted_pos', 'IMP_sum'])
    gsc_total_df = gsc_total_df.rename(columns={
        'Clicks': 'GSC Clicks',
        'Impressions': 'GSC Impressions'
    })
    
    print("\nAggregated GSC data by URL:")
    print(gsc_total_df)
    print(f"✅ GSC aggregation successful - {len(gsc_total_df)} URLs processed")
    
    print("\n2. Testing Historical Traffic aggregation...")
    
    # Create Historical-Traffic sheet by aggregating GSC data by URL and Month
    df = keywords_df.copy()
    
    # Standardize column names
    column_mapping = {
        'page': 'URL',
        'clicks': 'Clicks',
        'impressions': 'Impressions',
        'position': 'Position'
    }
    
    for old_col, new_col in column_mapping.items():
        if old_col in df.columns and new_col not in df.columns:
            df = df.rename(columns={old_col: new_col})
    
    # Aggregate by URL and Month
    hist_traffic_df = (
        df.groupby(['URL', 'Month'], as_index=False)
         .agg({'Clicks': 'sum', 'Impressions': 'sum'})
    )
    
    # Calculate CTR
    hist_traffic_df['CTR'] = hist_traffic_df.apply(
        lambda row: (row['Clicks'] / row['Impressions']) if row['Impressions'] else 0,
        axis=1
    )
    
    # Calculate weighted average position
    pos_weight = (
        df.assign(weighted_pos=lambda df: df['Position'] * df['Impressions'])
         .groupby(['URL', 'Month'], as_index=False)
         .agg({'weighted_pos': 'sum', 'Impressions': 'sum'})
    )
    pos_weight = pos_weight.rename(columns={'Impressions': 'IMP_sum'})
    
    hist_traffic_df = hist_traffic_df.merge(
        pos_weight[['URL', 'Month', 'weighted_pos', 'IMP_sum']],
        on=['URL', 'Month'],
        how='left'
    ).fillna({'weighted_pos': 0, 'IMP_sum': 1})
    
    hist_traffic_df['Position'] = hist_traffic_df.apply(
        lambda row: (row['weighted_pos'] / row['IMP_sum']) if row['IMP_sum'] else 0,
        axis=1
    )
    
    hist_traffic_df = hist_traffic_df.drop(columns=['weighted_pos', 'IMP_sum'])
    
    print("Historical Traffic data:")
    print(hist_traffic_df)
    print(f"✅ Historical traffic aggregation successful - {len(hist_traffic_df)} records")
    
    print("\n3. Testing GA data aggregation...")
    
    # Sample GA data
    ga_data = [
        {"pagePath": "/", "screenPageViews": 1250, "activeUsers": 890, "Month": "2024-01"},
        {"pagePath": "/about/", "screenPageViews": 456, "activeUsers": 320, "Month": "2024-01"},
        {"pagePath": "/services/", "screenPageViews": 678, "activeUsers": 445, "Month": "2024-01"},
        {"pagePath": "/", "screenPageViews": 1380, "activeUsers": 950, "Month": "2024-02"},
        {"pagePath": "/about/", "screenPageViews": 502, "activeUsers": 365, "Month": "2024-02"},
    ]
    ga_df = pd.DataFrame(ga_data)
    homepage = "https://example.com"
    
    print("Original GA data:")
    print(ga_df)
    
    # Aggregate GA data by URL
    df = ga_df.copy()
    
    # Keep only valid paths (those starting with "/")
    df = df[df['pagePath'].str.startswith('/')].copy()
    
    # Cast metrics to int BEFORE grouping
    df['screenPageViews'] = df['screenPageViews'].astype(int)
    df['activeUsers'] = df['activeUsers'].astype(int)
    
    # Build the full-URL column
    base = homepage.rstrip('/')
    df['URL'] = base + df['pagePath']
    
    # Aggregate per-URL for the "Data" sheet
    ga_total_df = (
        df.groupby('URL', as_index=False)
         .agg({'screenPageViews': 'sum', 'activeUsers': 'sum'})
         .rename(columns={
             'screenPageViews': 'Google Analytics Page Views',
             'activeUsers': 'Active Users'
         })
    )
    
    print("\nAggregated GA data by URL:")
    print(ga_total_df)
    print(f"✅ GA aggregation successful - {len(ga_total_df)} URLs processed")
    
    print("\n4. Testing comprehensive data merge...")
    
    # Sample crawl data
    crawl_data = [
        {
            'URL': 'https://example.com/',
            'Page Content': 'This is the main content of the home page with lots of useful information about our services.',
            'SEO Title': 'Home Page - Example Site',
            'Title Length': 26,
            'Meta Description': 'This is the home page of our example website',
            'H1': 'Welcome to Example Site',
            'Focus Keyword': '',
            'Page Type': '',
            'Topic': ''
        },
        {
            'URL': 'https://example.com/about/',
            'Page Content': 'We are a leading company in our industry with years of experience providing excellent services.',
            'SEO Title': 'About Us - Example Site',
            'Title Length': 24,
            'Meta Description': 'Learn more about our company and mission',
            'H1': 'About Our Company',
            'Focus Keyword': '',
            'Page Type': '',
            'Topic': ''
        },
        {
            'URL': 'https://example.com/services/',
            'Page Content': 'We offer a comprehensive range of professional services to meet all your business needs.',
            'SEO Title': 'Our Services - Example Site',
            'Title Length': 28,
            'Meta Description': 'Discover the range of services we offer',
            'H1': 'Our Professional Services',
            'Focus Keyword': '',
            'Page Type': '',
            'Topic': ''
        }
    ]
    crawl_df = pd.DataFrame(crawl_data)
    
    # Merge all data sources
    data_df = crawl_df.merge(gsc_total_df, on='URL', how='left')
    data_df = data_df.merge(ga_total_df, on='URL', how='left')
    
    # Fill NaNs with 0 for numeric metrics
    numeric_columns = ['GSC Clicks', 'GSC Impressions', 'Google Analytics Page Views', 'Active Users']
    for col in numeric_columns:
        if col in data_df.columns:
            data_df[col] = data_df[col].fillna(0)
            data_df[col] = data_df[col].astype(int)
    
    # Reorder columns to match original format
    data_columns = [
        'URL',
        'GSC Clicks',
        'GSC Impressions', 
        'Google Analytics Page Views',
        'Focus Keyword',
        'Page Type',
        'Topic',
        'Page Content',
        'SEO Title',
        'Title Length',
        'Meta Description',
        'H1'
    ]
    
    # Only include columns that exist
    data_df = data_df[[col for col in data_columns if col in data_df.columns]]
    
    print("Comprehensive Data sheet (merged):")
    print(data_df[['URL', 'GSC Clicks', 'GSC Impressions', 'Google Analytics Page Views', 'SEO Title']].head())
    print(f"✅ Data merge successful - {len(data_df)} pages with complete metrics")
    
    print("\n🎉 All aggregation tests passed!")
    print("\nKey improvements validated:")
    print("- ✅ GSC data properly aggregated by URL with weighted position calculation")
    print("- ✅ GA data properly aggregated by URL with full URL construction")
    print("- ✅ Historical traffic analysis by URL and Month")
    print("- ✅ Comprehensive data sheet merging all sources")
    print("- ✅ Proper column naming and ordering matching original format")
    
    return True

if __name__ == "__main__":
    success = test_aggregation_logic()
    print(f"\nTest result: {'✅ PASSED' if success else '❌ FAILED'}")
